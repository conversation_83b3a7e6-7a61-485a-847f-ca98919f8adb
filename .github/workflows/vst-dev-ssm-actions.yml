name: VST SSM DEV Deployment

on:
  workflow_dispatch:
    inputs:
      workflow_mode:
        description: 'Select workflow execution mode'
        required: true
        default: 'full_pipeline'
        type: choice
        options:
          - full_pipeline
          - tests_only
          - deploy_only

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    if: ${{ inputs.workflow_mode == 'full_pipeline' || inputs.workflow_mode == 'tests_only' }}
    outputs:
      test-status: ${{ steps.test-result.outputs.status }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: |
          cd ./e2e &&
          npm cache clean --force &&
          npm install --legacy-peer-deps
      
      - name: Install Playwright Browsers
        run: |
          cd ./e2e &&
          npx playwright install chromium --with-deps
    
      - name: Run E2E Tests
        id: run-tests
        run: |
          cd ./e2e &&
          ENV=dev npx playwright test --reporter=html,junit,json
        continue-on-error: true

      - name: Set Test Result Status
        id: test-result
        run: |
          if [ ${{ steps.run-tests.outcome }} == 'success' ]; then
            echo "status=passed" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

      - name: Upload Test Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-reports
          path: |
            e2e/playwright-report/
            e2e/test-results/
            e2e/results.xml
            e2e/results.json
            e2e/junit.xml
            e2e/**/*-results.json
            e2e/**/*-report.html
            e2e/**/*.xml
          retention-days: 30
          if-no-files-found: warn

      - name: Fail Job if Tests Failed
        if: steps.test-result.outputs.status == 'failed'
        run: |
          echo "E2E tests failed. Deployment will be skipped."
          exit 1

  deploy:
    name: Deploy to VST Dev
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: |
      always() &&
      (
        (inputs.workflow_mode == 'full_pipeline' && needs.e2e-tests.outputs.test-status == 'passed') ||
        inputs.workflow_mode == 'deploy_only'
      )
    
    permissions:
      id-token: write
      contents: read
    
    outputs:
      changes: ${{ steps.get-changes.outputs.changes }}
      deployment-status: ${{ steps.deploy-result.outputs.status }}
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '14'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/GitHubActionsArealyticsApplicationDeploymentRole
          role-session-name: vst-deployment-${{ github.run_number }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Get Deployment Changes
        id: get-changes
        run: |
          BRANCH_NAME="develop"
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      
      - name: Build Application
        run: |
          npm ci &&
          npm install sass --save-dev &&
          npm rebuild sass &&
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c dev &&
          cp -rvfap ./src/assets ./dist &&
          cd ./dist &&
          zip -r dist.zip ./*

      - name: Upload Build to S3
        run: |
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          DEPLOYMENT_KEY="vst-deployments/dist-${TIMESTAMP}-${{ github.run_number }}.zip"
          
          aws s3 cp ./dist/dist.zip s3://al-project-buildfiles/$DEPLOYMENT_KEY
          
          # Generate presigned URL valid for 1 hour
          DEPLOYMENT_URL=$(aws s3 presign s3://al-project-buildfiles/$DEPLOYMENT_KEY --expires-in 3600)
          
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "DEPLOYMENT_KEY=$DEPLOYMENT_KEY" >> $GITHUB_ENV

      - name: Verify SSM Connectivity
        run: |
          INSTANCE_STATUS=$(aws ssm describe-instance-information \
            --filters "Key=InstanceIds,Values=${{ secrets.EC2_INSTANCE_ID }}" \
            --query 'InstanceInformationList[0].PingStatus' \
            --output text)
          
          if [ "$INSTANCE_STATUS" != "Online" ]; then
            echo "❌ EC2 instance is not reachable via SSM. Status: $INSTANCE_STATUS"
            echo "Please check:"
            echo "1. SSM Agent is running on EC2"
            echo "2. IAM role is attached to EC2"
            echo "3. Internet connectivity from EC2"
            exit 1
          fi
          
          echo "✅ EC2 instance is online and reachable via SSM"

      - name: Deploy via SSM
        id: deploy
        run: |
          echo "🚀 Starting deployment to VST Dev environment..."
          COMMAND_ID=$(aws ssm send-command \
            --instance-ids "${{ secrets.EC2_INSTANCE_ID }}" \
            --document-name "Al-Apps-VST-Deploy-Document" \
            --parameters "deploymentUrl=$DEPLOYMENT_URL,deploymentPath=/var/www/vst-dev.arealytics.com.au,backupName=dist_backup_$(date +%Y%m%d_%H%M%S).zip" \
            --cloud-watch-output-config "CloudWatchLogGroupName=/aws/ssm/app-deployment/commands,CloudWatchOutputEnabled=true" \
            --query 'Command.CommandId' \
            --output text)
          
          echo "command_id=$COMMAND_ID" >> $GITHUB_OUTPUT
          echo "📋 Deployment command ID: $COMMAND_ID"

      - name: Monitor Deployment Progress
        id: monitor-deployment
        run: |
          COMMAND_ID="${{ steps.deploy.outputs.command_id }}"
          
          echo "⏳ Monitoring deployment progress..."
          
          while true; do
            RESULT=$(aws ssm get-command-invocation \
              --command-id "$COMMAND_ID" \
              --instance-id "${{ secrets.EC2_INSTANCE_ID }}" \
              --output json)
            
            STATUS=$(echo "$RESULT" | jq -r '.Status')
            
            echo "📊 Current status: $STATUS"
            
            case $STATUS in
              "InProgress")
                echo "⚙️  Deployment in progress..."
                sleep 15
                ;;
              "Success")
                echo "✅ Deployment completed successfully!"
                
                # Get deployment logs
                echo "📋 Deployment logs:"
                echo "$RESULT" | jq -r '.StandardOutputContent'
                
                break
                ;;
              "Failed"|"Cancelled"|"TimedOut")
                echo "❌ Deployment failed with status: $STATUS"
                
                # Get error details
                echo "🔍 Error details:"
                echo "$RESULT" | jq -r '.StandardErrorContent'
                echo "$RESULT" | jq -r '.StandardOutputContent'
                
                exit 1
                ;;
              *)
                echo "⚠️  Unknown status: $STATUS"
                sleep 10
                ;;
            esac
          done

      - name: Cleanup S3 Deployment File
        if: always()
        run: |
          if [ -n "$DEPLOYMENT_KEY" ]; then
            aws s3 rm s3://al-project-buildfiles/$DEPLOYMENT_KEY
            echo "🧹 Cleaned up temporary deployment file"
          fi

      - name: Set Deployment Result
        id: deploy-result
        if: always()
        run: |
          if [ ${{ steps.monitor-deployment.outcome }} == 'success' ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Post-Deployment Verification
        if: steps.deploy-result.outputs.status == 'success'
        run: |
          echo "🔍 Running post-deployment verification..."
          
          # Test if the website is accessible
          if curl -f -s -o /dev/null "https://vst-dev.arealytics.com.au"; then
            echo "✅ Website is accessible"
          else
            echo "⚠️  Website accessibility check failed"
          fi
  
  notify:
    name: Notify Slack
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Determine Notification Color
        id: notification-color
        run: |
          if [ "${{ needs.deploy.outputs.deployment-status }}" == "success" ]; then
            echo "color=good" >> $GITHUB_OUTPUT
            echo "emoji=✅" >> $GITHUB_OUTPUT
          else
            echo "color=danger" >> $GITHUB_OUTPUT
            echo "emoji=❌" >> $GITHUB_OUTPUT
          fi

      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ needs.deploy.result }}
          message: |
            ${{ steps.notification-color.outputs.emoji }} *VST DEV Deployment - Run #${{ github.run_number }}*
            
            *Started by:* ${{ github.actor }}
            *Branch:* ${{ github.ref_name }}
            *Deployment Status:* ${{ needs.deploy.outputs.deployment-status }}
            *Changes:* ${{ needs.deploy.outputs.changes }}
            
            *🌐 Application URL:* https://vst-dev.arealytics.com.au
            *📊 Workflow:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
