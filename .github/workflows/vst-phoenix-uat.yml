name: VST phoenix Uat Deployment

on:
  workflow_dispatch:
    inputs:
      workflow_mode:
        description: 'Select workflow execution mode'
        required: true
        default: 'full_pipeline'
        type: choice
        options:
          - full_pipeline
          - tests_only
          - deploy_only

permissions:
  id-token: write
  contents: read

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    if: ${{ inputs.workflow_mode == 'full_pipeline' || inputs.workflow_mode == 'tests_only' }}
    outputs:
      test-status: ${{ steps.test-result.outputs.status }}
      playwright-report-url: ${{ steps.get-report-url.outputs.url }}
      folder-name: ${{ steps.folder-name.outputs.folder-name }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: |
          cd ./e2e
          npm cache clean --force
          npm install --legacy-peer-deps

      - name: Install Playwright Browsers
        run: |
          cd ./e2e
          npx playwright install chromium --with-deps

      - name: Run E2E Tests
        id: run-tests
        run: |
          cd ./e2e
          ENV=dev npx playwright test --reporter=html
        continue-on-error: true

      - name: Ensure Playwright Report Directory Exists
        if: always()
        run: |
          cd ./e2e
          mkdir -p playwright-report
          if [ ! -f playwright-report/index.html ]; then
            echo "<html><body><h1>No Playwright Report Generated</h1><p>Check test execution logs for details.</p></body></html>" > playwright-report/index.html
          fi
          echo "Playwright report directory contents:"
          ls -la playwright-report/

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}
          role-session-name: GitHubActionsSession

      - name: Verify AWS Credentials
        if: always()
        run: |
          aws sts get-caller-identity || echo "Failed to authenticate with AWS. Check OIDC setup, role ARN, and region."

      - name: Determine Playwright Folder Name
        id: folder-name
        if: always()
        run: |
          RUN_ID="${{ github.run_id }}"
          echo "Checking for existing folders under run ID: $RUN_ID"
          EXISTING_COUNT=$(aws s3 ls s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/ | grep -c "playwright-report" || echo "0")
          FOLDER_NUMBER=$((EXISTING_COUNT + 1))
          FOLDER_NAME="playwright-report${FOLDER_NUMBER}"
          echo "Found $EXISTING_COUNT existing folders"
          echo "Using folder name: $FOLDER_NAME"
          echo "folder-name=$FOLDER_NAME" >> $GITHUB_OUTPUT

      - name: Upload Playwright Report Folder to S3
        if: always()
        run: |
          FOLDER_NAME="${{ steps.folder-name.outputs.folder-name }}"
          RUN_ID="${{ github.run_id }}"
          echo "Uploading Playwright report folder directly to S3..."
          echo "Source: ./e2e/playwright-report/"
          echo "Destination: s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/$FOLDER_NAME/"
          aws s3 sync ./e2e/playwright-report/ s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/$FOLDER_NAME/ \
            --delete \
            --exclude "*.zip"
          echo "✅ Successfully uploaded Playwright report folder to S3"
          echo "Verifying uploaded files:"
          aws s3 ls s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/$FOLDER_NAME/ --recursive

      - name: Generate Object URL for index.html
        id: get-report-url
        if: always()
        run: |
          FOLDER_NAME="${{ steps.folder-name.outputs.folder-name }}"
          RUN_ID="${{ github.run_id }}"
          INDEX_URL=$(aws s3 presign s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/$FOLDER_NAME/index.html --expires-in 86400)
          echo "Generated pre-signed URL for index.html: $INDEX_URL"
          echo "url=$INDEX_URL" >> $GITHUB_OUTPUT
          if aws s3 ls s3://${{ secrets.AWS_S3_BUCKET }}/$RUN_ID/$FOLDER_NAME/index.html; then
            echo "✅ index.html confirmed to exist in S3"
          else
            echo "❌ Warning: index.html not found in S3"
          fi

      - name: Upload Test Reports as Artifact
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-reports
          path: |
            e2e/test-results/
            e2e/playwright-report/
            reports/
            cypress/screenshots/
            cypress/videos/
            **/*test-report*
            **/*junit*.xml
            **/*allure*
          retention-days: 30

      - name: Set Test Result Status
        id: test-result
        run: |
          if [ ${{ steps.run-tests.outcome }} == 'success' ]; then
            echo "status=passed" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

      - name: Fail Job if Tests Failed
        if: steps.test-result.outputs.status == 'failed'
        run: |
          echo "E2E tests failed. Deployment will be skipped."
          exit 1

  deploy:
    name: Deploy to VST Phoenix Uat
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: |
      always() &&
      (
        (inputs.workflow_mode == 'full_pipeline' && needs.e2e-tests.outputs.test-status == 'passed') ||
        inputs.workflow_mode == 'deploy_only'
      )
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '14'

      - name: Get current deployment changes
        id: get-changes
        run: |
          BRANCH_NAME="develop"
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Build project
        run: |
          cd ./
          npm ci &&
          npm install sass --save-dev &&
          npm rebuild sass &&
          node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build -c phoenixuat
          cp -rvfap ./src/assets ./dist
          cd ./dist
          zip -r dist.zip ./*
        continue-on-error: true

      - name: Copy files via ssh rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr
          RSYNC_SOURCE: /dist/dist.zip
          RSYNC_TARGET: /home/<USER>/
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PHOENIX_REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.PHOENIX_REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.PHOENIX_USER }}

      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PHOENIX_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.PHOENIX_HOSTNAME }}
          REMOTE_USER: ${{ secrets.PHOENIX_USER }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
            cd /var/www/vst-phoenix-uat.arealytics.com.au/
            sudo unzip -o -d "/var/www/vst-phoenix-uat.arealytics.com.au/" /home/<USER>/dist.zip
            sudo chown -R ubuntu:Ubuntu /var/www/vst-phoenix-uat.arealytics.com.au
            sudo rm -rf /home/<USER>/dist.zip
          '

  notify:
    name: Notify Slack for Deployment
    runs-on: ubuntu-latest
    needs: [deploy, e2e-tests]
    if: always()
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get current deployment changes
        id: get-changes
        run: |
          BRANCH_NAME="develop"
          CHANGES=$(git log -1 --pretty=format:"%s")
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          message: |
            *${{ github.repository }} - #${{ github.run_number }}*
            Started by user ${{ github.actor }}
            *Deployment Status:* ${{ needs.deploy.result }}
            *Test Status:* ${{ needs.e2e-tests.outputs.test-status == 'passed' && '✅ Tests Passed' || '❌ Tests Failed' }}
            *Changes in Current Deployment:* ${{ steps.get-changes.outputs.changes }}
            *Deployment URL:* https://vst-phoenix-uat.arealytics.com.au
            *Test Report URL:* ${{ needs.e2e-tests.outputs.playwright-report-url || 'No report available' }}
            *Report Folder:* ${{ needs.e2e-tests.outputs.folder-name || 'N/A' }}
            
            Note: Test report URL is valid for 24 hours
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
