import { faker } from '@faker-js/faker';

export const propertyData = {
    pid: '672654',
    postalCode: '3000'
}

export const officeDetails = {
  officeUsePid : '733618',
  smallestFloor: faker.number.int({ min: 1, max: 5 }).toString(), // e.g. '1.00'
  largestFloor: faker.number.int({ min: 6, max: 20 }).toString(),
  sourceSF: faker.number.int({ min: 10, max: 100 }).toString(),
  yearBuilt: faker.date.past({ years: 30 }).getFullYear().toString(), // e.g. '1999'
  yearRenovated: faker.date.past({ years: 10 }).getFullYear().toString(), // e.g. '2002'
  gresbScoreMin: faker.number.int({ min: 1, max: 5 }).toString(),
  gresbScoreMax: faker.number.int({ min: 6, max: 10 }).toString(),
  comments: faker.lorem.sentence(),
  buildingWebsite: faker.internet.url(),
  reservedParking: faker.number.int({ min: 1, max: 10 }).toString(),
  reservedParkingRate: faker.number.int({ min: 10, max: 50 }).toString(),
  unreservedParking: faker.number.int({ min: 1, max: 10 }).toString(),
  unreservedParkingRate: faker.number.int({ min: 10, max: 50 }).toString(),
  passengerElevator: faker.number.int({ min: 1, max: 10 }).toString(),
  freightElevator: faker.number.int({ min: 1, max: 5 }).toString(),
  parkingElevator: faker.number.int({ min: 0, max: 3 }).toString(),
  vacancy: faker.number.int({ min: 0, max: 10 }).toString(),
  outgoings: faker.number.int({ min: 100, max: 1000 }).toString(),
  bookValue: faker.number.int({ min: 1, max: 50 }).toString(),
}

export const additionalAddress = {
    propertyId: '672654',
    minFloor: faker.number.int({ min: 1, max: 5 }).toString(),
    maxFloor: faker.number.int({ min: 6, max: 20 }).toString(),
    streetName: faker.location.street()
}

export const copyPolygon = {
    notStrataPropertyId: '672654',
    description:`A ${faker.commerce.productAdjective()} property located in ${faker.location.city()} with ${faker.number.int({ min: 1, max: 5 })} bedrooms.`
}

export const parcel = {
  propertyId : '733618',
  parcelNo: `${faker.number.int({ min: 100, max: 999 })}/RP${faker.number.int({ min: 10000, max: 99999 })}`,
  parcelSize: faker.number.float({ min: 100, max: 1000 }).toFixed(2),
  parcelLot: faker.number.int({ min: 1, max: 50 }).toString(),
  parcelBlock: faker.number.int({ min: 1, max: 10 }).toString(),
  parcelSubDivision: faker.number.int({ min: 1, max: 5 }).toString()
}

export const notes = {
  propertyId: '182153',
  title: `Update: ${faker.hacker.verb()} ${faker.hacker.noun()}`, // e.g., "Update: Override interface"
  description: `${faker.person.firstName()} ${faker.person.lastName()} ${faker.word.verb()} the site on ${faker.date.recent().toLocaleDateString()}. ${faker.lorem.sentence()}`, 
};

export const media = {
  propertyId: '672654'
}

export const formSearch = {
  propertyId : '749887',
  postalCode: '4000',
  masterStrataPid: '113436',
  masterFreeholdPid: '113515'
}
