// Get the current environment from process.env.ENV, default to 'dev'
// @ts-ignore
const environment = process.env.ENV || 'dev';

// Define credentials for different environments
const credentials = {
  local: {
    username: '<EMAIL>',
    password: 'emp123!'
  },
  dev: {
    username: '<EMAIL>',
    password: 'emp123!'
  },
  uat: {
    username: '<EMAIL>',
    password: 'empcm123!'
  },
  prod: {
    username: 'chandra<PERSON><PERSON>@zessta.com',
    password: '<PERSON><PERSON>@666'
  }
};

// Export the credentials for the current environment
export const login = credentials[environment] || credentials.dev;

