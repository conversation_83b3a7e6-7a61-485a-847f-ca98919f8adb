import { Page, expect } from '@playwright/test';
import PropertySearch from './property-search';
import { media } from '../fixtures/property-test-data';
import path from 'path';
import { CommonStrings } from '../../src/app/constants';

export default class Media {
    private page: Page;
    private propertySearch: PropertySearch;

    constructor(page: Page) {
        this.page = page;
        this.propertySearch = new PropertySearch(this.page);
    }

    async addNewMedia() {
        await this.propertySearch.propertySearchThroughMapSearch(media.propertyId);

        // Navigate to Media tab
        await this.page.getByRole('tab', { name: 'Media' }).click();
        await this.page.waitForTimeout(1000); // for transition
        const mediaSelector = await this.page.locator('[data-testid="media-gallery"]');
        const mediaCountBefore = await mediaSelector.count();

        // Upload file
        await this.page.getByTestId('media-add').locator('label').click();
        const filePath = path.join(__dirname, '../assets/test-images/map-image.jpg');
        await this.page.setInputFiles('input[type="file"]', filePath);

        // Set ownership and upload
        await this.page.getByTestId('media-ownership').click();
        await this.page.getByRole('option', { name: 'Yes' }).click();
        await this.page.getByTestId('media-upload').click();

        // Verify success message and updated media count
        await expect(this.page.getByText(CommonStrings.SuccessMessages.MediaSavedSuccessfully, { exact: true })).toBeVisible({ timeout: 5000 });
        await expect(mediaSelector).toHaveCount(mediaCountBefore + 1, { timeout: 5000 });

        await this.deleteMedia(mediaCountBefore);
    }

    async deleteMedia(expectedCountAfterDelete: number) {
        const deleteButton = this.page.getByTestId('media-delete').last();
        await deleteButton.click();

        const confirmButton = this.page.getByRole('button', { name: 'Yes' });
        await confirmButton.click();

        const mediaGallery = this.page.getByTestId('media-gallery');
        await expect(mediaGallery).toHaveCount(expectedCountAfterDelete, { timeout: 5000 });
    }

    async setFirstMediaAsDefault() {
        // Search for the property
        await this.propertySearch.propertySearchThroughMapSearch(media.propertyId);

        // Navigate to the Media tab
        await this.page.getByRole('tab', { name: 'Media' }).click();
        await this.page.waitForTimeout(1000); // Allow for transition

        // Get the first media gallery item
        const firstMediaItem = this.page.getByTestId('media-gallery').first();

        // Capture existing media information (e.g., name, date, etc.)
        const initialMediaInfo = await firstMediaItem.allTextContents();

        // Click the 'not default' star to set as default
        await firstMediaItem.getByTestId('media-not-default').click();
        await this.page.getByRole('button', { name: 'Yes' }).click();
        await this.page.waitForTimeout(1000);   // Wait for DOM update

        // Find which media item is now marked as default
        const totalMediaItems = await this.page.getByTestId('media-gallery').count();
        let currentDefaultMediaInfo: string[] = [];
        let currentDefaultIndex = -1;

        for (let i = 0; i < totalMediaItems; i++) {
            const mediaItem = this.page.getByTestId('media-gallery').nth(i);
            const isDefault = await mediaItem.getByTestId('media-default').isVisible().catch(() => false);

            if (isDefault) {
                currentDefaultMediaInfo = await mediaItem.allTextContents();
                currentDefaultIndex = i;
                break;
            }
        }
        // Validate that the default media is the one we selected
        await expect(currentDefaultMediaInfo).toStrictEqual(initialMediaInfo);

        // Reset media back to not default for cleanup
        await this.resetMediaToNotDefault(this.page.getByTestId('media-gallery').nth(currentDefaultIndex));
    }

    async resetMediaToNotDefault(mediaItem) {
        // Click edit on the selected media item
        await mediaItem.getByTestId('media-edit').click();

        // Uncheck 'Is Default' checkbox
        await this.page.getByTestId('media-isdefault').uncheck();
        await this.page.getByTestId('media-save').click();
        await this.page.waitForTimeout(1000); // Allow time for the UI update
        
        // Validate that the media is no longer default
        const isDefaultVisible = await mediaItem.getByTestId('media-default').isVisible().catch(() => false);
        await expect(isDefaultVisible).toBeFalsy();
    }

}
