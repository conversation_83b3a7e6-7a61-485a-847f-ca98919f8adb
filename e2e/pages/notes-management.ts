import { Page, expect } from '@playwright/test';
import PropertySearch from './property-search';
import { notes } from '../fixtures/property-test-data';

export default class Notes {
    private page: Page;
    private propertySearch: PropertySearch;

    constructor(page: Page) {
        this.page = page;
        this.propertySearch = new PropertySearch(this.page);
    }

    async addNotes() {
        // Navigate to the property and open the Notes tab
        await this.propertySearch.propertySearchThroughMapSearch(notes.propertyId);
        // Click on the "Notes" tab
        await this.page.getByRole('tab', { name: 'Notes' }).click();
        // Wait for the Notes tab to fully load
        await this.page.waitForSelector('[data-testid="notes-number"]');

        // Get count of existing notes before adding a new one
        const notesSelector = this.page.locator('[data-testid="notes-number"]');
        const notesCountBefore = await notesSelector.count();

        // Open add-note form and fill fields
        await this.page.getByTestId('notes-add').click();
        await this.page.getByTestId('notes-notes-type').getByRole('combobox').click();
        await this.page.getByRole('option', { name: 'Note', exact: true }).click();
        await this.page.getByTestId('notes-title').fill(notes.title);
        await this.page.getByTestId('notes-description').fill(notes.description);
        await this.page.getByTestId('notes-save').click();

        // Validate note count increased by 1
        await expect(this.page.getByTestId('notes-number')).toHaveCount(notesCountBefore + 1, { timeout: 5000 });

        // Verify newly added note is present at the bottom
        const latestNoteCard = this.page.locator('[data-testid="notes-card"]').last();
        await expect(latestNoteCard.getByRole('heading', { name: notes.title })).toBeVisible();
        await expect(latestNoteCard.getByText(notes.description)).toBeVisible();

        // Edit the newly added note
        await this.editNotes(latestNoteCard);

        // Delete the note to clean up
        await this.deleteNotes(notesCountBefore, notes.title);
    }

    async editNotes(latestNoteCard) {
        // Click edit button on the note card
        await latestNoteCard.getByTestId('note-edit').click();

        // Refill updated note data
        await this.page.getByTestId('notes-title').fill(notes.title);
        await this.page.getByTestId('notes-description').fill(notes.description);
        await this.page.getByTestId('notes-save').click();

        // Assert updated data is visible
        await expect(latestNoteCard.getByRole('heading', { name: notes.title })).toBeVisible();
        await expect(latestNoteCard.getByText(notes.description)).toBeVisible();
    }

    async deleteNotes(expectedCountAfterDelete: number, deletedTitle: string) {
        // Locate the note card by heading/title
        const noteCard = this.page.locator('[data-testid="notes-card"]').filter({
            has: this.page.getByRole('heading', { name: deletedTitle }),
        });

        // Ensure it's visible before deleting
        await expect(noteCard).toBeVisible();
        await noteCard.getByTestId('note-delete').click();

        // Confirm deletion in modal
        await this.page.getByRole('button', { name: 'Yes' }).click();

        // Validate that note count is back to expected count after delete
        await expect(this.page.getByTestId('notes-number')).toHaveCount(expectedCountAfterDelete);

        // Verify deleted note title no longer exists
        await expect(this.page.getByRole('heading', { name: deletedTitle })).toHaveCount(0);
    }

}
