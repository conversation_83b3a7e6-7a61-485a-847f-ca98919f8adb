import { expect, Page } from '@playwright/test';
import PropertySearch from './property-search';
import { parcel } from '../fixtures/property-test-data';
import { CommonStrings } from '../../src/app/constants'

export default class ParcelTest {
  private page: Page;
  private propertySearch: PropertySearch;

  constructor(page: Page) {
    this.page = page;
    this.propertySearch = new PropertySearch(this.page);
  }

  async enterParcel() {
    // Navigate to property and open parcel section
    await this.propertySearch.propertySearchThroughMapSearch(parcel.propertyId);
    await this.page.getByTestId('parcel-toggle').click();

    // Capture the current number of rows
    const tableBody = this.page.locator('#ParcelInformation table tbody');
    const beforeRowCount = await tableBody.locator('tr').count();

    // Fill in parcel form and save
    await this.page.getByTestId('enter-parcel').click();
    await this.page.getByTestId('parcel-parcel-no').fill(parcel.parcelNo);
    await this.page.getByTestId('parcel-sub-division').fill(parcel.parcelSubDivision);
    await this.page.getByTestId('parcel-parcel-size').fill(parcel.parcelSize);
    await this.page.getByTestId('parcel-lot').fill(parcel.parcelLot);
    await this.page.getByTestId('parcel-block').fill(parcel.parcelBlock);
    await this.page.getByTestId('parcel-enter-save').click();

    // Wait and verify that a new row is added
    await expect(tableBody.locator('tr')).toHaveCount(beforeRowCount + 1, { timeout: 5000 });

    // Find index of newly inserted parcel row
    const insertedRowIndex = await this.findParcelRowIndex();
    if (insertedRowIndex === -1) {
      throw new Error(`Inserted parcel with parcelNo "${parcel.parcelNo}" not found or doesn't match.`);
    }

    await expect(this.page.getByText(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull, { exact: true })).toBeVisible({ timeout: 5000 });
    await this.page.waitForTimeout(2000); //ui stable
    await this.validateAggregateParcelSize();
    // Edit the inserted parcel
    await this.page.waitForTimeout(2000);
    await this.editParcel(insertedRowIndex);
    // Delete the inserted parcel
    await this.page.waitForTimeout(2000);
    await this.deleteParcelByParcelNo(insertedRowIndex);
  }

  // Find the row index that matches the inserted parcel data
  async findParcelRowIndex(): Promise<number> {
    const tableBody = this.page.locator('#ParcelInformation table tbody');
    const rows = tableBody.locator('tr');
    const rowCount = await rows.count();

    // Iterate over each row and compare text content
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const text = (await row.textContent())?.toLowerCase() ?? '';
      if (
        text.includes(parcel.parcelNo.trim().toLowerCase()) &&
        text.includes(parcel.parcelSubDivision.trim().toLowerCase()) &&
        text.includes(parcel.parcelLot.trim().toLowerCase()) &&
        text.includes(parcel.parcelBlock.trim().toLowerCase())
      ) {
        return i;
      }
    }
    return -1; // Not found
  }

  // Edit the parcel data in the given row
  async editParcel(rowIndex: number) {
    const tableBody = this.page.locator('#ParcelInformation table tbody');
    const row = tableBody.locator('tr').nth(rowIndex);

    // Click edit on the row and fill the updated values
    await row.scrollIntoViewIfNeeded();
    await row.getByTestId('parcel-edit').click();

    await this.page.getByTestId('parcel-sub-division').waitFor({ state: 'visible' });
    await this.page.getByTestId('parcel-sub-division').fill(parcel.parcelSubDivision);
    await this.page.getByTestId('parcel-lot').fill(parcel.parcelLot);
    await this.page.getByTestId('parcel-block').fill(parcel.parcelBlock);
    await this.page.getByTestId('parcel-enter-save').click();

    await expect(this.page.getByText(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull, { exact: true }))
      .toBeVisible({ timeout: 5000 });

    // Validate that the row still contains the updated values
    const updatedRow = await tableBody.locator('tr').nth(rowIndex);
    const text = (await updatedRow.textContent())?.toLowerCase() ?? '';
    if (
      !text.includes(parcel.parcelNo.toLowerCase()) ||
      !text.includes(parcel.parcelSubDivision.toLowerCase()) ||
      !text.includes(parcel.parcelLot.toLowerCase()) ||
      !text.includes(parcel.parcelBlock.toLowerCase())
    ) {
      throw new Error('Parcel data not updated correctly in the table.');
    }
  }

  // Delete parcel at the given row index
  async deleteParcelByParcelNo(rowIndex: number) {
    const tableBody = this.page.locator('#ParcelInformation table tbody');
    const rows = tableBody.locator('tr');
    const rowCount = await rows.count();

    // Click edit and then delete on the specified row
    const row = rows.nth(rowIndex);
    await row.scrollIntoViewIfNeeded();
    await row.getByTestId('parcel-edit').click();
    await this.page.getByTestId('parcel-delete').click();

    // Wait for confirmation toast or dialog
    await expect(this.page.getByText(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull, { exact: true }))
      .toBeVisible({ timeout: 5000 });

    const confirmBtn = this.page.getByTestId('confirm-delete');
    if (await confirmBtn.isVisible()) {
      await confirmBtn.click();
    }

    // Verify row count has decreased by 1
    await expect(tableBody.locator('tr')).toHaveCount(rowCount - 1, { timeout: 5000 });

    // Final validation to ensure parcelNo no longer exists
    const updatedCount = await rows.count();
    for (let i = 0; i < updatedCount; i++) {
      const text = await rows.nth(i).textContent();
      if (text?.toLowerCase().includes(parcel.parcelNo.toLowerCase())) {
        throw new Error(`Parcel with parcelNo "${parcel.parcelNo}" still exists after deletion.`);
      }
    }
    //validate the aggregate after deletion
    await this.page.waitForTimeout(2000); //transistion
    await this.validateAggregateParcelSize();
  }

  async validateAggregateParcelSize() {
    await expect(this.page.locator('#ParcelInformation table tbody tr td').first()).toBeVisible({ timeout: 5000 });
    const parcelTable = this.page.locator('#ParcelInformation');

    // Get table headers and find the index of "Parcel Size" column
    const tableHeaders = parcelTable.locator('table thead tr th');
    const headerCount = await tableHeaders.count();

    let parcelSizeColumnIndex = -1;
    for (let i = 0; i < headerCount; i++) {
      const headerText = (await tableHeaders.nth(i).textContent())?.trim();
      const normalizedHeader = headerText?.replace(/\s+/g, '').toLowerCase();
      if (normalizedHeader === 'parcelsize') {
        parcelSizeColumnIndex = i;
        break;
      }
    }

    if (parcelSizeColumnIndex === -1) {
      throw new Error('Parcel Size column not found in the table headers.');
    }

    // Iterate through all rows and sum up parcel sizes
    const tableRows = parcelTable.locator('tbody tr');
    const rowCount = await tableRows.count();
    let calculatedParcelTotal = 0;

    for (let i = 0; i < rowCount; i++) {
      const sizeCell = tableRows.nth(i).locator('td').nth(parcelSizeColumnIndex);
      const cellText = (await sizeCell.textContent())?.trim().replace(/,/g, '');
      const parcelSize = Number(cellText);

      if (!isNaN(parcelSize)) {
        calculatedParcelTotal += parcelSize;
      } else {
        console.warn(`Invalid parcel size in row ${i + 1}: "${cellText}"`);
      }
    }

    // Get the aggregate parcel size from the UI input
    const aggregateInput = this.page.getByTestId('property-parcel-aggregate');
    const inputValue = await aggregateInput.inputValue();
    const displayedAggregateSize = Number(parseFloat(inputValue?.trim().replace(/,/g, '')).toFixed(2));

    // Round the calculated total for comparison
    const expectedAggregateSize = Number(calculatedParcelTotal.toFixed(2));

    // Compare values with exact precision (2 decimal places)
    await expect(expectedAggregateSize).toBe(displayedAggregateSize);
  }

}
