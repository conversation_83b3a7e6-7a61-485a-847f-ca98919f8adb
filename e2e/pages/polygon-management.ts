import { expect, Page } from '@playwright/test';
import PropertySearch from './property-search';
import { copyPolygon } from '../fixtures/property-test-data';

export default class PolygonManagement {
    private page: Page;
    private propertySearch: PropertySearch;

    constructor(page: Page) {
        this.page = page;
        this.propertySearch = new PropertySearch(this.page);
    }

    private labelClasses = [
        'blueCheckedLabel',
        'redCheckedLabel',
        'orangeCheckedLabel',
        'brownCheckedLabel',
        'magentaCheckedLabel',
        'greenCheckedLabel',
    ];

    async validateCopyPolygon() {
        await this.propertySearch.propertySearchThroughMapSearch(copyPolygon.notStrataPropertyId);

        const buildingSqm = Number(await this.page.getByTestId('property-default-building-size').inputValue());

        await this.page.locator('.fa-solid.fa-draw-polygon').click();
        await this.page.waitForTimeout(2000);

        const beforeCopyPolygon = await this.page.locator('[data-testid="property-floors-section-count"]').count();

        await this.page.getByTestId('property-copy-polygon').first().click();

        let matchedClass = '';

        const officeLabel = this.page.getByTestId('copy-polygon-to-property-use-5')
        const officeClass = await officeLabel.getAttribute('class');

        if (officeClass?.includes('radio-button-disabled')) {
            // If Office is disabled, click Industrial
            await this.page.getByTestId('copy-polygon-to-property-use-3').nth(1).click();
            matchedClass = 'redCheckedLabel';
            await this.page.getByTestId('copy-poygon-to-additional-use').locator('span').first().click();
            await this.page.getByRole('option', { name: 'O' }).click();
        } else {
            // Otherwise, click Office
            await officeLabel.click();
            matchedClass = 'blueCheckedLabel';
            await this.page.getByTestId('copy-poygon-to-additional-use').locator('span').first().click();
            await this.page.getByRole('option', { name: 'I' }).click();
        }

        // Fill other required fields

        await this.page.getByTestId('copy-polygon-to-min-floor').getByRole('combobox').click();
        await this.page.getByRole('option', { name: 'G' }).click();

        await this.page.getByTestId('copy-polygon-to-max-floor').getByRole('combobox').click();
        await this.page.getByRole('option', { name: 'G' }).click();

        await this.page.getByTestId('copy-polygon-to-description').fill(copyPolygon.description);

        const floorSize = Number(await this.page.getByTestId('copy-polygon-floor-size').inputValue());
        const floorCount = Number(await this.page.getByTestId('copy-polygon-floor-count').inputValue());

        // Submit
        await this.page.getByTestId('copy-polygon-add').click();
        await this.page.getByTestId('property-floors-count').isVisible({ timeout: 5000 });

        // Validate count
        const expectedCount = beforeCopyPolygon + 1;
        await expect(this.page.locator('[data-testid="property-floors-section-count"]')).toHaveCount(expectedCount);

        // Validate building size updated correctly
        const buildingSizeAfterCopy = Number(await this.page.getByTestId('property-default-building-size').inputValue());
        await expect(buildingSizeAfterCopy).toBe(buildingSqm + (floorSize * floorCount));

        await this.validateBuildingSizeAfterFloorDeletion(floorSize, matchedClass, buildingSizeAfterCopy);

        await this.validatePolygonBoundsAndBuildingSizeUpdate();
    }

    // Validate building size after deleting floor sections matching size & use type
    async validateBuildingSizeAfterFloorDeletion(floorSizeToDelete: number, matchedClass: string, initialBuildingSize: number) {
        let totalDeletedFloorSize = 0;

        // Get initial floor section count
        let floorSectionCount = await this.page.locator('[data-testid="property-floors-section-count"]').count();

        for (let i = 0; i < floorSectionCount; i++) {
            const floorSection = this.page.locator(`[data-testid="property-floors-section-count-${i}"]`);

            // Get all use type labels within the floor section
            const useTypeLabels = floorSection.getByTestId('property-floor-property-use');
            const labelCount = await useTypeLabels.count();

            let selectedClass = '';

            // Find the label with "CheckedLabel" to determine selected use type
            for (let j = 0; j < labelCount; j++) {
                const labelEl = useTypeLabels.nth(j);
                const classAttr = await labelEl.getAttribute('class');

                if (classAttr?.includes('CheckedLabel')) {
                    selectedClass = classAttr
                        .split(' ')
                        .find(cls => this.labelClasses.includes(cls)) || '';
                    break;
                }
            }

            // Get floor size and floor count
            const floorSize = Number(await floorSection.getByTestId('property-floor-size').inputValue());
            const floorCount = Number(await floorSection.getByTestId('property-floor-count').inputValue());

            // If floor matches size & use type, delete and update total deleted floor size
            if (selectedClass === matchedClass && floorSize === floorSizeToDelete) {
                await floorSection.getByTestId('floor-delete').click();
                await this.page.getByRole('button', { name: 'Ok' }).click();

                totalDeletedFloorSize += floorSize * floorCount;

                // Refresh floor section count after deletion
                await this.page.waitForTimeout(500);
                floorSectionCount = await this.page.locator('[data-testid="property-floors-section-count"]').count();
                i = -1; // Reset loop to avoid stale locators
            }
        }

        // Validate building size after deletion
        const updatedBuildingSize = Number(await this.page.getByTestId('property-default-building-size').inputValue());
        const expectedBuildingSize = initialBuildingSize - totalDeletedFloorSize;

        await expect(updatedBuildingSize.toFixed(2)).toBe(expectedBuildingSize.toFixed(2));
    }

    // Validate building size after increasing floor counts of floor sections
    async validatePolygonBoundsAndBuildingSizeUpdate() {
        const buildingSqmBeforeUpdate = Number(await this.page.getByTestId('property-default-building-size').inputValue());
        const totalFloors = Number(await this.page.getByTestId('property-floors-count').inputValue());

        const floorSectionCount = await this.page.locator('[data-testid="property-floors-section-count"]').count();

        let floorSizeToUpdate = 0;
        let floorsAdded = 0;

        // Iterate floor sections to find one that can be updated
        for (let i = 0; i < floorSectionCount; i++) {
            const floorSection = this.page.locator(`[data-testid="property-floors-section-count-${i}"]`);

            const floorCount = Number(await floorSection.getByTestId('property-floor-count').inputValue());
            const floorSize = Number(await floorSection.getByTestId('property-floor-size').inputValue());

            // If floor count is less than total floors, increase it to max
            if (floorCount < totalFloors) {
                const floorsToAdd = totalFloors - floorCount;

                await floorSection.getByTestId('property-floor-max').getByRole('combobox').fill(String(totalFloors-1));
                await this.page.getByRole('option').first().click();

                floorSizeToUpdate = floorSize;
                floorsAdded = floorsToAdd;

                break; // Stop after first applicable update
            }
        }

        if (floorsAdded === 0) {
            console.log("No floor sections required updating.");
            return;
        }

        await this.page.waitForTimeout(1000); // Allow DOM update

        // Validate building size reflects the floor count update
        const buildingSqmAfterUpdate = Number(await this.page.getByTestId('property-default-building-size').inputValue());
        const expectedBuildingSize = buildingSqmBeforeUpdate + (floorSizeToUpdate * floorsAdded);

        await expect(buildingSqmAfterUpdate.toFixed(2)).toBe(expectedBuildingSize.toFixed(2));
    }

}

