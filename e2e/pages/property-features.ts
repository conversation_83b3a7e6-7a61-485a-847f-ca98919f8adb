import { Page, expect } from '@playwright/test';
import { additionalAddress } from '../fixtures/property-test-data';
import PropertySearch from './property-search'

export default class PropertyFeatures {
    private page: Page;
    private propertySearch: PropertySearch;

    constructor(page: Page) {
        this.page = page;
        this.propertySearch = new PropertySearch(this.page);
    }

    async addAdditionalAddress() {
        const { page } = this;

        await this.propertySearch.propertySearchThroughMapSearch(additionalAddress.propertyId);


        // Wait for the additional address table to load
        await page.waitForSelector('#AdditionalAddress table tbody');
        await page.waitForFunction(() => {
            const tbody = document.querySelector('#AdditionalAddress table tbody');
            return tbody !== null;
        });

        // Count current rows before adding
        const tableBody = page.locator('#AdditionalAddress table tbody');
        const beforeRowCount = await tableBody.locator('tr').count();

        // Scroll down and click on "New Address"
        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
        await page.getByRole('button', { name: 'New Address' }).click();

        // Fill in new address details in modal
        await page.getByTestId('additional-address-street-min').fill(additionalAddress.minFloor);
        await page.getByTestId('additional-address-street-max').fill(additionalAddress.maxFloor);
        await page.getByTestId('additional-address-street-name').fill(additionalAddress.streetName);

        // Click Save
        await page.getByTestId('additional-address-save').click();

        // Verify that row count increased by 1
        await expect(tableBody.locator('tr')).toHaveCount(beforeRowCount + 1, { timeout: 5000 });

        // Wait for the table row count to increase (use longer timeout)
        await expect(tableBody.locator('tr')).toHaveCount(beforeRowCount + 1, { timeout: 5000 });

        // Remove the newly added address
        await this.removeAdditionalAddress(beforeRowCount + 1);

    }

    async removeAdditionalAddress(afterRowCount: number) {
        const { page } = this;
        const tableBody = page.locator('#AdditionalAddress table tbody');
        await tableBody.scrollIntoViewIfNeeded();

        // Get the last added row and its delete icon
        const rowLocator = tableBody.locator('tr').nth(afterRowCount - 1);
        await rowLocator.scrollIntoViewIfNeeded();
        const deleteIcon = rowLocator.getByTestId('additional-address-delete');

        // Click delete and confirm
        await deleteIcon.click();
        await page.getByRole('button', { name: 'Yes' }).click();

        // Verify row count decreased by 1
        await expect(tableBody.locator('tr')).toHaveCount(afterRowCount - 1, { timeout: 5000 });
    }

}
