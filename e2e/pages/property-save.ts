import { Page, expect } from '@playwright/test';
import PropertySearch from '../pages/property-search'
import { officeDetails } from '../fixtures/property-test-data';
import { getNumericValue, selectDropdownOption } from '../utils/function-helper'

export default class PropertySave {
  private page: Page;
  private propertySearch: PropertySearch;

  constructor(page: Page) {
    this.page = page;
    this.propertySearch = new PropertySearch(this.page);
  }

   async saveValues(){
    await this.propertySearch.propertySearchThroughMapSearch(officeDetails.officeUsePid);
    await this.commonOfficeDetails();
   }

    async commonOfficeDetails() {
    await this.page.getByTestId('property-details-toggle').click();
    await this.page.waitForTimeout(3000);

    await selectDropdownOption(this.page, 'property-details-building-grade', -1);
    await selectDropdownOption(this.page, 'property-details-specific-use', -1);

    const officeBuildingSize = await this.page.getByTestId('property-details-building-size').inputValue();
    const defaultBuildingSize = await this.page.getByTestId('property-default-building-size').inputValue();

    await expect(officeBuildingSize).toStrictEqual(defaultBuildingSize);

    await this.page.getByTestId('property-details-min-floor').fill(officeDetails.smallestFloor);
    await this.page.getByTestId('property-details-max-floor').fill(officeDetails.largestFloor);

    await this.page.getByTestId('property-details-contributed-gba').fill(officeDetails.sourceSF);

    await selectDropdownOption(this.page, 'property-details-contributed-source');

    await this.page.getByTestId('property-details-office-nla').fill(officeDetails.sourceSF);

    await selectDropdownOption(this.page, 'property-details-nla-source');

    await this.page.getByTestId('property-details-retail-glar').fill(officeDetails.sourceSF);

    await selectDropdownOption(this.page, 'property-details-glar-source');

    await this.page.getByTestId('property-details-typical-size').fill(officeDetails.sourceSF);

    await selectDropdownOption(this.page, 'property-details-typical-source');

    await this.page.getByTestId('property-details-sprinkler').getByRole('combobox').click();
    await this.page.getByRole('option', { name: 'Yes' }).click();

    await selectDropdownOption(this.page, 'property-details-sprinkler-type');

    await this.page.getByTestId('property-details-year-built').fill(officeDetails.yearBuilt);
    await this.page.getByTestId('property-details-year-renovated').fill(officeDetails.yearRenovated);


    await selectDropdownOption(this.page, 'property-details-nabers-energy');
    await selectDropdownOption(this.page, 'property-details-water-rating');
    await selectDropdownOption(this.page, 'property-details-green-star');

    await this.page.getByTestId('property-details-gresb-minscore').fill(officeDetails.gresbScoreMin);
    await this.page.getByTestId('property-details-gresb-maxscore').fill(officeDetails.gresbScoreMax);

    await this.page.getByTestId('property-details-contributed-comments').fill(officeDetails.comments);

    // await selectDropdownOption(this.page, 'property-details-amenities-type');
    // await this.page.waitForTimeout(100);

    // await this.page.getByTestId('property-details-amenities-comments').click();
    // await this.page.waitForTimeout(100);
    // await this.page.getByTestId('property-details-amenities-comments').fill(officeDetails.comments);

    await this.page.getByTestId('property-details-building-comments').fill(officeDetails.comments);
    await this.page.getByTestId('property-details-building-website').fill(officeDetails.buildingWebsite);

    await this.page.getByRole('button', { name: 'Save', exact: true }).click();
    const successMessage = this.page.getByText('Property Saved Successfully');
    await successMessage.waitFor({ state: 'visible', timeout: 10000 });
    await this.page.waitForTimeout(5000);
    // Confirm field values match expected inputs
    // Numeric inputs
    // const minFloor = await getNumericValue(this.page, 'property-details-min-floor');
    // expect(minFloor).toBe(Number(officeDetails.smallestFloor));

    // const maxFloor = await getNumericValue(this.page, 'property-details-max-floor');
    // expect(maxFloor).toBe(Number(officeDetails.largestFloor));

    const contributedValue = await getNumericValue(this.page, 'property-details-contributed-gba');
    expect(contributedValue).toBe(Number(officeDetails.sourceSF));

    const officeNLA = await getNumericValue(this.page, 'property-details-office-nla');
    expect(officeNLA).toBe(Number(officeDetails.sourceSF));

    const retialGlar = await getNumericValue(this.page, 'property-details-retail-glar');
    expect(retialGlar).toBe(Number(officeDetails.sourceSF));

    const typicalSizeSM =await getNumericValue(this.page, 'property-details-typical-size');
    expect(typicalSizeSM).toBe(Number(officeDetails.sourceSF));

    // Yes/No combobox
    await expect(this.page.getByTestId('property-details-sprinkler').locator('.ng-value-label')).toHaveText('Yes');
    // Year fields
    await expect(this.page.getByTestId('property-details-year-built')).toHaveValue(officeDetails.yearBuilt);
    await expect(this.page.getByTestId('property-details-year-renovated')).toHaveValue(officeDetails.yearRenovated);

    // GRESB score
    await expect(this.page.getByTestId('property-details-gresb-minscore')).toHaveValue(officeDetails.gresbScoreMin);
    await expect(this.page.getByTestId('property-details-gresb-maxscore')).toHaveValue(officeDetails.gresbScoreMax);

    // Comments areas
    await expect(this.page.getByTestId('property-details-contributed-comments')).toHaveValue(officeDetails.comments);
    // await expect(this.page.getByTestId('property-details-amenities')).toHaveValue(officeDetails.comments);
    await expect(this.page.getByTestId('property-details-building-comments')).toHaveValue(officeDetails.comments);

    // Website
    await expect(this.page.getByTestId('property-details-building-website')).toHaveValue(officeDetails.buildingWebsite);

    await this.page.getByTestId('change-log-toggle').click();
    await this.page.getByTestId('property-changelog').click();
    await this.page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await this.page.waitForTimeout(2000);
  }

  async validateModifiedAndReviewedBy() {
    await this.propertySearch.propertySearchThroughMapSearch(officeDetails.officeUsePid);

    await this.validateAuditLine('property-modified-by', 'Last Modified By');
    await this.validateAuditLine('property-reviewed-by', 'Last Reviewed By');
  }

  private async validateAuditLine(testId: string, labelPrefix: string) {
    const section = this.page.getByTestId(testId);

    await expect(section).toBeVisible();

    const text = await section.textContent();
    // Pattern matches e.g. "Last Modified By XXX on 17/06/2025"
    const pattern = new RegExp(`${labelPrefix}\\s+\\S.*\\s+on\\s+\\d{2}/\\d{2}/\\d{4}`);

    expect(text).toMatch(pattern);
  }



}


