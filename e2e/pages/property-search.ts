import { Page, expect } from '@playwright/test';
import { propertyData } from '../fixtures/property-test-data';
import { formSearch } from '../fixtures/property-test-data';
import { States, CondoType } from '../utils/constant';

export default class PropertySearch {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  //validate all fields in form search
  async validateFormFields() {
    await this.page.waitForTimeout(5000);
    await expect(this.page.getByText('Property Audit Tool')).toBeVisible();
    await expect(this.page.getByTitle('New Updates').locator('i')).toBeVisible();
    await expect(this.page.getByText('PROPERTY SEARCH')).toBeVisible();

    await expect(this.page.getByText('PID')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Property ID' })).toBeVisible();

    await expect(this.page.getByText('Property Use')).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Office" })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Industrial" })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Retail" })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Apartments" })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Special" })).toBeVisible();
    await expect(this.page.getByRole('checkbox', { name: "Land" })).toBeVisible();
    await expect(this.page.getByText('Street Number')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Min', exact: true })).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Max', exact: true })).toBeVisible();

    await expect(this.page.getByText('Street Name')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Street Name' })).toBeVisible();

    await expect(this.page.getByText('Postal Code')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Postal Code' })).toBeVisible();

    await expect(this.page.getByText('State')).toBeVisible();
    // 1. Click on the dropdown
    const dropdown = this.page.locator('input[role="combobox"]#State');
    await expect(dropdown).toBeVisible();
    await dropdown.click(); // This opens the dropdown
    await this.page.waitForTimeout(1000);
    // 2. Locate the options container and fetch options
    const options = this.page.locator('[role="option"]');
    await expect(options.first()).toBeVisible();
    await expect(options).toHaveCount(6);

    await expect(this.page.getByText('City')).toBeVisible();
    await expect(this.page.locator('#City')).toBeVisible();

    await expect(this.page.getByText('Research Status')).toBeVisible();
    await expect(this.page.locator('#researchType').first()).toBeVisible();

    await expect(this.page.getByText('Without Footprint')).toBeVisible();
    await expect(this.page.getByTestId('withoutFootprint-checkbox')).toBeVisible();

    await expect(this.page.getByText('Without Parcel')).toBeVisible();
    await expect(this.page.getByTestId('withoutParcel-checkbox')).toBeVisible();

    await expect(this.page.getByText('Not Strata Only')).toBeVisible();
    await expect(this.page.getByTestId('notStrata-checkbox')).toBeVisible();

    await expect(this.page.getByText('Exclude Hidden')).toBeVisible();
    await expect(this.page.getByTestId('excludeHidden-checkbox')).toBeVisible();

    await expect(this.page.getByText('Last Modified By')).toBeVisible();
    await expect(this.page.locator('#researcher')).toBeVisible();

    await expect(this.page.getByText('Audit Status')).toBeVisible();
    await expect(this.page.locator('#researchType').nth(1)).toBeVisible();

    await expect(this.page.getByText('Not Reviewed')).toBeVisible();
    await expect(this.page.getByTestId('notReviewed-checkbox')).toBeVisible();

    await expect(this.page.getByText('Last Reviewed')).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Min Date' })).toBeVisible();
    await expect(this.page.getByRole('textbox', { name: 'Max Date' })).toBeVisible();

    await expect(this.page.getByRole('button', { name: 'Reset' })).toBeVisible();
    await expect(this.page.locator('.btn.search-btn')).toBeEnabled();
    await expect(this.page.getByRole('button', { name: 'Add Property' })).toBeVisible();
    await expect(this.page.getByRole('button', { name: 'Map Search' })).toBeVisible();
  };
  
  public async propertySearchThroughMapSearch(pid) {
    // Click Map Search
    await this.page.getByTestId('map-search').click();
    await this.page.locator('gmp-advanced-marker:nth-child(1)').waitFor({ state: 'visible'});
    // Fill in Property ID
    await this.page.getByTestId('map-search-propertyId').click();
    await this.page.getByTestId('map-search-propertyId').fill(pid);
    await this.page.waitForTimeout(1000);
    await this.page.getByTestId('map-search-propertyId').press('Enter');
  
    // Wait for the map pin (gmp-advanced-marker) to appear
    const pin = this.page.locator('gmp-advanced-marker');
    await pin.first().waitFor({ state: 'visible'}); // waits up to 30s
    await this.page.waitForTimeout(3000);
    // Click the marker
    await pin.first().click();
  
    // Wait for property detail heading to appear
    await this.page.getByRole('heading', { name: `Edit Property - ${pid}` }).waitFor({ state: 'visible', timeout: 20000 });
    await this.page.waitForTimeout(3000);
  }

  async validatePropertySearchAndMandatoryFields(){
    await this.propertySearchThroughMapSearch(propertyData.pid);
    await this.validateTopSectionMandatoryFields();
    await this.validateLocationMandatoryFields();
  }

  async validateTopSectionMandatoryFields() {
    //Property Use
    // Combine all possible checked label classes into one selector
    const checkedLabel = this.page.locator(
      '.blueCheckedLabel, .redCheckedLabel, .orangeCheckedLabel, .brownCheckedLabel, .magentaCheckedLabel, .greenCheckedLabel'
    );
    const count = await checkedLabel.count();

    // If none are selected, throw an error
    if (count === 0) {
      throw new Error('No Property Use Type is selected — one must be checked.');
    }

    // at least one is visible
    await expect(checkedLabel.first()).toBeVisible();

    //to do: property floors
    // const floor1 = this.page.getByTestId('property-floor-one');
    // const floor2 = this.page.getByTestId('property-floor-two');
    // const floor3 = this.page.getByTestId('property-floor-three');
    // const floorInput = this.page.getByTestId('property-floor-count');

    // // Wait for one element to appear — adjust based on your form logic
    // await expect(floor1).toBeVisible({ timeout: 5000 });

    // // Check if any radio is selected
    // const isFloor1Checked = await floor1.evaluate(el => el.classList.contains('blueCheckedLabel'));
    // const isFloor2Checked = await floor2.evaluate(el => el.classList.contains('blueCheckedLabel'));
    // const isFloor3Checked = await floor3.evaluate(el => el.classList.contains('blueCheckedLabel'));


    // // Check if the input box has a non-zero value
    // const inputValue = await floorInput.inputValue();
    // const hasInputValue = !!inputValue && Number(inputValue) > 0;

    // // Throw error if none of the above is selected/entered
    // if (!isFloor1Checked && !isFloor2Checked && !isFloor3Checked && !hasInputValue) {
    //   throw new Error('At least one floor must be selected or entered.');
    // }

    // List of mandatory fields with their associated data-testIds and labels
    const mandatoryFields = [
      { testId: 'property-lot-size-source', label: 'Lot Size Source' },
      { testId: 'property-construction-type', label: 'Construction Type' },
      { testId: 'property-construction-status', label: 'Construction Status' },
      { testId: 'property-record-type', label: 'Record Type'},
    ];

    // Iterate over each mandatory field to validate
    for (const field of mandatoryFields) {
      const locator = this.page.getByTestId(field.testId);

      // Check if the element has the 'error-field' class (i.e. validation failed)
      const hasError = await locator.evaluate(el => el.classList.contains('error-field'));

      // If the field has an error, throw a error
      if (hasError) {
        throw new Error(`${field.label} is mandatory but not filled.`);
      }

      // Assert that the field does not have the error class as a double verification
      await expect(locator).not.toHaveClass(/error-field/);
    }

    //Building Size field
    // Locate the mandatory asterisk (*) for the Building Size field
    const defaultBuildingSize = this.page.getByTestId('building-size-default-mandatory');

    // Check if the asterisk is visible, which indicates the field is not filled
    const isVisible = await defaultBuildingSize.isVisible();

    // If visible, throw an error stating that the mandatory field is not filled
    if (isVisible) {
      throw new Error('Building Size is mandatory but not filled.');
    }
   
  }

  async validateLocationMandatoryFields(){
    await this.page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await this.page.getByTestId('location-info-toggle').click();
    const addressRadio = this.page.getByTestId('location-address-adress');
    const intersectionRadio = this.page.getByTestId('location-address-intersection');

    const isAddressChecked = await addressRadio.isChecked();
    const isIntersectionChecked = await intersectionRadio.isChecked();

    expect(isAddressChecked || isIntersectionChecked).toBeTruthy();
    if (!isAddressChecked && !isIntersectionChecked) {
      throw new Error('Neither "Address" nor "Intersection" radio is selected.');
    }

    // List of mandatory fields with their associated data-testIds and labels
    const mandatoryFields = [
      { testId: 'location-city', label: 'City' },
      { testId: 'location-state', label: 'State' },
      { testId: 'location-zipcode', label: 'ZipCode' },
      { testId: 'location-property-name', label: 'Property Name' },
      { testId: 'location-latitude', label: 'Latitude' },
      { testId: 'location-longitude', label: 'Longitude' },
    ];

        for (const field of mandatoryFields) {
      const locator = this.page.getByTestId(field.testId);

      // Check if the element has the 'error-field' class (i.e. validation failed)
      const hasError = await locator.evaluate(el => el.classList.contains('error-field'));

      // If the field has an error, throw a error
      if (hasError) {
        throw new Error(`${field.label} is mandatory but not filled.`);
      }

      // Assert that the field does not have the error class as a double verification
      await expect(locator).not.toHaveClass(/error-field/);
    }
  }

  async postalCodeMapSearch() {
    // Click on Add Property button
    await this.page.getByTestId('add-property').click();

    // Wait for UI to settle
    await this.page.waitForTimeout(5000);

    // Fill in the postal code and press Enter
    await this.page.getByTestId('search-postal-code').fill(propertyData.postalCode);
    await this.page.getByTestId('search-postal-code').press('Enter');
    await this.page.waitForTimeout(3000);

    // Locate the map marker (e.g., 3rd marker)
    const pin = this.page.locator('gmp-advanced-marker:nth-child(3)');

    // check that the marker is visible
    await expect(pin.first()).toBeVisible({ timeout: 10000 });
    await this.page.waitForTimeout(3000);

    // Click the marker after it's confirmed visible
    await pin.first().click();
    await this.page.waitForTimeout(7000);
  }

  async pIdFormSearch() {
    try {
      // Enter Property ID and search
      await this.page.getByTestId('search-propertyid').fill(formSearch.propertyId);
      await this.page.getByTestId('search-search-button').click();

      // Locate the cell with expected PID
      await this.page.getByRole('cell', { name: formSearch.propertyId }).waitFor({ timeout: 10000 });
      const cell = this.page.getByRole('cell', { name: formSearch.propertyId });
      const count = await cell.count();

      if (count > 0) {
        await expect(cell.first()).toHaveText(formSearch.propertyId);
        return { success: true, resultsCount: count };
      } else {
        return { success: false, resultsCount: 0 };
      }
    } catch (error) {
      console.error('Exception during property id search:', error);
      return { success: false, resultsCount: 0 };
    }

  }

  async postalCodeFormSearch() {
    await this.page.getByTestId('search-postal-code').fill(formSearch.postalCode);
    await this.page.getByTestId('search-search-button').click();

    await this.page.getByRole('cell', { name: '001'}).waitFor({ timeout: 10000 })
    const table = this.page.locator('[data-testid="search-table-result"]')
    const tableHeader = table.locator('thead tr th .header-row-content');

    // Step 1: Find "Zipcode" column index from thead
    const headerCount = await tableHeader.count();

    let zipcodeColIndex = -1;
    for (let i = 0; i < headerCount; i++) {
      const text = (await tableHeader.nth(i).textContent())?.trim();
      const normalized = text?.replace(/\s+/g, '').toLowerCase();
      if (normalized === 'zipcode') {
        zipcodeColIndex = i;
        break;
      }
    }


    if (zipcodeColIndex === -1) {
      throw new Error(`"Zipcode" column not found in table headers.`);
    }

    // Step 2: Verify all rows in that column contain the expected zipcode
    const rows = table.locator('tbody tr');
    const rowCount = await rows.count();

    for (let i = 0; i < rowCount; i++) {
      const cell = rows.nth(i).locator('td').nth(zipcodeColIndex + 1);
      const cellText = (await cell.textContent())?.trim();
      const nrmltext = cellText?.replace(/\s+/g, '').toLowerCase();

      if (nrmltext !== formSearch.postalCode) {
        throw new Error(
          `Row ${i + 1}: Expected Zipcode "${formSearch.postalCode}", but found "${cell.textContent()}"`
        );
      }
    }
  }

  async validateMasterPidSearch(formSearchPid: string, expectedTypes: string[]) {
    await this.page.getByTestId('search-propertyid').fill(formSearchPid);
    await this.page.getByTestId('search-search-button').click();

    await this.page.getByRole('cell', { name: '001'}).waitFor({ timeout: 10000 })
    const table = this.page.locator('[data-testid="search-table-result"]');
    const tableHeader = table.locator('thead tr th .header-row-content');

    // Wait until headers are rendered
    await this.page.waitForFunction(() => {
      const headers = document.querySelectorAll('[data-testid="search-table-result"] thead tr th .header-row-content');
      return headers.length > 0;
    });

    const headerCount = await tableHeader.count();

    let recordTypeColIndex = -1;
    for (let i = 0; i < headerCount; i++) {
      const text = (await tableHeader.nth(i).textContent())?.trim();
      const normalized = text?.replace(/\s+/g, '').toLowerCase();
      if (normalized === 'recordtype') {
        recordTypeColIndex = i;
        break;
      }
    }

    if (recordTypeColIndex === -1) {
      throw new Error(`"Record Type" column not found in table headers.`);
    }

    const rows = table.locator('tbody tr');
    const rowCount = await rows.count();

    for (let i = 0; i < rowCount; i++) {
      const cell = rows.nth(i).locator('td').nth(recordTypeColIndex + 1); // +1 for checkbox
      const cellText = (await cell.textContent())?.trim();
      // Last row MasterPID validation
      if (i === rowCount - 1) {
        const pidCell = rows.nth(i).locator('td').nth(2);
        const masterId = (await pidCell.textContent())?.trim() ?? '';
        await expect(masterId).toBe(formSearchPid);
      }
      if (!expectedTypes.includes(cellText ?? '')) {
        throw new Error(
          `Row ${i + 1}: Expected RecordType ${expectedTypes.join(' or ')}, but found "${cellText}"`
        );
      }
    }
  }

  async masterStrataPidFormSearch() {
    await this.validateMasterPidSearch(formSearch.masterStrataPid, [CondoType.MasterStrata, CondoType.Strata]);
  }

  async masterFreeholdPidFormSearch() {
    await this.validateMasterPidSearch(formSearch.masterFreeholdPid, [CondoType.MasterFreehold, CondoType.ChildFreehold]);
  }

  async cityFormSearch() {

    // Pick a random state
    await this.page.waitForTimeout(3000); //wait to load api
    const randomIndex = Math.floor(Math.random() * States.length);
    const selectedState = States[randomIndex];

    await this.page.getByTestId('search-state').getByRole('combobox').click();
    await this.page.getByRole('option', { name: selectedState, exact: true }).click();

    await this.page.getByTestId('search-city').getByRole('combobox').click();
    await this.page.getByRole('option')

    const cityOptions = this.page.locator('[role="listbox"] >> [role="option"]');
    const cityCount = await cityOptions.count();

    // Pick a random city
    const randomCityIndex = Math.floor(Math.random() * cityCount);
    const cityOption = cityOptions.nth(randomCityIndex);
    const cityName = await cityOption.textContent();

    // Click to select it
    await cityOption.click();

    await this.page.getByTestId('search-search-button').click();
    await this.page.waitForTimeout(3000);
    const cell = this.page.getByRole('cell', { name: '001' });
    if (await cell.count() == 0) {
      console.log('No records found.');
    } else {

      const table = this.page.locator('[data-testid="search-table-result"]');
      const tableHeader = table.locator('thead tr th .header-row-content');
      const headerCount = await tableHeader.count();

      let cityColIndex = -1;
      for (let i = 0; i < headerCount; i++) {
        const text = (await tableHeader.nth(i).textContent())?.trim();
        const normalized = text?.replace(/\s+/g, '').toLowerCase();
        if (normalized === 'city') {
          cityColIndex = i;
          break;
        }
      }

      if (cityColIndex === -1) {
        throw new Error(`"City" column not found in table headers.`);
      }

      // Step 2: Verify all rows in that column contain the expected city
      const rows = table.locator('tbody tr');
      const rowCount = await rows.count();

      for (let i = 0; i < rowCount; i++) {
        const cell = rows.nth(i).locator('td').nth(cityColIndex + 1); // +1 for checkbox column
        const cellText = (await cell.textContent())?.trim();
        const nrmltext = cellText?.replace(/\s+/g, '').toLowerCase();
        if (nrmltext !== cityName?.replace(/\s+/g, '').trim().toLowerCase()) {
          throw new Error(
            `Row ${i + 1}: Expected City, "${cityName}" but found "${nrmltext}"`
          );
        }
      }
    }

  }

}


