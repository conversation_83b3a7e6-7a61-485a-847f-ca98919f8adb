import { defineConfig } from '@playwright/test';

// Read environment from process.env.ENV, default to 'dev' if not specified
// @ts-ignore
const environment = process.env.ENV || 'phoenixdev';

// Define URLs for different environments
const environmentConfig = {
  local: {
    baseURL: 'http://localhost:4200',
  },
  dev: {
    baseURL: 'https://vst-phoenix-dev.arealytics.com.au',
  },
  phoenixdev: {
    baseURL: 'https://vst-phoenix-dev.arealytics.com.au',
  },
  uat: {
    baseURL: 'https://vst-uat.arealytics.com.au',
  },
  prod: {
    baseURL: 'https://pro.arealytics.com.au',
  },
};

// Get the config for the current environment
const envConfig = environmentConfig[environment] || environmentConfig.dev;

export default defineConfig({
  testDir: './tests',
  workers: 4,
  timeout: 120000,
  reporter: [['html', { outputFolder: 'playwright-report', open: 'never' }]],
  use: {
    headless: true,
    baseURL: envConfig.baseURL,
    screenshot: 'only-on-failure',
    // video: 'retain-on-failure',
    video: 'on',
    trace: 'on-first-retry',
    launchOptions: {
      slowMo: 200, // slow down Playwright actions by 200ms
    },
  },


});

export { environmentConfig };

