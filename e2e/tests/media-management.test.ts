import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';

import Login from '../pages/login.page';
import Media from '../pages/media-management';

test.describe('Media test', () => {
    let pagelogin: Page;

    test.beforeEach(async ({ page }) => {
        // @ts-ignore
        const environment = process.env.ENV || 'dev';
        const baseURL = environmentConfig[environment]?.baseURL;
        await page.goto(baseURL);
        await page.setViewportSize({ width: 1920, height: 1040 });
        const loginPage = new Login(page);
        await loginPage.login();
        pagelogin = page;
    });

    test('Should add and delete Media', async () => {
        const mediaTest = new Media(pagelogin);
        await mediaTest.addNewMedia();
    });

    test('Should set media default and reset back to not default', async () => {
        const mediaTest = new Media(pagelogin);
        await mediaTest.setFirstMediaAsDefault();
    });

})