import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';

import Login from '../pages/login.page';
import Notes from '../pages/notes-management';

test.describe('Notes test', () => {
    let pagelogin: Page;

    test.beforeEach(async ({ page }) => {
        // @ts-ignore
        const environment = process.env.ENV || 'dev';
        const baseURL = environmentConfig[environment]?.baseURL;
        await page.goto(baseURL);
        await page.setViewportSize({ width: 1920, height: 1040 });
        const loginPage = new Login(page);
        await loginPage.login();
        pagelogin = page;
    });

    test('Should add, edit and delete Notes', async () => {
        const notesTest = new Notes(pagelogin);
        await notesTest.addNotes();
    });

})
