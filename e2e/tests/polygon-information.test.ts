import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';

import Login from '../pages/login.page';
import PolygonInformationTest from '../pages/polygon-management'

test.describe('Polygon Tests', () => {
  let pagelogin: Page;

  test.beforeEach(async ({ page }) => {
    // @ts-ignore
    const environment = process.env.ENV || 'dev';
    const baseURL = environmentConfig[environment]?.baseURL;
    await page.goto(baseURL);
    await page.setViewportSize({ width: 1920, height: 1040 });
    const loginPage = new Login(page);
    await loginPage.login();
    pagelogin = page;
  });


  //copy polygon
  test('Should add a new floor section using Copy Polygon and validate building size', async () => {
    const copyPolygon = new PolygonInformationTest(pagelogin);
    await copyPolygon.validateCopyPolygon();
  });

})