import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';

import Login from '../pages/login.page';
import PropertyFeatures from '../pages/property-features'

test.describe('Property Feature test', () => {
    let pagelogin: Page;

    test.beforeEach(async ({ page }) => {
        // @ts-ignore
        const environment = process.env.ENV || 'dev';
        const baseURL = environmentConfig[environment]?.baseURL;
        await page.goto(baseURL);
        await page.setViewportSize({ width: 1920, height: 1040 });
        const loginPage = new Login(page);
        await loginPage.login();
        pagelogin = page;
    });

    //additional address
    test('Should add and delete additional address', async () => {
        const additionalAddress = new PropertyFeatures(pagelogin);
        await additionalAddress.addAdditionalAddress();
    });

})