import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';

import Login from '../pages/login.page';
import PropertySave from '../pages/property-save';


test.describe('Property Save Tests', () => {
  let pagelogin: Page;

  test.beforeEach(async ({ page }) => {
    // @ts-ignore
    const environment = process.env.ENV || 'dev';
    const baseURL = environmentConfig[environment]?.baseURL;
    await page.goto(baseURL);
    await page.setViewportSize({ width: 1920, height: 1040 });
    const loginPage = new Login(page);
    await loginPage.login();
    pagelogin = page;
  });


  test('Should save common office details', async () => {
    const propertySave = new PropertySave(pagelogin);
    await propertySave.saveValues();
  });

  test('Should verify last modified by and last reviewed by', async () => {
    const propertySave = new PropertySave(pagelogin);
    await propertySave.validateModifiedAndReviewedBy();
  })

})