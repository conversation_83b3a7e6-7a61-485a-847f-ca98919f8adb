import { test, Page } from '@playwright/test';
import { environmentConfig } from '../playwright.config';
import { propertyData } from '../fixtures/property-test-data';

import Login from '../pages/login.page';
import PropertySearch from '../pages/property-search';


test.describe('Property Search Tests', () => {
  let pagelogin: Page;

  test.beforeEach(async ({ page }) => {
    // @ts-ignore
    const environment = process.env.ENV || 'dev';
    const baseURL = environmentConfig[environment]?.baseURL;
    await page.goto(baseURL);
    await page.setViewportSize({ width: 1920, height: 1040 });
    const loginPage = new Login(page);
    await loginPage.login();
    pagelogin = page;
  });


  test('Should search for PropertyId through map search and validate the id', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.propertySearchThroughMapSearch(propertyData.pid);
  });

  // we are just validating here
  test('Should validate all the mandatory fields filled are not', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.validatePropertySearchAndMandatoryFields();
  });

  test('Should search for property through Postal code map search', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.postalCodeMapSearch();
  });

  test('Should search for master freehold property through form search ', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.masterFreeholdPidFormSearch();
  })

  test('Should search for master strata property through form search ', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.masterStrataPidFormSearch();
  })

  test('Should search using postal code through form search ', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.postalCodeFormSearch();
  })

  test('Should search a property through form search ', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.pIdFormSearch();
  })

  test('Should search for city property through form search ', async () => {
    const propertySearch = new PropertySearch(pagelogin);
    await propertySearch.cityFormSearch();
  })

})


