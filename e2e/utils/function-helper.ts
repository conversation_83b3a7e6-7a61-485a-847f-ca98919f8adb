import type { Page } from '@playwright/test';

export async function selectDropdownOption(
  page: Page,
  testId: string,
  index = 0
) {
  const root = page.getByTestId(testId);
  const clearBtn = root.getByTitle('Clear all');
  if (await clearBtn.isVisible()) await clearBtn.click();

  await root.getByRole('combobox').click();
  const options = page.getByRole('option');
  if (index < 0) {
    await options.last().click();
  } else {
    await options.nth(index).click();
  }
}

export async function getNumericValue(
  page: Page,
  testId: string
): Promise<number> {
  const raw = await page.getByTestId(testId).inputValue();
  return parseFloat(raw);
}