#!/bin/bash

# Check for environment input
ENV=$1

if [[ -z "$ENV" ]]; then
  echo "❌ Please provide the environment (dev, uat, prod)"
  echo "Usage: ./regenerate-api.sh dev"
  exit 1
fi

# Define Swagger URLs per environment
if [[ "$ENV" == "dev" ]]; then
  API_URL="https://api-phoenix-dev.arealytics.com.au/v3/api-docs"
elif [[ "$ENV" == "uat" ]]; then
  API_URL="https://api-phoenix-uat.arealytics.com.au/v3/api-docs"
elif [[ "$ENV" == "prod" ]]; then
  API_URL="https://api-phoenix.arealytics.com.au/v3/api-docs"
else
  echo "❌ Unknown environment: $ENV"
  exit 1
fi

echo "🌐 Regenerating API client for environment: $ENV"
echo "🔗 Using API: $API_URL"

#Install node 18
# nvm install 18
# nvm use 18


#Install OpenAPI Generator if not already installed
npm install -g @openapitools/openapi-generator-cli

# Remove old client
rm -rf ./src/app/api-client

# Generate the client (no quotes here!)
openapi-generator-cli generate -i "$API_URL" -g typescript-angular --additional-properties=ngVersion=9.0.0,providedInRoot=true -o ./src/app/api-client

echo "✅ API client generated for $ENV"
