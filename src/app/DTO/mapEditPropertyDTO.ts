import { LatLng } from "../modules/map-module/models/LatLng";
import { AddressDTO } from "../api-client";
import { PropertyDetails } from "../models/PropertyDetails";

export class mapEditPropertyDTO{
    latLng: LatLng;
    propertyId: number;
    locationData: any;
    selectedParcel: any;
    isNewProperty:boolean;
    parcelProperties :any;
    address: any;
    fromMasterStrata: boolean;
    masterStrataObj: masterStrataDTO;
}

export class masterStrataDTO {
    property: PropertyDetails;
    minStrataUnit: any;
    isMultiStrata: boolean;
    strataList: any[];
    isFreehold: boolean;
}

