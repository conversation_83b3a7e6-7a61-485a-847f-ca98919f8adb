/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { ApiResponseListUsersDTO } from '../model/apiResponseListUsersDTO';
// @ts-ignore
import { ApiResponseUserPreferenceResponseDTO } from '../model/apiResponseUserPreferenceResponseDTO';
// @ts-ignore
import { UserPreferenceRequestDTO } from '../model/userPreferenceRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class ArealyticsUsersService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Create User Preferences
     * Creates user preferences based on the provided request
     * @param userPreferenceRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createUserPreferences(userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseUserPreferenceResponseDTO>;
    public createUserPreferences(userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseUserPreferenceResponseDTO>>;
    public createUserPreferences(userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseUserPreferenceResponseDTO>>;
    public createUserPreferences(userPreferenceRequestDTO: UserPreferenceRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (userPreferenceRequestDTO === null || userPreferenceRequestDTO === undefined) {
            throw new Error('Required parameter userPreferenceRequestDTO was null or undefined when calling createUserPreferences.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/users/preferences`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseUserPreferenceResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: userPreferenceRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Arealytics Users
     * Fetches the list of users associated with Arealytics
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getArealyticsUsers(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListUsersDTO>;
    public getArealyticsUsers(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListUsersDTO>>;
    public getArealyticsUsers(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListUsersDTO>>;
    public getArealyticsUsers(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/users/arealytics`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListUsersDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get User Preferences by Type and Screen
     * Fetches user preferences based on type and screen
     * @param type 
     * @param screen 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getUserPreferecesByTypeAndScreen(type: 'DOWNLOAD' | 'CUSTOMIZE' | 'MAIL_PREFERENCES', screen: 'TRANSACTION' | 'REQUIRED_PREFERENCE' | 'TENANT' | 'REPORT_PREFERENCES' | 'LEASE_TRANSACTION' | 'DASHBOARD_ACTIVITY' | 'PROPERTY' | 'MARKET_BRIEF_MESSAGE_PREFERENCES' | 'MARKET_BRIEF_PREFERENCES' | 'MARKET_BRIEF_SUITE' | 'MARKET_BRIEF_TOUR' | 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE' | 'RECENTLY_ADDED_LISTING' | 'RECENT_LISTING_ACTIVITY' | 'PURCHASE_LOG' | 'EXECUTION_DETAILS' | 'SUITES', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseUserPreferenceResponseDTO>;
    public getUserPreferecesByTypeAndScreen(type: 'DOWNLOAD' | 'CUSTOMIZE' | 'MAIL_PREFERENCES', screen: 'TRANSACTION' | 'REQUIRED_PREFERENCE' | 'TENANT' | 'REPORT_PREFERENCES' | 'LEASE_TRANSACTION' | 'DASHBOARD_ACTIVITY' | 'PROPERTY' | 'MARKET_BRIEF_MESSAGE_PREFERENCES' | 'MARKET_BRIEF_PREFERENCES' | 'MARKET_BRIEF_SUITE' | 'MARKET_BRIEF_TOUR' | 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE' | 'RECENTLY_ADDED_LISTING' | 'RECENT_LISTING_ACTIVITY' | 'PURCHASE_LOG' | 'EXECUTION_DETAILS' | 'SUITES', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseUserPreferenceResponseDTO>>;
    public getUserPreferecesByTypeAndScreen(type: 'DOWNLOAD' | 'CUSTOMIZE' | 'MAIL_PREFERENCES', screen: 'TRANSACTION' | 'REQUIRED_PREFERENCE' | 'TENANT' | 'REPORT_PREFERENCES' | 'LEASE_TRANSACTION' | 'DASHBOARD_ACTIVITY' | 'PROPERTY' | 'MARKET_BRIEF_MESSAGE_PREFERENCES' | 'MARKET_BRIEF_PREFERENCES' | 'MARKET_BRIEF_SUITE' | 'MARKET_BRIEF_TOUR' | 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE' | 'RECENTLY_ADDED_LISTING' | 'RECENT_LISTING_ACTIVITY' | 'PURCHASE_LOG' | 'EXECUTION_DETAILS' | 'SUITES', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseUserPreferenceResponseDTO>>;
    public getUserPreferecesByTypeAndScreen(type: 'DOWNLOAD' | 'CUSTOMIZE' | 'MAIL_PREFERENCES', screen: 'TRANSACTION' | 'REQUIRED_PREFERENCE' | 'TENANT' | 'REPORT_PREFERENCES' | 'LEASE_TRANSACTION' | 'DASHBOARD_ACTIVITY' | 'PROPERTY' | 'MARKET_BRIEF_MESSAGE_PREFERENCES' | 'MARKET_BRIEF_PREFERENCES' | 'MARKET_BRIEF_SUITE' | 'MARKET_BRIEF_TOUR' | 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE' | 'RECENTLY_ADDED_LISTING' | 'RECENT_LISTING_ACTIVITY' | 'PURCHASE_LOG' | 'EXECUTION_DETAILS' | 'SUITES', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (type === null || type === undefined) {
            throw new Error('Required parameter type was null or undefined when calling getUserPreferecesByTypeAndScreen.');
        }
        if (screen === null || screen === undefined) {
            throw new Error('Required parameter screen was null or undefined when calling getUserPreferecesByTypeAndScreen.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/users/preferences/${this.configuration.encodeParam({name: "type", value: type, in: "path", style: "simple", explode: false, dataType: "'DOWNLOAD' | 'CUSTOMIZE' | 'MAIL_PREFERENCES'", dataFormat: undefined})}/${this.configuration.encodeParam({name: "screen", value: screen, in: "path", style: "simple", explode: false, dataType: "'TRANSACTION' | 'REQUIRED_PREFERENCE' | 'TENANT' | 'REPORT_PREFERENCES' | 'LEASE_TRANSACTION' | 'DASHBOARD_ACTIVITY' | 'PROPERTY' | 'MARKET_BRIEF_MESSAGE_PREFERENCES' | 'MARKET_BRIEF_PREFERENCES' | 'MARKET_BRIEF_SUITE' | 'MARKET_BRIEF_TOUR' | 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE' | 'RECENTLY_ADDED_LISTING' | 'RECENT_LISTING_ACTIVITY' | 'PURCHASE_LOG' | 'EXECUTION_DETAILS' | 'SUITES'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseUserPreferenceResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update User Preferences
     * Updates user preferences based on the provided request and user preference ID
     * @param userPreferenceId 
     * @param userPreferenceRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateUserPreferences(userPreferenceId: number, userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseUserPreferenceResponseDTO>;
    public updateUserPreferences(userPreferenceId: number, userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseUserPreferenceResponseDTO>>;
    public updateUserPreferences(userPreferenceId: number, userPreferenceRequestDTO: UserPreferenceRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseUserPreferenceResponseDTO>>;
    public updateUserPreferences(userPreferenceId: number, userPreferenceRequestDTO: UserPreferenceRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (userPreferenceId === null || userPreferenceId === undefined) {
            throw new Error('Required parameter userPreferenceId was null or undefined when calling updateUserPreferences.');
        }
        if (userPreferenceRequestDTO === null || userPreferenceRequestDTO === undefined) {
            throw new Error('Required parameter userPreferenceRequestDTO was null or undefined when calling updateUserPreferences.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/users/preferences/${this.configuration.encodeParam({name: "userPreferenceId", value: userPreferenceId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseUserPreferenceResponseDTO>('put', `${basePath}${localVarPath}`,
            {
                body: userPreferenceRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
