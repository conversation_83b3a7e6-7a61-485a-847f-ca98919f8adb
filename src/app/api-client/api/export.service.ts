/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { ApiResponseBoolean } from '../model/apiResponseBoolean';
// @ts-ignore
import { ApiResponseExportCheckLimitResponseDTO } from '../model/apiResponseExportCheckLimitResponseDTO';
// @ts-ignore
import { ApiResponseListExportSettingsResponseDTO } from '../model/apiResponseListExportSettingsResponseDTO';
// @ts-ignore
import { ApiResponseWeeklyExportLimitResponseDTO } from '../model/apiResponseWeeklyExportLimitResponseDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class ExportService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Check limit flag for entity
     * Checks if the export limit flag is set for a specific entity.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public checkLimitFlag(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseExportCheckLimitResponseDTO>;
    public checkLimitFlag(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseExportCheckLimitResponseDTO>>;
    public checkLimitFlag(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseExportCheckLimitResponseDTO>>;
    public checkLimitFlag(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/export/check-limit-flag`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseExportCheckLimitResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get default export settings
     * Fetches the default export settings for the application.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getDefaultSettings(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListExportSettingsResponseDTO>;
    public getDefaultSettings(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListExportSettingsResponseDTO>>;
    public getDefaultSettings(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListExportSettingsResponseDTO>>;
    public getDefaultSettings(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/export/default-settings`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListExportSettingsResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get weekly export limit by parent table
     * Fetches the weekly export limit for a specific parent table.
     * @param parentTableId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getWeeklyExportLimitByParentTable(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseWeeklyExportLimitResponseDTO>;
    public getWeeklyExportLimitByParentTable(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseWeeklyExportLimitResponseDTO>>;
    public getWeeklyExportLimitByParentTable(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseWeeklyExportLimitResponseDTO>>;
    public getWeeklyExportLimitByParentTable(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (parentTableId === null || parentTableId === undefined) {
            throw new Error('Required parameter parentTableId was null or undefined when calling getWeeklyExportLimitByParentTable.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/export/weekily-export-limit/${this.configuration.encodeParam({name: "parentTableId", value: parentTableId, in: "path", style: "simple", explode: false, dataType: "'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseWeeklyExportLimitResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save data protection disclaimer log
     * Saves the data protection disclaimer flag for a specific parent table.
     * @param parentTableId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public saveDataProtectionDesclaimerLog(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseBoolean>;
    public saveDataProtectionDesclaimerLog(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseBoolean>>;
    public saveDataProtectionDesclaimerLog(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseBoolean>>;
    public saveDataProtectionDesclaimerLog(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (parentTableId === null || parentTableId === undefined) {
            throw new Error('Required parameter parentTableId was null or undefined when calling saveDataProtectionDesclaimerLog.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/export/save-data-protection-disclaimer-log/${this.configuration.encodeParam({name: "parentTableId", value: parentTableId, in: "path", style: "simple", explode: false, dataType: "'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseBoolean>('post', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
