/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { ApiResponseImageUploadDTO } from '../model/apiResponseImageUploadDTO';
// @ts-ignore
import { ApiResponseListMediaDTO } from '../model/apiResponseListMediaDTO';
// @ts-ignore
import { ApiResponseMediaDTO } from '../model/apiResponseMediaDTO';
// @ts-ignore
import { ApiResponseNull } from '../model/apiResponseNull';
// @ts-ignore
import { ImageUploadRequestDTO } from '../model/imageUploadRequestDTO';
// @ts-ignore
import { MediaDeleteRequestDTO } from '../model/mediaDeleteRequestDTO';
// @ts-ignore
import { MediaRequestDTO } from '../model/mediaRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class MediaService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Create New Media Record
     * This API allows the creation of a new media record based on the provided &#x60;MediaRequestDTO&#x60;. It supports associating media with entities like properties or other relation types. If marked as default, it automatically updates related defaults and may cascade updates to parent entities such as a master property in a STRATA relationship.
     * @param mediaRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createMedia(mediaRequestDTO: MediaRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseMediaDTO>;
    public createMedia(mediaRequestDTO: MediaRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseMediaDTO>>;
    public createMedia(mediaRequestDTO: MediaRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseMediaDTO>>;
    public createMedia(mediaRequestDTO: MediaRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (mediaRequestDTO === null || mediaRequestDTO === undefined) {
            throw new Error('Required parameter mediaRequestDTO was null or undefined when calling createMedia.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseMediaDTO>('post', `${basePath}${localVarPath}`,
            {
                body: mediaRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete Existing Media Record
     * This API deletes an existing media record identified by &#x60;mediaRelationshipId&#x60;. 
     * @param mediaDeleteRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteMedia(mediaDeleteRequestDTO: MediaDeleteRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNull>;
    public deleteMedia(mediaDeleteRequestDTO: MediaDeleteRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNull>>;
    public deleteMedia(mediaDeleteRequestDTO: MediaDeleteRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNull>>;
    public deleteMedia(mediaDeleteRequestDTO: MediaDeleteRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (mediaDeleteRequestDTO === null || mediaDeleteRequestDTO === undefined) {
            throw new Error('Required parameter mediaDeleteRequestDTO was null or undefined when calling deleteMedia.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/delete`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNull>('delete', `${basePath}${localVarPath}`,
            {
                body: mediaDeleteRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Fetch Media by Property ID
     * Retrieves media records linked to the specified property\&#39;s child properties. Media associated with the master (parent) property is not included in the response.
     * @param propertyId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getLinkedPropertyMedia(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListMediaDTO>;
    public getLinkedPropertyMedia(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListMediaDTO>>;
    public getLinkedPropertyMedia(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListMediaDTO>>;
    public getLinkedPropertyMedia(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getLinkedPropertyMedia.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/linked-properties-media/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListMediaDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Fetch Media by Relation ID
     * This API retrieves media records based on the given &#x60;mediaRelationId&#x60; and &#x60;relationId&#x60;. It supports fetching media for various types of relations such as entities, properties, etc. The response includes additional information about the media, such as edit permissions and building size for property-related media.
     * @param mediaRelationTypeId Media Relation Type
     * @param relationId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getMediaByRelationId(mediaRelationTypeId: 'PROPERTY' | 'LISTING' | 'SUITE' | 'SALE' | 'COMPANY' | 'BRANCH' | 'PERSON' | 'ENTITY' | 'ALL_MEDIA' | 'PROPERTY_CHANGE_LOG' | 'LEASE' | 'MEDIA_SUPPORT_DOCS' | 'MARKET_BRIEF', relationId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListMediaDTO>;
    public getMediaByRelationId(mediaRelationTypeId: 'PROPERTY' | 'LISTING' | 'SUITE' | 'SALE' | 'COMPANY' | 'BRANCH' | 'PERSON' | 'ENTITY' | 'ALL_MEDIA' | 'PROPERTY_CHANGE_LOG' | 'LEASE' | 'MEDIA_SUPPORT_DOCS' | 'MARKET_BRIEF', relationId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListMediaDTO>>;
    public getMediaByRelationId(mediaRelationTypeId: 'PROPERTY' | 'LISTING' | 'SUITE' | 'SALE' | 'COMPANY' | 'BRANCH' | 'PERSON' | 'ENTITY' | 'ALL_MEDIA' | 'PROPERTY_CHANGE_LOG' | 'LEASE' | 'MEDIA_SUPPORT_DOCS' | 'MARKET_BRIEF', relationId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListMediaDTO>>;
    public getMediaByRelationId(mediaRelationTypeId: 'PROPERTY' | 'LISTING' | 'SUITE' | 'SALE' | 'COMPANY' | 'BRANCH' | 'PERSON' | 'ENTITY' | 'ALL_MEDIA' | 'PROPERTY_CHANGE_LOG' | 'LEASE' | 'MEDIA_SUPPORT_DOCS' | 'MARKET_BRIEF', relationId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (mediaRelationTypeId === null || mediaRelationTypeId === undefined) {
            throw new Error('Required parameter mediaRelationTypeId was null or undefined when calling getMediaByRelationId.');
        }
        if (relationId === null || relationId === undefined) {
            throw new Error('Required parameter relationId was null or undefined when calling getMediaByRelationId.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/${this.configuration.encodeParam({name: "mediaRelationTypeId", value: mediaRelationTypeId, in: "path", style: "simple", explode: false, dataType: "'PROPERTY' | 'LISTING' | 'SUITE' | 'SALE' | 'COMPANY' | 'BRANCH' | 'PERSON' | 'ENTITY' | 'ALL_MEDIA' | 'PROPERTY_CHANGE_LOG' | 'LEASE' | 'MEDIA_SUPPORT_DOCS' | 'MARKET_BRIEF'", dataFormat: undefined})}/${this.configuration.encodeParam({name: "relationId", value: relationId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListMediaDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update Existing Media Record
     * This API updates an existing media record identified by &#x60;mediaId&#x60; using the provided &#x60;MediaRequestDTO&#x60;. It ensures related property data is updated if the media is marked as default. For STRATA properties, it conditionally updates the corresponding master property\&#39;s media record as well.
     * @param mediaId 
     * @param mediaRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateMedia(mediaId: number, mediaRequestDTO: MediaRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseMediaDTO>;
    public updateMedia(mediaId: number, mediaRequestDTO: MediaRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseMediaDTO>>;
    public updateMedia(mediaId: number, mediaRequestDTO: MediaRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseMediaDTO>>;
    public updateMedia(mediaId: number, mediaRequestDTO: MediaRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (mediaId === null || mediaId === undefined) {
            throw new Error('Required parameter mediaId was null or undefined when calling updateMedia.');
        }
        if (mediaRequestDTO === null || mediaRequestDTO === undefined) {
            throw new Error('Required parameter mediaRequestDTO was null or undefined when calling updateMedia.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/${this.configuration.encodeParam({name: "mediaId", value: mediaId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseMediaDTO>('put', `${basePath}${localVarPath}`,
            {
                body: mediaRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Upload Image For Media Record
     * This API upload an image file to s3 bucket 
     * @param imageUploadRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public uploadImage(imageUploadRequestDTO: ImageUploadRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseImageUploadDTO>;
    public uploadImage(imageUploadRequestDTO: ImageUploadRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseImageUploadDTO>>;
    public uploadImage(imageUploadRequestDTO: ImageUploadRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseImageUploadDTO>>;
    public uploadImage(imageUploadRequestDTO: ImageUploadRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (imageUploadRequestDTO === null || imageUploadRequestDTO === undefined) {
            throw new Error('Required parameter imageUploadRequestDTO was null or undefined when calling uploadImage.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/media/image-upload`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseImageUploadDTO>('post', `${basePath}${localVarPath}`,
            {
                body: imageUploadRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
