/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { ApiResponseListNotesResponseDTO } from '../model/apiResponseListNotesResponseDTO';
// @ts-ignore
import { ApiResponseNotesResponseDTO } from '../model/apiResponseNotesResponseDTO';
// @ts-ignore
import { ApiResponseNull } from '../model/apiResponseNull';
// @ts-ignore
import { NotesRequestDTO } from '../model/notesRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class NotesService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Delete a note by ID
     * Deletes the note corresponding to the given note ID. This is a soft delete operation. Any media linked to the note will also be soft deleted.
     * @param noteId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteNotes(noteId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNull>;
    public deleteNotes(noteId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNull>>;
    public deleteNotes(noteId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNull>>;
    public deleteNotes(noteId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (noteId === null || noteId === undefined) {
            throw new Error('Required parameter noteId was null or undefined when calling deleteNotes.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/notes/${this.configuration.encodeParam({name: "noteId", value: noteId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNull>('delete', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Notes by Parent ID and Parent Table
     * Retrieves a list of notes associated with the given parent ID and parent table type.
     * @param parentId 
     * @param parentTableId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getNotes(parentId: number, parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListNotesResponseDTO>;
    public getNotes(parentId: number, parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListNotesResponseDTO>>;
    public getNotes(parentId: number, parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListNotesResponseDTO>>;
    public getNotes(parentId: number, parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (parentId === null || parentId === undefined) {
            throw new Error('Required parameter parentId was null or undefined when calling getNotes.');
        }
        if (parentTableId === null || parentTableId === undefined) {
            throw new Error('Required parameter parentTableId was null or undefined when calling getNotes.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/notes/${this.configuration.encodeParam({name: "parentId", value: parentId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/${this.configuration.encodeParam({name: "parentTableId", value: parentTableId, in: "path", style: "simple", explode: false, dataType: "'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListNotesResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save a new note
     * Creates and saves a new note based on the provided request data.
     * @param notesRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public saveNotes(notesRequestDTO: NotesRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNotesResponseDTO>;
    public saveNotes(notesRequestDTO: NotesRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNotesResponseDTO>>;
    public saveNotes(notesRequestDTO: NotesRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNotesResponseDTO>>;
    public saveNotes(notesRequestDTO: NotesRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (notesRequestDTO === null || notesRequestDTO === undefined) {
            throw new Error('Required parameter notesRequestDTO was null or undefined when calling saveNotes.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/notes/`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNotesResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: notesRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update an existing note
     * Updates the details of an existing note identified by note ID using the provided request payload.
     * @param noteId 
     * @param notesRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updatesNotes(noteId: number, notesRequestDTO: NotesRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNotesResponseDTO>;
    public updatesNotes(noteId: number, notesRequestDTO: NotesRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNotesResponseDTO>>;
    public updatesNotes(noteId: number, notesRequestDTO: NotesRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNotesResponseDTO>>;
    public updatesNotes(noteId: number, notesRequestDTO: NotesRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (noteId === null || noteId === undefined) {
            throw new Error('Required parameter noteId was null or undefined when calling updatesNotes.');
        }
        if (notesRequestDTO === null || notesRequestDTO === undefined) {
            throw new Error('Required parameter notesRequestDTO was null or undefined when calling updatesNotes.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/notes/${this.configuration.encodeParam({name: "noteId", value: noteId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNotesResponseDTO>('put', `${basePath}${localVarPath}`,
            {
                body: notesRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
