/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { AdditionalAddressRequestDTO } from '../model/additionalAddressRequestDTO';
// @ts-ignore
import { ApiResponseAdditionalAddressDTO } from '../model/apiResponseAdditionalAddressDTO';
// @ts-ignore
import { ApiResponseECRESearchResponseDTO } from '../model/apiResponseECRESearchResponseDTO';
// @ts-ignore
import { ApiResponseListAdditionalAddressDTO } from '../model/apiResponseListAdditionalAddressDTO';
// @ts-ignore
import { ApiResponseListBuildingFootPrintDTO } from '../model/apiResponseListBuildingFootPrintDTO';
// @ts-ignore
import { ApiResponseListFullStrataDetailResponseDTO } from '../model/apiResponseListFullStrataDetailResponseDTO';
// @ts-ignore
import { ApiResponseListMasterPropertiesDTO } from '../model/apiResponseListMasterPropertiesDTO';
// @ts-ignore
import { ApiResponseListParcelPropertyDTO } from '../model/apiResponseListParcelPropertyDTO';
// @ts-ignore
import { ApiResponseListPolygonShapesResponseDTO } from '../model/apiResponseListPolygonShapesResponseDTO';
// @ts-ignore
import { ApiResponseListPropertyElasticSearchResponseDTO } from '../model/apiResponseListPropertyElasticSearchResponseDTO';
// @ts-ignore
import { ApiResponseListPropertyIdResponseDTO } from '../model/apiResponseListPropertyIdResponseDTO';
// @ts-ignore
import { ApiResponseListPropertyIntersectResponseDTO } from '../model/apiResponseListPropertyIntersectResponseDTO';
// @ts-ignore
import { ApiResponseListPropertyMapSearchSizeResponseDTO } from '../model/apiResponseListPropertyMapSearchSizeResponseDTO';
// @ts-ignore
import { ApiResponseListPropertyStrataDetailsDTO } from '../model/apiResponseListPropertyStrataDetailsDTO';
// @ts-ignore
import { ApiResponseListPropertyStrataRelationshipResponseDTO } from '../model/apiResponseListPropertyStrataRelationshipResponseDTO';
// @ts-ignore
import { ApiResponseNull } from '../model/apiResponseNull';
// @ts-ignore
import { ApiResponseParcelPropertyDTO } from '../model/apiResponseParcelPropertyDTO';
// @ts-ignore
import { ApiResponsePropertyDetailsSizeResponseDTO } from '../model/apiResponsePropertyDetailsSizeResponseDTO';
// @ts-ignore
import { ApiResponsePropertyGridSearchResponseDTO } from '../model/apiResponsePropertyGridSearchResponseDTO';
// @ts-ignore
import { ApiResponsePropertyResponseDTO } from '../model/apiResponsePropertyResponseDTO';
// @ts-ignore
import { ApiResponsePropertySearchResponseDTO } from '../model/apiResponsePropertySearchResponseDTO';
// @ts-ignore
import { ApiResponseString } from '../model/apiResponseString';
// @ts-ignore
import { ApiResponseVoid } from '../model/apiResponseVoid';
// @ts-ignore
import { BuildingFootPrintDeleteDTO } from '../model/buildingFootPrintDeleteDTO';
// @ts-ignore
import { BuildingFootprintCollectionRequestDTO } from '../model/buildingFootprintCollectionRequestDTO';
// @ts-ignore
import { MultipleChildRequestDTO } from '../model/multipleChildRequestDTO';
// @ts-ignore
import { ParcelPropertyRequestDTO } from '../model/parcelPropertyRequestDTO';
// @ts-ignore
import { ProductActivityLogRequestDTO } from '../model/productActivityLogRequestDTO';
// @ts-ignore
import { PropertyDetailsDTO } from '../model/propertyDetailsDTO';
// @ts-ignore
import { PropertyIntersectRequestDTO } from '../model/propertyIntersectRequestDTO';
// @ts-ignore
import { PropertyMapSearchRequestDTO } from '../model/propertyMapSearchRequestDTO';
// @ts-ignore
import { PropertySearchDetailsRequestDTO } from '../model/propertySearchDetailsRequestDTO';
// @ts-ignore
import { PropertySearchECRERequestDTO } from '../model/propertySearchECRERequestDTO';
// @ts-ignore
import { PropertySearchRequestDTO } from '../model/propertySearchRequestDTO';
// @ts-ignore
import { PropertyStrataRelationshipRequestDTO } from '../model/propertyStrataRelationshipRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class PropertyService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Create a Parcel
     * This API creates a new Parcel if the ParcelID is not provided
     * @param propertyId 
     * @param parcelPropertyRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createParcel(propertyId: number, parcelPropertyRequestDTO: Array<ParcelPropertyRequestDTO>, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListParcelPropertyDTO>;
    public createParcel(propertyId: number, parcelPropertyRequestDTO: Array<ParcelPropertyRequestDTO>, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListParcelPropertyDTO>>;
    public createParcel(propertyId: number, parcelPropertyRequestDTO: Array<ParcelPropertyRequestDTO>, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListParcelPropertyDTO>>;
    public createParcel(propertyId: number, parcelPropertyRequestDTO: Array<ParcelPropertyRequestDTO>, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling createParcel.');
        }
        if (parcelPropertyRequestDTO === null || parcelPropertyRequestDTO === undefined) {
            throw new Error('Required parameter parcelPropertyRequestDTO was null or undefined when calling createParcel.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/parcels`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListParcelPropertyDTO>('post', `${basePath}${localVarPath}`,
            {
                body: parcelPropertyRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a new Property
     * Creates a new property using the provided property data. Returns the created property details.
     * @param propertyDetailsDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponsePropertyResponseDTO>;
    public createProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponsePropertyResponseDTO>>;
    public createProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponsePropertyResponseDTO>>;
    public createProperty(propertyDetailsDTO: PropertyDetailsDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyDetailsDTO === null || propertyDetailsDTO === undefined) {
            throw new Error('Required parameter propertyDetailsDTO was null or undefined when calling createProperty.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponsePropertyResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertyDetailsDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete an Additional Address
     * Performs a soft delete of the specified additional address by setting its active status to false. Also updates the modified date and modified by fields for audit purposes.
     * @param additionalAddressId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteAdditionalAddress(additionalAddressId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNull>;
    public deleteAdditionalAddress(additionalAddressId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNull>>;
    public deleteAdditionalAddress(additionalAddressId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNull>>;
    public deleteAdditionalAddress(additionalAddressId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (additionalAddressId === null || additionalAddressId === undefined) {
            throw new Error('Required parameter additionalAddressId was null or undefined when calling deleteAdditionalAddress.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/additional-address/${this.configuration.encodeParam({name: "additionalAddressId", value: additionalAddressId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNull>('delete', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param buildingFootPrintDeleteDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteBuildingFootprints(buildingFootPrintDeleteDTO: BuildingFootPrintDeleteDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseVoid>;
    public deleteBuildingFootprints(buildingFootPrintDeleteDTO: BuildingFootPrintDeleteDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseVoid>>;
    public deleteBuildingFootprints(buildingFootPrintDeleteDTO: BuildingFootPrintDeleteDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseVoid>>;
    public deleteBuildingFootprints(buildingFootPrintDeleteDTO: BuildingFootPrintDeleteDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (buildingFootPrintDeleteDTO === null || buildingFootPrintDeleteDTO === undefined) {
            throw new Error('Required parameter buildingFootPrintDeleteDTO was null or undefined when calling deleteBuildingFootprints.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/building-footprints/delete`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseVoid>('delete', `${basePath}${localVarPath}`,
            {
                body: buildingFootPrintDeleteDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete a Parcel
     * Deletes a specific parcel associated with the given property ID. This operation is irreversible. Make sure the ParcelID is correct before proceeding.
     * @param propertyId 
     * @param parcelId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteParcel(propertyId: number, parcelId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseNull>;
    public deleteParcel(propertyId: number, parcelId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseNull>>;
    public deleteParcel(propertyId: number, parcelId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseNull>>;
    public deleteParcel(propertyId: number, parcelId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling deleteParcel.');
        }
        if (parcelId === null || parcelId === undefined) {
            throw new Error('Required parameter parcelId was null or undefined when calling deleteParcel.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/parcels/${this.configuration.encodeParam({name: "parcelId", value: parcelId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseNull>('delete', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Property Details
     * Retrieves detailed information about a property based on its ID
     * @param propertyId PropertyId to fetch details for
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public findPropertyDetailsByPropertyID(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponsePropertyDetailsSizeResponseDTO>;
    public findPropertyDetailsByPropertyID(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponsePropertyDetailsSizeResponseDTO>>;
    public findPropertyDetailsByPropertyID(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponsePropertyDetailsSizeResponseDTO>>;
    public findPropertyDetailsByPropertyID(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling findPropertyDetailsByPropertyID.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/details`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponsePropertyDetailsSizeResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Additional Addresses for a Property
     * Retrieves a list of additional address records associated with a given property ID. Excludes the primary (sequence 1) address and returns only active additional addresses.
     * @param propertyId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAdditionalAddress(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListAdditionalAddressDTO>;
    public getAdditionalAddress(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListAdditionalAddressDTO>>;
    public getAdditionalAddress(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListAdditionalAddressDTO>>;
    public getAdditionalAddress(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getAdditionalAddress.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/additional-address`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListAdditionalAddressDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Fetch Building Footprints by Property ID
     * Retrieves all building footprint details associated with the specified property ID. Returns a list of building footprints for the given property ID. If no footprints are found, an empty list is returned.
     * @param propertyId ID of the property to fetch building footprints for
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getBuildingFootprintByPropertyId(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListBuildingFootPrintDTO>;
    public getBuildingFootprintByPropertyId(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListBuildingFootPrintDTO>>;
    public getBuildingFootprintByPropertyId(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListBuildingFootPrintDTO>>;
    public getBuildingFootprintByPropertyId(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getBuildingFootprintByPropertyId.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/building-footprints`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListBuildingFootPrintDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param propertyId 
     * @param sortBy 
     * @param sortDirection 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getDetailedLinkedProperties(propertyId: number, sortBy?: 'STRATA_TYPE' | 'ADDRESS' | 'STRATA_UNIT' | 'BUILDING_SF' | 'LOT_SIZE_SF' | 'BUILDING_SIZE_SM' | 'LOT_SIZE_SM' | 'LAST_SALE_DATE' | 'LAST_SALE_PRICE' | 'PARCEL_NUMBERS' | 'PROPERTY_ID', sortDirection?: 'ASCENDING' | 'DESCENDING', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListFullStrataDetailResponseDTO>;
    public getDetailedLinkedProperties(propertyId: number, sortBy?: 'STRATA_TYPE' | 'ADDRESS' | 'STRATA_UNIT' | 'BUILDING_SF' | 'LOT_SIZE_SF' | 'BUILDING_SIZE_SM' | 'LOT_SIZE_SM' | 'LAST_SALE_DATE' | 'LAST_SALE_PRICE' | 'PARCEL_NUMBERS' | 'PROPERTY_ID', sortDirection?: 'ASCENDING' | 'DESCENDING', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListFullStrataDetailResponseDTO>>;
    public getDetailedLinkedProperties(propertyId: number, sortBy?: 'STRATA_TYPE' | 'ADDRESS' | 'STRATA_UNIT' | 'BUILDING_SF' | 'LOT_SIZE_SF' | 'BUILDING_SIZE_SM' | 'LOT_SIZE_SM' | 'LAST_SALE_DATE' | 'LAST_SALE_PRICE' | 'PARCEL_NUMBERS' | 'PROPERTY_ID', sortDirection?: 'ASCENDING' | 'DESCENDING', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListFullStrataDetailResponseDTO>>;
    public getDetailedLinkedProperties(propertyId: number, sortBy?: 'STRATA_TYPE' | 'ADDRESS' | 'STRATA_UNIT' | 'BUILDING_SF' | 'LOT_SIZE_SF' | 'BUILDING_SIZE_SM' | 'LOT_SIZE_SM' | 'LAST_SALE_DATE' | 'LAST_SALE_PRICE' | 'PARCEL_NUMBERS' | 'PROPERTY_ID', sortDirection?: 'ASCENDING' | 'DESCENDING', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getDetailedLinkedProperties.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>sortBy, 'sortBy');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>sortDirection, 'sortDirection');

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/get-detailed-linked-properties/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListFullStrataDetailResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get strata details for a property
     * Fetches strata-related details for a given property ID, which can be either a master or a child property. If the provided property is a child, the associated master property and all its related strata properties are returned. Validates the property\&#39;s condo type and ensures only active relationships are considered.
     * @param propertyId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getLinkedPropertyDetails(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyStrataDetailsDTO>;
    public getLinkedPropertyDetails(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyStrataDetailsDTO>>;
    public getLinkedPropertyDetails(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyStrataDetailsDTO>>;
    public getLinkedPropertyDetails(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getLinkedPropertyDetails.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/get-linked-properties/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyStrataDetailsDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Fetch Parcels by Property ID
     * Retrieves all parcel details associated with the specified property ID. If no parcels are found, an empty list is returned
     * @param propertyId ID of the property to fetch parcels for
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getParcelByProperty(propertyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListParcelPropertyDTO>;
    public getParcelByProperty(propertyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListParcelPropertyDTO>>;
    public getParcelByProperty(propertyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListParcelPropertyDTO>>;
    public getParcelByProperty(propertyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling getParcelByProperty.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/parcels`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListParcelPropertyDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get polygon shapes for a given company
     * Returns the polygon shapes associated with the specified company ID.
     * @param companyId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getPolygonShapesByCompanyId(companyId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPolygonShapesResponseDTO>;
    public getPolygonShapesByCompanyId(companyId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPolygonShapesResponseDTO>>;
    public getPolygonShapesByCompanyId(companyId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPolygonShapesResponseDTO>>;
    public getPolygonShapesByCompanyId(companyId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (companyId === null || companyId === undefined) {
            throw new Error('Required parameter companyId was null or undefined when calling getPolygonShapesByCompanyId.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/polygon-shapes/${this.configuration.encodeParam({name: "companyId", value: companyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPolygonShapesResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get properties by search
     * Fetches properties for a given filters
     * @param propertySearchECRERequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getPropertiesByECRESearch(propertySearchECRERequestDTO: PropertySearchECRERequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseECRESearchResponseDTO>;
    public getPropertiesByECRESearch(propertySearchECRERequestDTO: PropertySearchECRERequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseECRESearchResponseDTO>>;
    public getPropertiesByECRESearch(propertySearchECRERequestDTO: PropertySearchECRERequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseECRESearchResponseDTO>>;
    public getPropertiesByECRESearch(propertySearchECRERequestDTO: PropertySearchECRERequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertySearchECRERequestDTO === null || propertySearchECRERequestDTO === undefined) {
            throw new Error('Required parameter propertySearchECRERequestDTO was null or undefined when calling getPropertiesByECRESearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/ecre/search`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseECRESearchResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertySearchECRERequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get properties by search
     * Fetches properties for a given filters
     * @param propertyMapSearchRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getPropertiesByMapSearch(propertyMapSearchRequestDTO: PropertyMapSearchRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyMapSearchSizeResponseDTO>;
    public getPropertiesByMapSearch(propertyMapSearchRequestDTO: PropertyMapSearchRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyMapSearchSizeResponseDTO>>;
    public getPropertiesByMapSearch(propertyMapSearchRequestDTO: PropertyMapSearchRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyMapSearchSizeResponseDTO>>;
    public getPropertiesByMapSearch(propertyMapSearchRequestDTO: PropertyMapSearchRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyMapSearchRequestDTO === null || propertyMapSearchRequestDTO === undefined) {
            throw new Error('Required parameter propertyMapSearchRequestDTO was null or undefined when calling getPropertiesByMapSearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/mapsearch`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyMapSearchSizeResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertyMapSearchRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get properties by search
     * Fetches properties for a given filters
     * @param propertySearchRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getPropertiesBySearch(propertySearchRequestDTO: PropertySearchRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponsePropertySearchResponseDTO>;
    public getPropertiesBySearch(propertySearchRequestDTO: PropertySearchRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponsePropertySearchResponseDTO>>;
    public getPropertiesBySearch(propertySearchRequestDTO: PropertySearchRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponsePropertySearchResponseDTO>>;
    public getPropertiesBySearch(propertySearchRequestDTO: PropertySearchRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertySearchRequestDTO === null || propertySearchRequestDTO === undefined) {
            throw new Error('Required parameter propertySearchRequestDTO was null or undefined when calling getPropertiesBySearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/search`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponsePropertySearchResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertySearchRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Market and SubMarket by Property or Coordinates
     * Retrieves Market and SubMarket details for a given Property ID or Latitude/Longitude coordinates, filtered by UseTypeID. Returns a list of Market and SubMarket intersections.
     * @param propertyIntersectRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getPropertyIntersect(propertyIntersectRequestDTO: PropertyIntersectRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyIntersectResponseDTO>;
    public getPropertyIntersect(propertyIntersectRequestDTO: PropertyIntersectRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyIntersectResponseDTO>>;
    public getPropertyIntersect(propertyIntersectRequestDTO: PropertyIntersectRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyIntersectResponseDTO>>;
    public getPropertyIntersect(propertyIntersectRequestDTO: PropertyIntersectRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyIntersectRequestDTO === null || propertyIntersectRequestDTO === undefined) {
            throw new Error('Required parameter propertyIntersectRequestDTO was null or undefined when calling getPropertyIntersect.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/intersect-market-submarket/`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyIntersectResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertyIntersectRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Link child pids to master
     * Establishes a relationship between a master property and one or more child properties. Validates property types before linking and ensures existing relationships are deactivated if the child property is already linked to a different master. Only active, valid relationships are returned.
     * @param propertyStrataRelationshipRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public linkChildToMaster(propertyStrataRelationshipRequestDTO: PropertyStrataRelationshipRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyStrataRelationshipResponseDTO>;
    public linkChildToMaster(propertyStrataRelationshipRequestDTO: PropertyStrataRelationshipRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyStrataRelationshipResponseDTO>>;
    public linkChildToMaster(propertyStrataRelationshipRequestDTO: PropertyStrataRelationshipRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyStrataRelationshipResponseDTO>>;
    public linkChildToMaster(propertyStrataRelationshipRequestDTO: PropertyStrataRelationshipRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyStrataRelationshipRequestDTO === null || propertyStrataRelationshipRequestDTO === undefined) {
            throw new Error('Required parameter propertyStrataRelationshipRequestDTO was null or undefined when calling linkChildToMaster.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/link-child-to-master`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyStrataRelationshipResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertyStrataRelationshipRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get master properties by search
     * Fetch master properties for a given search text and strata type
     * @param searchText 
     * @param strataType 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public masterPropertiesSearch(searchText: string, strataType: 'NOT_STRATA' | 'STRATA' | 'MASTER_STRATA_RECORD' | 'MASTER_FREEHOLD' | 'CHILD_FREEHOLD', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListMasterPropertiesDTO>;
    public masterPropertiesSearch(searchText: string, strataType: 'NOT_STRATA' | 'STRATA' | 'MASTER_STRATA_RECORD' | 'MASTER_FREEHOLD' | 'CHILD_FREEHOLD', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListMasterPropertiesDTO>>;
    public masterPropertiesSearch(searchText: string, strataType: 'NOT_STRATA' | 'STRATA' | 'MASTER_STRATA_RECORD' | 'MASTER_FREEHOLD' | 'CHILD_FREEHOLD', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListMasterPropertiesDTO>>;
    public masterPropertiesSearch(searchText: string, strataType: 'NOT_STRATA' | 'STRATA' | 'MASTER_STRATA_RECORD' | 'MASTER_FREEHOLD' | 'CHILD_FREEHOLD', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (searchText === null || searchText === undefined) {
            throw new Error('Required parameter searchText was null or undefined when calling masterPropertiesSearch.');
        }
        if (strataType === null || strataType === undefined) {
            throw new Error('Required parameter strataType was null or undefined when calling masterPropertiesSearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/get-master-properties/${this.configuration.encodeParam({name: "searchText", value: searchText, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/${this.configuration.encodeParam({name: "strataType", value: strataType, in: "path", style: "simple", explode: false, dataType: "'NOT_STRATA' | 'STRATA' | 'MASTER_STRATA_RECORD' | 'MASTER_FREEHOLD' | 'CHILD_FREEHOLD'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListMasterPropertiesDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Auto Search Properties
     * Performs an auto-complete search for properties based on the provided text and state ID. Returns a list of matching properties.
     * @param text 
     * @param stateId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public propertyAutoSearch(text: string, stateId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyElasticSearchResponseDTO>;
    public propertyAutoSearch(text: string, stateId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyElasticSearchResponseDTO>>;
    public propertyAutoSearch(text: string, stateId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyElasticSearchResponseDTO>>;
    public propertyAutoSearch(text: string, stateId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (text === null || text === undefined) {
            throw new Error('Required parameter text was null or undefined when calling propertyAutoSearch.');
        }
        if (stateId === null || stateId === undefined) {
            throw new Error('Required parameter stateId was null or undefined when calling propertyAutoSearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/property-auto-search/${this.configuration.encodeParam({name: "text", value: text, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/${this.configuration.encodeParam({name: "stateId", value: stateId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyElasticSearchResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save a New Additional Address
     * Creates a new additional address for the specified property. The sequence number is auto-generated, and inherited fields like countryId and locationId are copied from the property\&#39;s primary address if available. Returns the updated list of additional addresses.
     * @param additionalAddressRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public saveAdditionalAddress(additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseAdditionalAddressDTO>;
    public saveAdditionalAddress(additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseAdditionalAddressDTO>>;
    public saveAdditionalAddress(additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseAdditionalAddressDTO>>;
    public saveAdditionalAddress(additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (additionalAddressRequestDTO === null || additionalAddressRequestDTO === undefined) {
            throw new Error('Required parameter additionalAddressRequestDTO was null or undefined when calling saveAdditionalAddress.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/additional-address`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseAdditionalAddressDTO>('post', `${basePath}${localVarPath}`,
            {
                body: additionalAddressRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save or Update Building Footprints
     * Creates new or updates existing building footprints associated with a property. If a &#x60;BuildingFootPrintId&#x60; is provided, the corresponding footprint will be updated. If not, a new footprint will be created. Returns the updated list of footprints for the given property ID.
     * @param buildingFootprintCollectionRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public saveBuildingFootprints(buildingFootprintCollectionRequestDTO: BuildingFootprintCollectionRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListBuildingFootPrintDTO>;
    public saveBuildingFootprints(buildingFootprintCollectionRequestDTO: BuildingFootprintCollectionRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListBuildingFootPrintDTO>>;
    public saveBuildingFootprints(buildingFootprintCollectionRequestDTO: BuildingFootprintCollectionRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListBuildingFootPrintDTO>>;
    public saveBuildingFootprints(buildingFootprintCollectionRequestDTO: BuildingFootprintCollectionRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (buildingFootprintCollectionRequestDTO === null || buildingFootprintCollectionRequestDTO === undefined) {
            throw new Error('Required parameter buildingFootprintCollectionRequestDTO was null or undefined when calling saveBuildingFootprints.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/building-footprints`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListBuildingFootPrintDTO>('post', `${basePath}${localVarPath}`,
            {
                body: buildingFootprintCollectionRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save Property Activity Log
     * Saves an activity log entry associated with a property and related entities.
     * @param productActivityLogRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public savePropertyActivityLog(productActivityLogRequestDTO: ProductActivityLogRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseString>;
    public savePropertyActivityLog(productActivityLogRequestDTO: ProductActivityLogRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseString>>;
    public savePropertyActivityLog(productActivityLogRequestDTO: ProductActivityLogRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseString>>;
    public savePropertyActivityLog(productActivityLogRequestDTO: ProductActivityLogRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (productActivityLogRequestDTO === null || productActivityLogRequestDTO === undefined) {
            throw new Error('Required parameter productActivityLogRequestDTO was null or undefined when calling savePropertyActivityLog.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/product-activity-log-save`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseString>('post', `${basePath}${localVarPath}`,
            {
                body: productActivityLogRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save Multiple Strata/Freehold
     * Add multiple strata and freehold
     * @param multipleChildRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public savePropertyMultiStrata(multipleChildRequestDTO: MultipleChildRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListPropertyIdResponseDTO>;
    public savePropertyMultiStrata(multipleChildRequestDTO: MultipleChildRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListPropertyIdResponseDTO>>;
    public savePropertyMultiStrata(multipleChildRequestDTO: MultipleChildRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListPropertyIdResponseDTO>>;
    public savePropertyMultiStrata(multipleChildRequestDTO: MultipleChildRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (multipleChildRequestDTO === null || multipleChildRequestDTO === undefined) {
            throw new Error('Required parameter multipleChildRequestDTO was null or undefined when calling savePropertyMultiStrata.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/multi-children-to-master`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListPropertyIdResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: multipleChildRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get Property search details for grid view
     * Returns a list of property search results based on the listingtype and useractivity
     * @param propertySearchDetailsRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public searchProperties(propertySearchDetailsRequestDTO: PropertySearchDetailsRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponsePropertyGridSearchResponseDTO>;
    public searchProperties(propertySearchDetailsRequestDTO: PropertySearchDetailsRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponsePropertyGridSearchResponseDTO>>;
    public searchProperties(propertySearchDetailsRequestDTO: PropertySearchDetailsRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponsePropertyGridSearchResponseDTO>>;
    public searchProperties(propertySearchDetailsRequestDTO: PropertySearchDetailsRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertySearchDetailsRequestDTO === null || propertySearchDetailsRequestDTO === undefined) {
            throw new Error('Required parameter propertySearchDetailsRequestDTO was null or undefined when calling searchProperties.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/ecre/search/details`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponsePropertyGridSearchResponseDTO>('post', `${basePath}${localVarPath}`,
            {
                body: propertySearchDetailsRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update an Existing Additional Address
     * Updates the details of an existing additional address using the provided address ID. Also regenerates the formatted address text and updates audit fields such as modified date and user.
     * @param additionalAddressId 
     * @param additionalAddressRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateAdditionalAddress(additionalAddressId: number, additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseAdditionalAddressDTO>;
    public updateAdditionalAddress(additionalAddressId: number, additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseAdditionalAddressDTO>>;
    public updateAdditionalAddress(additionalAddressId: number, additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseAdditionalAddressDTO>>;
    public updateAdditionalAddress(additionalAddressId: number, additionalAddressRequestDTO: AdditionalAddressRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (additionalAddressId === null || additionalAddressId === undefined) {
            throw new Error('Required parameter additionalAddressId was null or undefined when calling updateAdditionalAddress.');
        }
        if (additionalAddressRequestDTO === null || additionalAddressRequestDTO === undefined) {
            throw new Error('Required parameter additionalAddressRequestDTO was null or undefined when calling updateAdditionalAddress.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/additional-address/${this.configuration.encodeParam({name: "additionalAddressId", value: additionalAddressId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseAdditionalAddressDTO>('put', `${basePath}${localVarPath}`,
            {
                body: additionalAddressRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a Parcel
     * This API updates an existing Parcel if the ParcelID and PropertyID is present in the request body.
     * @param propertyId 
     * @param parcelId 
     * @param parcelPropertyRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateParcel(propertyId: number, parcelId: number, parcelPropertyRequestDTO: ParcelPropertyRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseParcelPropertyDTO>;
    public updateParcel(propertyId: number, parcelId: number, parcelPropertyRequestDTO: ParcelPropertyRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseParcelPropertyDTO>>;
    public updateParcel(propertyId: number, parcelId: number, parcelPropertyRequestDTO: ParcelPropertyRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseParcelPropertyDTO>>;
    public updateParcel(propertyId: number, parcelId: number, parcelPropertyRequestDTO: ParcelPropertyRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyId === null || propertyId === undefined) {
            throw new Error('Required parameter propertyId was null or undefined when calling updateParcel.');
        }
        if (parcelId === null || parcelId === undefined) {
            throw new Error('Required parameter parcelId was null or undefined when calling updateParcel.');
        }
        if (parcelPropertyRequestDTO === null || parcelPropertyRequestDTO === undefined) {
            throw new Error('Required parameter parcelPropertyRequestDTO was null or undefined when calling updateParcel.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property/${this.configuration.encodeParam({name: "propertyId", value: propertyId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}/parcels/${this.configuration.encodeParam({name: "parcelId", value: parcelId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseParcelPropertyDTO>('put', `${basePath}${localVarPath}`,
            {
                body: parcelPropertyRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update an existing Property
     * Updates the property details for the given property. The property must exist; otherwise, an error will be returned. Returns the updated property details.
     * @param propertyDetailsDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponsePropertyResponseDTO>;
    public updateProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponsePropertyResponseDTO>>;
    public updateProperty(propertyDetailsDTO: PropertyDetailsDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponsePropertyResponseDTO>>;
    public updateProperty(propertyDetailsDTO: PropertyDetailsDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (propertyDetailsDTO === null || propertyDetailsDTO === undefined) {
            throw new Error('Required parameter propertyDetailsDTO was null or undefined when calling updateProperty.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/property`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponsePropertyResponseDTO>('put', `${basePath}${localVarPath}`,
            {
                body: propertyDetailsDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
