/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { ApiResponseInteger } from '../model/apiResponseInteger';
// @ts-ignore
import { ApiResponseListSavedSearchFieldsResponseDTO } from '../model/apiResponseListSavedSearchFieldsResponseDTO';
// @ts-ignore
import { SavedSearchRequestDTO } from '../model/savedSearchRequestDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class SavedSearchService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * @param parentTableId 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getSavedSearchFields(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseListSavedSearchFieldsResponseDTO>;
    public getSavedSearchFields(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseListSavedSearchFieldsResponseDTO>>;
    public getSavedSearchFields(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseListSavedSearchFieldsResponseDTO>>;
    public getSavedSearchFields(parentTableId: 'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification', observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (parentTableId === null || parentTableId === undefined) {
            throw new Error('Required parameter parentTableId was null or undefined when calling getSavedSearchFields.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/saved-search/get-fields/${this.configuration.encodeParam({name: "parentTableId", value: parentTableId, in: "path", style: "simple", explode: false, dataType: "'Property' | 'Listing' | 'Suite' | 'Parcel' | 'Company' | 'Branch' | 'ContactRole' | 'Person' | 'Sale' | 'Lease' | 'Address' | 'AdditionalUse' | 'AdditionalAddress' | 'PropertyAllocation' | 'SalePriceConfirmation' | 'SaleLoanInfo' | 'SellerContact' | 'BuyerContact' | 'LevelSpacesBreakdown' | 'ExtensionsAndOptions' | 'ReviewsAndRentEscalations' | 'Media' | 'KeyContact' | 'TenantVerification' | 'Tenant' | 'DashBoard' | 'MarketBrief' | 'BranchDataFeed' | 'PropertyOwner' | 'ProductUpdate' | 'ListingVerification' | 'SuiteVerification' | 'LeaseVerification' | 'SaleVerification'", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseListSavedSearchFieldsResponseDTO>('get', `${basePath}${localVarPath}`,
            {
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Save the SavedSearch
     * Saves the All the details of the saved Search like Criteria and result etc
     * @param savedSearchRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public saveDetailsofSavedSearch(savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseInteger>;
    public saveDetailsofSavedSearch(savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseInteger>>;
    public saveDetailsofSavedSearch(savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseInteger>>;
    public saveDetailsofSavedSearch(savedSearchRequestDTO: SavedSearchRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (savedSearchRequestDTO === null || savedSearchRequestDTO === undefined) {
            throw new Error('Required parameter savedSearchRequestDTO was null or undefined when calling saveDetailsofSavedSearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/saved-search/save`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseInteger>('post', `${basePath}${localVarPath}`,
            {
                body: savedSearchRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update the SavedSearch
     * Updates the SavedSearch Criteria and result etc of the given savedSearchId
     * @param searchId 
     * @param savedSearchRequestDTO 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateDetailsOfSavedSearch(searchId: number, savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<ApiResponseInteger>;
    public updateDetailsOfSavedSearch(searchId: number, savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpResponse<ApiResponseInteger>>;
    public updateDetailsOfSavedSearch(searchId: number, savedSearchRequestDTO: SavedSearchRequestDTO, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json',}): Observable<HttpEvent<ApiResponseInteger>>;
    public updateDetailsOfSavedSearch(searchId: number, savedSearchRequestDTO: SavedSearchRequestDTO, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json',}): Observable<any> {
        if (searchId === null || searchId === undefined) {
            throw new Error('Required parameter searchId was null or undefined when calling updateDetailsOfSavedSearch.');
        }
        if (savedSearchRequestDTO === null || savedSearchRequestDTO === undefined) {
            throw new Error('Required parameter savedSearchRequestDTO was null or undefined when calling updateDetailsOfSavedSearch.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (bearer-jwt) required
        localVarHeaders = this.configuration.addCredentialToHeaders('bearer-jwt', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }



        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/saved-search/update/${this.configuration.encodeParam({name: "searchId", value: searchId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: "int32"})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ApiResponseInteger>('put', `${basePath}${localVarPath}`,
            {
                body: savedSearchRequestDTO,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
