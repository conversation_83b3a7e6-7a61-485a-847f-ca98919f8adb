/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface AdditionalAddressDTO { 
    AddressId?: number;
    AddressTypeId?: AdditionalAddressDTO.AddressTypeIdEnum;
    AddressStreetNumber?: string;
    AddressStreetName?: string;
    AddressText?: string;
    ZipCode?: string;
    ParentTableId?: AdditionalAddressDTO.ParentTableIdEnum;
    Sequence?: number;
    StreetSuffix1?: number;
    StreetSuffix2?: number;
    Zip4?: string;
    FloorNumber?: string;
    CountryName?: string;
    CountryCode?: string;
    Address1?: string;
    Address2?: string;
    StreetNumberMin?: string;
    StreetNumberMax?: string;
    StreetPrefix1?: AdditionalAddressDTO.StreetPrefix1Enum;
    StreetPrefix2?: AdditionalAddressDTO.StreetPrefix2Enum;
    BuildingNumber?: string;
    ComplexName?: string;
    PrimaryStreet?: string;
    PrimaryAccess?: string;
    PrimaryTrafficCount?: string;
    PrimaryTrafficCountDate?: string;
    PrimaryFrontage?: string;
    SecondaryStreet?: string;
    SecondaryAccess?: string;
    SecondaryTrafficCount?: string;
    SecondaryFrontage?: string;
    ParentId?: number;
    LocationId?: number;
    IsActive?: boolean;
    StateDisplayName?: string;
    CityDisplayName?: string;
    CreatedBy?: number;
    CreatedDate?: string;
    ModifiedBy?: number;
    ModifiedDate?: string;
    CityID?: number;
    City?: string;
    StateID?: number;
    StateCode?: string;
    State?: string;
    CountyID?: number;
    County?: string;
    CountryID?: number;
    Country?: string;
    EastWestStreet?: string;
    NorthSouthStreet?: string;
    Quadrant?: AdditionalAddressDTO.QuadrantEnum;
    PartOfComplex?: AdditionalAddressDTO.PartOfComplexEnum;
    SecTrafficCntDate?: string;
    AddressType?: boolean;
}
export namespace AdditionalAddressDTO {
    export const AddressTypeIdEnum = {
        Billing: 'BILLING',
        Physical: 'PHYSICAL',
        Mailing: 'MAILING'
    } as const;
    export type AddressTypeIdEnum = typeof AddressTypeIdEnum[keyof typeof AddressTypeIdEnum];
    export const ParentTableIdEnum = {
        Property: 'Property',
        Listing: 'Listing',
        Suite: 'Suite',
        Parcel: 'Parcel',
        Company: 'Company',
        Branch: 'Branch',
        ContactRole: 'ContactRole',
        Person: 'Person',
        Sale: 'Sale',
        Lease: 'Lease',
        Address: 'Address',
        AdditionalUse: 'AdditionalUse',
        AdditionalAddress: 'AdditionalAddress',
        PropertyAllocation: 'PropertyAllocation',
        SalePriceConfirmation: 'SalePriceConfirmation',
        SaleLoanInfo: 'SaleLoanInfo',
        SellerContact: 'SellerContact',
        BuyerContact: 'BuyerContact',
        LevelSpacesBreakdown: 'LevelSpacesBreakdown',
        ExtensionsAndOptions: 'ExtensionsAndOptions',
        ReviewsAndRentEscalations: 'ReviewsAndRentEscalations',
        Media: 'Media',
        KeyContact: 'KeyContact',
        TenantVerification: 'TenantVerification',
        Tenant: 'Tenant',
        DashBoard: 'DashBoard',
        MarketBrief: 'MarketBrief',
        BranchDataFeed: 'BranchDataFeed',
        PropertyOwner: 'PropertyOwner',
        ProductUpdate: 'ProductUpdate',
        ListingVerification: 'ListingVerification',
        SuiteVerification: 'SuiteVerification',
        LeaseVerification: 'LeaseVerification',
        SaleVerification: 'SaleVerification'
    } as const;
    export type ParentTableIdEnum = typeof ParentTableIdEnum[keyof typeof ParentTableIdEnum];
    export const StreetPrefix1Enum = {
        East: 'EAST',
        North: 'NORTH',
        Northeast: 'NORTHEAST',
        Northwest: 'NORTHWEST',
        South: 'SOUTH',
        Southeast: 'SOUTHEAST',
        Southwest: 'SOUTHWEST',
        West: 'WEST'
    } as const;
    export type StreetPrefix1Enum = typeof StreetPrefix1Enum[keyof typeof StreetPrefix1Enum];
    export const StreetPrefix2Enum = {
        East: 'EAST',
        North: 'NORTH',
        Northeast: 'NORTHEAST',
        Northwest: 'NORTHWEST',
        South: 'SOUTH',
        Southeast: 'SOUTHEAST',
        Southwest: 'SOUTHWEST',
        West: 'WEST'
    } as const;
    export type StreetPrefix2Enum = typeof StreetPrefix2Enum[keyof typeof StreetPrefix2Enum];
    export const QuadrantEnum = {
        Nw: 'NW',
        Ne: 'NE',
        Sw: 'SW',
        Se: 'SE'
    } as const;
    export type QuadrantEnum = typeof QuadrantEnum[keyof typeof QuadrantEnum];
    export const PartOfComplexEnum = {
        NotPartOfComplex: 'NOT_PART_OF_COMPLEX',
        PartOfComplex: 'PART_OF_COMPLEX'
    } as const;
    export type PartOfComplexEnum = typeof PartOfComplexEnum[keyof typeof PartOfComplexEnum];
}


