/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface AdditionalAddressRequestDTO { 
    AddressStreetName?: string;
    AddressType?: boolean;
    BuildingNumber?: string;
    CityId?: number;
    CountryId?: number;
    CountyId?: number;
    EastWestSt?: string;
    IsActive?: boolean;
    NorthSouthSt?: string;
    StreetPrefix1?: AdditionalAddressRequestDTO.StreetPrefix1Enum;
    StreetPrefix2?: AdditionalAddressRequestDTO.StreetPrefix2Enum;
    ParentId?: number;
    QuadrantId?: AdditionalAddressRequestDTO.QuadrantIdEnum;
    StateId?: number;
    StreetNumberMin?: number;
    StreetNumberMax?: number;
    StreetSuffix1?: number;
    StreetSuffix2?: number;
    ZipCode?: number;
}
export namespace AdditionalAddressRequestDTO {
    export const StreetPrefix1Enum = {
        East: 'EAST',
        North: 'NORTH',
        Northeast: 'NORTHEAST',
        Northwest: 'NORTHWEST',
        South: 'SOUTH',
        Southeast: 'SOUTHEAST',
        Southwest: 'SOUTHWEST',
        West: 'WEST'
    } as const;
    export type StreetPrefix1Enum = typeof StreetPrefix1Enum[keyof typeof StreetPrefix1Enum];
    export const StreetPrefix2Enum = {
        East: 'EAST',
        North: 'NORTH',
        Northeast: 'NORTHEAST',
        Northwest: 'NORTHWEST',
        South: 'SOUTH',
        Southeast: 'SOUTHEAST',
        Southwest: 'SOUTHWEST',
        West: 'WEST'
    } as const;
    export type StreetPrefix2Enum = typeof StreetPrefix2Enum[keyof typeof StreetPrefix2Enum];
    export const QuadrantIdEnum = {
        Nw: 'NW',
        Ne: 'NE',
        Sw: 'SW',
        Se: 'SE'
    } as const;
    export type QuadrantIdEnum = typeof QuadrantIdEnum[keyof typeof QuadrantIdEnum];
}


