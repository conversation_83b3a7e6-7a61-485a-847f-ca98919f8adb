/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AdditionalAddressDTO } from './additionalAddressDTO';


export interface ApiResponseAdditionalAddressDTO { 
    error?: boolean;
    message?: string;
    responseData?: AdditionalAddressDTO;
    status?: number;
    totalCount?: object;
    userActivityID?: object;
}

