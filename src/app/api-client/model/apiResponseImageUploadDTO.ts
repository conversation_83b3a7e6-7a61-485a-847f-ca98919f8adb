/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ImageUploadDTO } from './imageUploadDTO';


export interface ApiResponseImageUploadDTO { 
    error?: boolean;
    message?: string;
    responseData?: ImageUploadDTO;
    status?: number;
    totalCount?: object;
    userActivityID?: object;
}

