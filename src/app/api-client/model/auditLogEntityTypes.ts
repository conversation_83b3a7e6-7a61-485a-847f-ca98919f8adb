/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Audit Entity Types Enum
 */
export const AuditLogEntityTypes = {
    Property: 'Property',
    Parcel: 'Parcel',
    Address: 'Address',
    Location: 'Location',
    BuildingFootPrint: 'BuildingFootPrint',
    Media: 'Media',
    MediaRelationship: 'MediaRelationship',
    AdditionalAddress: 'AdditionalAddress'
} as const;
export type AuditLogEntityTypes = typeof AuditLogEntityTypes[keyof typeof AuditLogEntityTypes];

