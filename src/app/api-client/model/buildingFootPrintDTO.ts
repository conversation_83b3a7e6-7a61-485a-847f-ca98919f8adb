/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface BuildingFootPrintDTO { 
    BuildingFootPrintId?: number;
    BuildingFootPrint?: string;
    SizeInSF?: number;
    SizeInSM?: number;
    PropertyMinFloor?: number;
    PropertyMaxFloor?: number;
    Floors?: number;
    IsDefault?: boolean;
    UseTypeId?: number;
    AdditionalUseTypeId?: number;
    MainSpecificUseTypeId?: number;
    AdditionalSpecificUseTypeId?: number;
    UseTypeName?: string;
    AdditionalUseTypeName?: string;
    MainSpecificUseTypeName?: string;
    AdditionalSpecificUseTypeName?: string;
    Description?: string;
    ModifiedByName?: string;
    ModifiedDate?: string;
    CRE_PropertyId?: number;
}

