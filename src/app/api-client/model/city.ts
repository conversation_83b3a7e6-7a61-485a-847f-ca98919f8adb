/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { EntityModel } from './entityModel';


export interface City { 
    createdBy?: EntityModel;
    createdDate?: string;
    modifiedBy?: EntityModel;
    modifiedDate?: string;
    cityId?: number;
    cityName?: string;
    stateId?: number;
    isActive?: boolean;
    activeMunicipality?: boolean;
    showInPublic?: boolean;
    displayOrderPublic?: number;
    imageUrl?: string;
    displayName?: string;
    marketStateId?: number;
}

