/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { EntityModel } from './entityModel';
import { Country } from './country';


export interface Company { 
    createdBy?: EntityModel;
    createdDate?: string;
    modifiedBy?: EntityModel;
    modifiedDate?: string;
    companyId?: number;
    companyName?: string;
    vendorTenantIdDel?: string;
    website?: string;
    companyTypeId?: number;
    isActive?: boolean;
    isPublic?: boolean;
    isNationalCompany?: boolean;
    ticker?: string;
    altCompanyName?: string;
    isCountryHeadQuarter?: boolean;
    isNationalBrokerageFirm?: boolean;
    isGlobalHeadQuarter?: boolean;
    country?: Country;
    naicsCode?: string;
    isMember?: boolean;
    metroId?: number;
    isic?: string;
    ratingTierId?: number;
    isDelinquent?: boolean;
    occupiedSF?: number;
    isAddressUnknown?: boolean;
    tenantResearchStatusId?: number;
    floorNumber?: string;
    nationalId?: string;
    noOfEmployees?: number;
    revenue?: string;
    estSpaceOccupied?: number;
    floorStatusId?: number;
    estTimeAtLocation?: string;
    hidedBy?: number;
    hidedDate?: string;
    hideReasonId?: number;
    hideReasonComments?: string;
    isHidden?: boolean;
    hasJointVenture?: boolean;
    useAltCoAsCompanyName?: boolean;
    isRegisteredAddress?: boolean;
    applicationId?: number;
    hideAgentsInPublic?: boolean;
    isListingManagedByProvider?: boolean;
    isTenantRepFirm?: boolean;
    excludeFromAnalytics?: boolean;
    hasExchangeOwnerLock?: boolean;
    companyTierId?: number;
    leaseCredits?: number;
    leaseInitialCredits?: number;
    subHideReasonId?: number;
    primarySICDivisionDesc?: string;
    primarySIC2DigitDesc?: string;
    primarySIC3DigitDesc?: string;
    registrationOrIncorporationDate?: string;
    anzsicCode?: number;
}

