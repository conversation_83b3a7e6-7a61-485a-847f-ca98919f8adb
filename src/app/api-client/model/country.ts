/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface Country { 
    countryId?: number;
    countryName?: string;
    alpha2Code?: string;
    alpha3Code?: string;
    unCountryCode?: string;
    callingCountryCode?: string;
    isActive?: boolean;
    unitId?: Country.UnitIdEnum;
    dateFormat?: string;
    dateFormatSave?: string;
    dateFormatRead?: string;
    unitDisplayTextSize?: string;
    unitDisplayTextLength?: string;
    mobileNumberPrefix?: string;
    mobileNumberMask?: string;
    mobileNumberRegEx?: string;
    mobileNumberPattern?: string;
    phoneNumberMask?: string;
    phoneNumberRegEx?: string;
    phoneNumberPattern?: string;
}
export namespace Country {
    export const UnitIdEnum = {
        Metric: 'METRIC',
        Imperium: 'IMPERIUM'
    } as const;
    export type UnitIdEnum = typeof UnitIdEnum[keyof typeof UnitIdEnum];
}


