/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Company } from './company';
import { Person } from './person';


export interface EntityModel { 
    entityId?: number;
    person?: Person;
    personId?: number;
    company?: Company;
    companyId?: number;
    tenantId?: number;
    roleId?: EntityModel.RoleIdEnum;
    startDate?: string;
    endDate?: string;
    applicationId?: number;
}
export namespace EntityModel {
    export const RoleIdEnum = {
        Ceo: 'CEO',
        Internal: 'INTERNAL',
        ListingAgent: 'LISTING_AGENT',
        BranchSuperUser: 'BRANCH_SUPER_USER',
        MetroSuperUser: 'METRO_SUPER_USER',
        CompanySuperUser: 'COMPANY_SUPER_USER',
        LeaseExchange: 'LEASE_EXCHANGE',
        DataFeed: 'DATA_FEED',
        ResearchAnalyst: 'RESEARCH_ANALYST',
        CustomerSupport: 'CUSTOMER_SUPPORT',
        Administration: 'ADMINISTRATION',
        Auditor: 'AUDITOR'
    } as const;
    export type RoleIdEnum = typeof RoleIdEnum[keyof typeof RoleIdEnum];
}


