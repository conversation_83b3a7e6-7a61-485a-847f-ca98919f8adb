/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface ExportSettingsResponseDTO { 
    ParentTableID?: ExportSettingsResponseDTO.ParentTableIDEnum;
    SettingsJSON?: { [key: string]: object; };
}
export namespace ExportSettingsResponseDTO {
    export const ParentTableIDEnum = {
        Property: 'Property',
        Listing: 'Listing',
        Suite: 'Suite',
        Parcel: 'Parcel',
        Company: 'Company',
        Branch: 'Branch',
        ContactRole: 'ContactRole',
        Person: 'Person',
        Sale: 'Sale',
        Lease: 'Lease',
        Address: 'Address',
        AdditionalUse: 'AdditionalUse',
        AdditionalAddress: 'AdditionalAddress',
        PropertyAllocation: 'PropertyAllocation',
        SalePriceConfirmation: 'SalePriceConfirmation',
        SaleLoanInfo: 'SaleLoanInfo',
        SellerContact: 'SellerContact',
        BuyerContact: 'BuyerContact',
        LevelSpacesBreakdown: 'LevelSpacesBreakdown',
        ExtensionsAndOptions: 'ExtensionsAndOptions',
        ReviewsAndRentEscalations: 'ReviewsAndRentEscalations',
        Media: 'Media',
        KeyContact: 'KeyContact',
        TenantVerification: 'TenantVerification',
        Tenant: 'Tenant',
        DashBoard: 'DashBoard',
        MarketBrief: 'MarketBrief',
        BranchDataFeed: 'BranchDataFeed',
        PropertyOwner: 'PropertyOwner',
        ProductUpdate: 'ProductUpdate',
        ListingVerification: 'ListingVerification',
        SuiteVerification: 'SuiteVerification',
        LeaseVerification: 'LeaseVerification',
        SaleVerification: 'SaleVerification'
    } as const;
    export type ParentTableIDEnum = typeof ParentTableIDEnum[keyof typeof ParentTableIDEnum];
}


