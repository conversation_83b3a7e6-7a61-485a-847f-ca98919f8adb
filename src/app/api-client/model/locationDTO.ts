/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface LocationDTO { 
    Latitude?: number;
    Longitude?: number;
    RooftopSourceID?: LocationDTO.RooftopSourceIDEnum;
    ZCoordinate?: number;
    GISShapeID?: number;
}
export namespace LocationDTO {
    export const RooftopSourceIDEnum = {
        MappingProvider: 'MAPPING_PROVIDER',
        Researcher: 'RESEARCHER'
    } as const;
    export type RooftopSourceIDEnum = typeof RooftopSourceIDEnum[keyof typeof RooftopSourceIDEnum];
}


