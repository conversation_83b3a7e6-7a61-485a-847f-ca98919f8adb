/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface LoginUserInfoDTO { 
    Username?: string;
    LoginID?: number;
    PersonName?: string;
    FirstName?: string;
    LastName?: string;
    Email?: string;
    CompanyID?: number;
    CompanyName?: string;
    RoleID?: LoginUserInfoDTO.RoleIDEnum;
    TenantID?: number;
    PersonID?: number;
    EntityID?: number;
    CountryID?: number;
    MetroID?: number;
    MetroCentroidLat?: number;
    MetroCentroidLong?: number;
    RoleName?: string;
    UnitId?: LoginUserInfoDTO.UnitIdEnum;
    MainPhotoUrl?: string;
    FailedAttemptsCount?: number;
    DateFormat?: string;
    IsMember?: boolean;
    MetroSkylineImageUrl?: string;
    CanImpersonateUser?: boolean;
    CanUpdateRole?: boolean;
    PilotMode?: boolean;
    OfficePhone?: string;
    DriveToolVersion?: string;
    CompanyFloorNumber?: string;
    CompanyAddress?: string;
    CompanyMainPhotoUrl?: string;
    CompanyCityName?: string;
    CompanyZipCode?: string;
    PhoneNumberRegEx?: string;
    PhoneNumberPattern?: string;
    PhoneNumberMask?: string;
    UnitDisplayTextSize?: string;
    UnitDisplayTextLength?: string;
    CanEditInPublic?: boolean;
    MobileNumberPrefix?: string;
    MobileNumberMask?: string;
    ShowSavedSearch?: boolean;
    ShowRegisteredLease?: boolean;
    CompanyTierID?: number;
    UltimateCompanyID?: number;
    UltimateCompanyName?: string;
    StateID?: number;
    IsLimitedAccess?: boolean;
    HasMarketBriefAccess?: boolean;
    EnforceTenantExportLimit?: boolean;
    EnableSaleExport?: boolean;
    EnableLeaseExport?: boolean;
    ForcefullyResetPassword?: boolean;
    EnableV2Search?: boolean;
    EnforceProExportLimit?: boolean;
}
export namespace LoginUserInfoDTO {
    export const RoleIDEnum = {
        Ceo: 'CEO',
        Internal: 'INTERNAL',
        ListingAgent: 'LISTING_AGENT',
        BranchSuperUser: 'BRANCH_SUPER_USER',
        MetroSuperUser: 'METRO_SUPER_USER',
        CompanySuperUser: 'COMPANY_SUPER_USER',
        LeaseExchange: 'LEASE_EXCHANGE',
        DataFeed: 'DATA_FEED',
        ResearchAnalyst: 'RESEARCH_ANALYST',
        CustomerSupport: 'CUSTOMER_SUPPORT',
        Administration: 'ADMINISTRATION',
        Auditor: 'AUDITOR'
    } as const;
    export type RoleIDEnum = typeof RoleIDEnum[keyof typeof RoleIDEnum];
    export const UnitIdEnum = {
        Metric: 'METRIC',
        Imperium: 'IMPERIUM'
    } as const;
    export type UnitIdEnum = typeof UnitIdEnum[keyof typeof UnitIdEnum];
}


