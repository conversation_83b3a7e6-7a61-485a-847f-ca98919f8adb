/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MediaDTO { 
    CreatedBy?: number;
    CreatedDate?: string;
    ModifiedBy?: number;
    ModifiedDate?: string;
    MediaID?: number;
    MediaName?: string;
    Height?: number;
    Width?: number;
    Size?: number;
    Path?: string;
    Ext?: string;
    Description?: string;
    ModifiedByName?: string;
    MediaRelationshipID?: number;
    MediaRelationTypeID?: MediaDTO.MediaRelationTypeIDEnum;
    MediaRelationTypeName?: string;
    MediaTypeID?: MediaDTO.MediaTypeIDEnum;
    MediaTypeName?: string;
    MediaSubTypeID?: MediaDTO.MediaSubTypeIDEnum;
    MediaSubTypeName?: string;
    RelationID?: number;
    PropertyID?: number;
    IsDefault?: boolean;
    IsOwnMedia?: boolean;
    MediaSourceID?: MediaDTO.MediaSourceIDEnum;
    SourceComments?: string;
    HasEdit?: boolean;
    BuildingSizeSF?: number;
    CondoUnit?: string;
}
export namespace MediaDTO {
    export const MediaRelationTypeIDEnum = {
        Property: 'PROPERTY',
        Listing: 'LISTING',
        Suite: 'SUITE',
        Sale: 'SALE',
        Company: 'COMPANY',
        Branch: 'BRANCH',
        Person: 'PERSON',
        Entity: 'ENTITY',
        AllMedia: 'ALL_MEDIA',
        PropertyChangeLog: 'PROPERTY_CHANGE_LOG',
        Lease: 'LEASE',
        MediaSupportDocs: 'MEDIA_SUPPORT_DOCS',
        MarketBrief: 'MARKET_BRIEF'
    } as const;
    export type MediaRelationTypeIDEnum = typeof MediaRelationTypeIDEnum[keyof typeof MediaRelationTypeIDEnum];
    export const MediaTypeIDEnum = {
        ArtistDrawing: 'ARTIST_DRAWING',
        FlyerBrochure: 'FLYER_BROCHURE',
        BuildingImage: 'BUILDING_IMAGE',
        StatisticsAnalytics: 'STATISTICS_ANALYTICS',
        LegalDocs: 'LEGAL_DOCS',
        AerialImagery: 'AERIAL_IMAGERY',
        PropertyManager: 'PROPERTY_MANAGER',
        LandLotImage: 'LAND_LOT_IMAGE',
        NewsPublication: 'NEWS_PUBLICATION',
        Interior: 'INTERIOR',
        ObliqueAerial: 'OBLIQUE_AERIAL',
        Title: 'TITLE',
        ListingSign: 'LISTING_SIGN',
        SuiteFloorPlan: 'SUITE_FLOOR_PLAN',
        TenantRoster: 'TENANT_ROSTER',
        OtherMedia: 'OTHER_MEDIA',
        SitePlan: 'SITE_PLAN',
        PropertyFloorPlan: 'PROPERTY_FLOOR_PLAN',
        Signage: 'SIGNAGE',
        RegisteredLease: 'REGISTERED_LEASE',
        RegisteredSublease: 'REGISTERED_SUBLEASE',
        ParcelPlat: 'PARCEL_PLAT',
        LeaseFolio: 'LEASE_FOLIO',
        MapImage: 'MAP_IMAGE'
    } as const;
    export type MediaTypeIDEnum = typeof MediaTypeIDEnum[keyof typeof MediaTypeIDEnum];
    export const MediaSubTypeIDEnum = {
        MainPhoto: 'MAIN_PHOTO',
        RightSide: 'RIGHT_SIDE',
        Lobby: 'LOBBY',
        Rear: 'REAR',
        LeftSide: 'LEFT_SIDE',
        OutBuilding: 'OUT_BUILDING',
        Front: 'FRONT',
        Misc: 'MISC'
    } as const;
    export type MediaSubTypeIDEnum = typeof MediaSubTypeIDEnum[keyof typeof MediaSubTypeIDEnum];
    export const MediaSourceIDEnum = {
        EmpiricalPhotography: 'EMPIRICAL_PHOTOGRAPHY',
        Streetview: 'STREETVIEW',
        OwnerWebsite: 'OWNER_WEBSITE',
        BuildingWebsite: 'BUILDING_WEBSITE',
        BrokersWebsite: 'BROKERS_WEBSITE',
        BrokersEmail: 'BROKERS_EMAIL',
        Brochure: 'BROCHURE',
        BrokersPdf: 'BROKERS_PDF',
        ThirdPartyWebsite: 'THIRD_PARTY_WEBSITE'
    } as const;
    export type MediaSourceIDEnum = typeof MediaSourceIDEnum[keyof typeof MediaSourceIDEnum];
}


