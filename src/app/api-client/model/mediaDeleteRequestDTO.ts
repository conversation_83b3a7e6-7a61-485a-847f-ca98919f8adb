/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MediaDeleteRequestDTO { 
    MediaRelationTypeID: MediaDeleteRequestDTO.MediaRelationTypeIDEnum;
    RelationID: number;
    MediaRelationshipID: number;
}
export namespace MediaDeleteRequestDTO {
    export const MediaRelationTypeIDEnum = {
        Property: 'PROPERTY',
        Listing: 'LISTING',
        Suite: 'SUITE',
        Sale: 'SALE',
        Company: 'COMPANY',
        Branch: 'BRANCH',
        Person: 'PERSON',
        Entity: 'ENTITY',
        AllMedia: 'ALL_MEDIA',
        PropertyChangeLog: 'PROPERTY_CHANGE_LOG',
        Lease: 'LEASE',
        MediaSupportDocs: 'MEDIA_SUPPORT_DOCS',
        MarketBrief: 'MARKET_BRIEF'
    } as const;
    export type MediaRelationTypeIDEnum = typeof MediaRelationTypeIDEnum[keyof typeof MediaRelationTypeIDEnum];
}


