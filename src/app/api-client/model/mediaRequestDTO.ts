/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MediaRequestDTO { 
    Description?: string;
    Ext?: string;
    Height?: number;
    Width?: number;
    IsDefault?: boolean;
    IsOwnMedia?: boolean;
    MediaName?: string;
    MediaRelationTypeID: MediaRequestDTO.MediaRelationTypeIDEnum;
    MediaSourceID?: MediaRequestDTO.MediaSourceIDEnum;
    MediaSubTypeID?: MediaRequestDTO.MediaSubTypeIDEnum;
    MediaTypeID?: MediaRequestDTO.MediaTypeIDEnum;
    Path?: string;
    PropertyID: number;
    RelationID: number;
    Size?: number;
    SourceComments?: string;
}
export namespace MediaRequestDTO {
    export const MediaRelationTypeIDEnum = {
        Property: 'PROPERTY',
        Listing: 'LISTING',
        Suite: 'SUITE',
        Sale: 'SALE',
        Company: 'COMPANY',
        Branch: 'BRANCH',
        Person: 'PERSON',
        Entity: 'ENTITY',
        AllMedia: 'ALL_MEDIA',
        PropertyChangeLog: 'PROPERTY_CHANGE_LOG',
        Lease: 'LEASE',
        MediaSupportDocs: 'MEDIA_SUPPORT_DOCS',
        MarketBrief: 'MARKET_BRIEF'
    } as const;
    export type MediaRelationTypeIDEnum = typeof MediaRelationTypeIDEnum[keyof typeof MediaRelationTypeIDEnum];
    export const MediaSourceIDEnum = {
        EmpiricalPhotography: 'EMPIRICAL_PHOTOGRAPHY',
        Streetview: 'STREETVIEW',
        OwnerWebsite: 'OWNER_WEBSITE',
        BuildingWebsite: 'BUILDING_WEBSITE',
        BrokersWebsite: 'BROKERS_WEBSITE',
        BrokersEmail: 'BROKERS_EMAIL',
        Brochure: 'BROCHURE',
        BrokersPdf: 'BROKERS_PDF',
        ThirdPartyWebsite: 'THIRD_PARTY_WEBSITE'
    } as const;
    export type MediaSourceIDEnum = typeof MediaSourceIDEnum[keyof typeof MediaSourceIDEnum];
    export const MediaSubTypeIDEnum = {
        MainPhoto: 'MAIN_PHOTO',
        RightSide: 'RIGHT_SIDE',
        Lobby: 'LOBBY',
        Rear: 'REAR',
        LeftSide: 'LEFT_SIDE',
        OutBuilding: 'OUT_BUILDING',
        Front: 'FRONT',
        Misc: 'MISC'
    } as const;
    export type MediaSubTypeIDEnum = typeof MediaSubTypeIDEnum[keyof typeof MediaSubTypeIDEnum];
    export const MediaTypeIDEnum = {
        ArtistDrawing: 'ARTIST_DRAWING',
        FlyerBrochure: 'FLYER_BROCHURE',
        BuildingImage: 'BUILDING_IMAGE',
        StatisticsAnalytics: 'STATISTICS_ANALYTICS',
        LegalDocs: 'LEGAL_DOCS',
        AerialImagery: 'AERIAL_IMAGERY',
        PropertyManager: 'PROPERTY_MANAGER',
        LandLotImage: 'LAND_LOT_IMAGE',
        NewsPublication: 'NEWS_PUBLICATION',
        Interior: 'INTERIOR',
        ObliqueAerial: 'OBLIQUE_AERIAL',
        Title: 'TITLE',
        ListingSign: 'LISTING_SIGN',
        SuiteFloorPlan: 'SUITE_FLOOR_PLAN',
        TenantRoster: 'TENANT_ROSTER',
        OtherMedia: 'OTHER_MEDIA',
        SitePlan: 'SITE_PLAN',
        PropertyFloorPlan: 'PROPERTY_FLOOR_PLAN',
        Signage: 'SIGNAGE',
        RegisteredLease: 'REGISTERED_LEASE',
        RegisteredSublease: 'REGISTERED_SUBLEASE',
        ParcelPlat: 'PARCEL_PLAT',
        LeaseFolio: 'LEASE_FOLIO',
        MapImage: 'MAP_IMAGE'
    } as const;
    export type MediaTypeIDEnum = typeof MediaTypeIDEnum[keyof typeof MediaTypeIDEnum];
}


