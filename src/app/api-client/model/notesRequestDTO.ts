/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface NotesRequestDTO { 
    NoteTypeId?: NotesRequestDTO.NoteTypeIdEnum;
    NoteTitle?: string;
    NoteDescription?: string;
    ParentTableId?: NotesRequestDTO.ParentTableIdEnum;
    ParentId?: number;
}
export namespace NotesRequestDTO {
    export const NoteTypeIdEnum = {
        Note: 'NOTE',
        Meeting: 'MEETING',
        Rfi: 'RFI',
        Rfp: 'RFP',
        Presentation: 'PRESENTATION',
        ReceivedExclusive: 'RECEIVED_EXCLUSIVE',
        CompletedLease: 'COMPLETED_LEASE',
        CompletedSale: 'COMPLETED_SALE',
        Email: 'EMAIL',
        SuiteTransactionNote: 'SUITE_TRANSACTION_NOTE',
        SuiteVerificationNote: 'SUITE_VERIFICATION_NOTE',
        LeaseCompTransactionNotes: 'LEASE_COMP_TRANSACTION_NOTES',
        LeaseCompVerificationNotes: 'LEASE_COMP_VERIFICATION_NOTES',
        LeaseCompSourceCommentNotes: 'LEASE_COMP_SOURCE_COMMENT_NOTES',
        CompanyRelatedTo: 'COMPANY_RELATED_TO',
        LeaseCreditAdjustmentNotes: 'LEASE_CREDIT_ADJUSTMENT_NOTES',
        UnderRenovation: 'UNDER_RENOVATION',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        BuildingDemolished: 'BUILDING_DEMOLISHED',
        BuildingVacant: 'BUILDING_VACANT',
        TenantMoved: 'TENANT_MOVED',
        GovtBuilding: 'GOVT_BUILDING',
        CannotAccessTenantRoster: 'CANNOT_ACCESS_TENANT_ROSTER',
        Other: 'OTHER'
    } as const;
    export type NoteTypeIdEnum = typeof NoteTypeIdEnum[keyof typeof NoteTypeIdEnum];
    export const ParentTableIdEnum = {
        Property: 'Property',
        Listing: 'Listing',
        Suite: 'Suite',
        Parcel: 'Parcel',
        Company: 'Company',
        Branch: 'Branch',
        ContactRole: 'ContactRole',
        Person: 'Person',
        Sale: 'Sale',
        Lease: 'Lease',
        Address: 'Address',
        AdditionalUse: 'AdditionalUse',
        AdditionalAddress: 'AdditionalAddress',
        PropertyAllocation: 'PropertyAllocation',
        SalePriceConfirmation: 'SalePriceConfirmation',
        SaleLoanInfo: 'SaleLoanInfo',
        SellerContact: 'SellerContact',
        BuyerContact: 'BuyerContact',
        LevelSpacesBreakdown: 'LevelSpacesBreakdown',
        ExtensionsAndOptions: 'ExtensionsAndOptions',
        ReviewsAndRentEscalations: 'ReviewsAndRentEscalations',
        Media: 'Media',
        KeyContact: 'KeyContact',
        TenantVerification: 'TenantVerification',
        Tenant: 'Tenant',
        DashBoard: 'DashBoard',
        MarketBrief: 'MarketBrief',
        BranchDataFeed: 'BranchDataFeed',
        PropertyOwner: 'PropertyOwner',
        ProductUpdate: 'ProductUpdate',
        ListingVerification: 'ListingVerification',
        SuiteVerification: 'SuiteVerification',
        LeaseVerification: 'LeaseVerification',
        SaleVerification: 'SaleVerification'
    } as const;
    export type ParentTableIdEnum = typeof ParentTableIdEnum[keyof typeof ParentTableIdEnum];
}


