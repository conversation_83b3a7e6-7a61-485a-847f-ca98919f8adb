/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { EntityModel } from './entityModel';


export interface Person { 
    createdBy?: EntityModel;
    createdDate?: string;
    modifiedBy?: EntityModel;
    modifiedDate?: string;
    personId?: number;
    firstName?: string;
    middleName?: string;
    lastName?: string;
    suffix?: string;
    email?: string;
    titleId?: string;
    nickName?: string;
    webSite?: string;
    linkedInProfile?: string;
    photoUrl?: string;
}

