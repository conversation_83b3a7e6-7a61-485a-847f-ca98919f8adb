/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PropertyMasterRollupObject } from './propertyMasterRollupObject';
import { AddressDTO } from './addressDTO';
import { LocationDTO } from './locationDTO';


export interface PropertyDetailsDTO { 
    CreatedBy?: number;
    CreatedDate?: string;
    ModifiedBy?: number;
    ModifiedDate?: string;
    PropertyID?: number;
    PropertyName?: string;
    ParentPropertyID?: number;
    IsActive?: boolean;
    YearBuilt?: number;
    YearRenovated?: number;
    Floors?: number;
    ConstructionStatusID?: PropertyDetailsDTO.ConstructionStatusIDEnum;
    ConstructionTypeID?: PropertyDetailsDTO.ConstructionTypeIDEnum;
    HvacTypeID?: PropertyDetailsDTO.HvacTypeIDEnum;
    SprinklerTypeID?: PropertyDetailsDTO.SprinklerTypeIDEnum;
    CondoTypeID?: PropertyDetailsDTO.CondoTypeIDEnum;
    UseTypeID?: number;
    UseTypeName?: string;
    PropertyUse?: string;
    SpecificUseID?: number;
    SpecificUseName?: string;
    EstimatedCompletionDate?: string;
    IsADAAccessible?: boolean;
    IsVented?: boolean;
    IsOwnerOccupied?: boolean;
    ClassTypeID?: PropertyDetailsDTO.ClassTypeIDEnum;
    TenancyTypeID?: PropertyDetailsDTO.TenancyTypeIDEnum;
    IsEnergyStar?: boolean;
    MixedUseAllocation?: string;
    BuildingWebsite?: string;
    BuildingComments?: string;
    MetroId?: number;
    MarketId?: number;
    SubMarketID?: number;
    GovernmentInterestID?: PropertyDetailsDTO.GovernmentInterestIDEnum;
    HasSolar?: boolean;
    TrafficCount?: number;
    EnergyStarRatingID?: PropertyDetailsDTO.EnergyStarRatingIDEnum;
    WaterStarRatingID?: PropertyDetailsDTO.WaterStarRatingIDEnum;
    GreenStarRatingID?: PropertyDetailsDTO.GreenStarRatingIDEnum;
    CurrentTitle?: string;
    ActualCompletion?: string;
    TitleReferenceDate?: string;
    LandUse?: number;
    LastReviewedBy?: number;
    LastReviewedDate?: string;
    ConstructionStartDate?: string;
    BookValue?: number;
    BookValueDate?: string;
    ParcelInfo?: string;
    MainPhotoUrl?: string;
    Zoning?: string;
    UseAddressAsPropertyName?: boolean;
    Amenities?: string;
    PassengerElevators?: number;
    ParkingElevators?: number;
    FreighElevators?: number;
    DockHigh?: number;
    Truckwell?: number;
    YardPaved?: boolean;
    PowerComments?: string;
    UtilityComments?: string;
    PropertyComments?: string;
    NoOfUnits?: number;
    Lifts?: boolean;
    LiftsCount?: number;
    PowerType?: PropertyDetailsDTO.PowerTypeEnum;
    Vacancy?: number;
    BayWidth?: number;
    BayDepth?: number;
    Depth?: number;
    Width?: number;
    IncludeInAnalytics?: boolean;
    OfficeAC?: PropertyDetailsDTO.OfficeACEnum;
    LotSizeSourceID?: PropertyDetailsDTO.LotSizeSourceIDEnum;
    ZoningClassID?: PropertyDetailsDTO.ZoningClassIDEnum;
    ZoningCode?: string;
    PotentialZoningID?: PropertyDetailsDTO.PotentialZoningIDEnum;
    SurroundingLandUse?: PropertyDetailsDTO.SurroundingLandUseEnum;
    RailServed?: boolean;
    IsFloodPlain?: boolean;
    SmallestFloor?: number;
    LargestFloor?: number;
    RoofTypeID?: PropertyDetailsDTO.RoofTypeIDEnum;
    HasYardFenced?: boolean;
    HasYardUnfenced?: boolean;
    BuildSpecStatusID?: PropertyDetailsDTO.BuildSpecStatusIDEnum;
    LegalDescription?: string;
    InternalComments?: string;
    ParkingSpaces?: number;
    HasSprinkler?: boolean;
    SizeSourceID?: PropertyDetailsDTO.SizeSourceIDEnum;
    HasPortAccess?: boolean;
    HasYard?: boolean;
    HasResCoveredParking?: boolean;
    ParkingRatio?: string;
    EarthquakeZoneID?: number;
    GeoscapePropertyID?: string;
    CounsilTaxID?: string;
    ValuerGeneralID?: string;
    ClearHeightMin?: number;
    ClearHeightMax?: number;
    Volts?: number;
    Phase?: number;
    Amps?: number;
    RetailFrontage?: number;
    TypicalFloorSize?: number;
    HardstandArea?: number;
    HardstandAreaSM?: number;
    HardstandAreaSourceID?: PropertyDetailsDTO.HardstandAreaSourceIDEnum;
    ContributedGBASizeSourceID?: PropertyDetailsDTO.ContributedGBASizeSourceIDEnum;
    Mezzanine?: boolean;
    Awnings?: boolean;
    AwningsCount?: number;
    BuildingSize?: number;
    MinFloorSize?: number;
    MaxFloorSize?: number;
    ResearchTypeName?: string;
    TrueOwners?: string;
    RecordedOwners?: string;
    IsSkipped?: boolean;
    IsMultiplePolygonsNeeded?: boolean;
    NeedsResearchComments?: string;
    HasNoBuildingFootprints?: boolean;
    HasNoExistingParcelInTileLayer?: boolean;
    MasterPropertyId?: number;
    PropertyCreatedByName?: string;
    PropertyModifiedByName?: string;
    PropertyLastReviewedByName?: string;
    ContributedSourceComments?: string;
    CondoUnit?: string;
    GbasizeSource?: string;
    NRASizeSourceID?: PropertyDetailsDTO.NRASizeSourceIDEnum;
    NoOfOfficeFloor?: number;
    MarketName?: string;
    OccupancyPercent?: number;
    OccupiedPercentage?: number;
    TIAllowance?: number;
    GRESBScore?: number;
    GRESBScoreMin?: number;
    GRESBScoreMax?: number;
    EstCompletionDate?: string;
    Complex?: string;
    LGA?: string;
    GradeLevelDriveIn?: number;
    ReservedParkingSpaces?: number;
    ReservedParkingSpacesRatePerMonth?: number;
    HasReservedParkingSpaces?: boolean;
    UnreservedParkingSpaces?: number;
    UnreservedParkingSpacesRatePerMonth?: number;
    HasUnreservedParkingSpaces?: boolean;
    TotalAnchorSF?: number;
    HVAC?: boolean;
    IsCraneServed?: boolean;
    Anchors?: number;
    NoOfAnchor?: number;
    GLASizeSourceID?: PropertyDetailsDTO.GLASizeSourceIDEnum;
    GLARSizeSourceID?: PropertyDetailsDTO.GLARSizeSourceIDEnum;
    LotSizeAcres?: number;
    RetailSF?: number;
    OfficeSF?: number;
    BuildingSF?: number;
    LotSizeSF?: number;
    TotalSaleSizeSF?: number;
    ContributedGBA_SF?: number;
    GLA_SF?: number;
    GLAR_SF?: number;
    Mezzanine_Size_SF?: number;
    Awnings_Size_SF?: number;
    LotSizeAc?: number;
    NLA_SF?: number;
    NLAac?: number;
    ContributedGBA_SM?: number;
    GLA?: number;
    GLAR?: number;
    PropertyResearchTypeID?: number;
    TypicalFloorSizeSourceID?: PropertyDetailsDTO.TypicalFloorSizeSourceIDEnum;
    OfficeHVAC?: PropertyDetailsDTO.OfficeHVACEnum;
    FeatureIDs?: string;
    AmenitiesType?: string;
    IsReviewed?: boolean;
    Address?: AddressDTO;
    Location?: LocationDTO;
    PropertyAuditStatusID?: number;
    RollupObject?: PropertyMasterRollupObject;
}
export namespace PropertyDetailsDTO {
    export const ConstructionStatusIDEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusIDEnum = typeof ConstructionStatusIDEnum[keyof typeof ConstructionStatusIDEnum];
    export const ConstructionTypeIDEnum = {
        Brick: 'BRICK',
        BrickBlock: 'BRICK_BLOCK',
        ConcretePoured: 'CONCRETE_POURED',
        ConcreteBlock: 'CONCRETE_BLOCK',
        CurtainWallMiscType: 'CURTAIN_WALL_MISC_TYPE',
        FrameAndStucco: 'FRAME_AND_STUCCO',
        Glass: 'GLASS',
        Masonry: 'MASONRY',
        MetalGeneralCase: 'METAL_GENERAL_CASE',
        MetalSteel: 'METAL_STEEL',
        Mixed: 'MIXED',
        Other: 'OTHER',
        Precast: 'PRECAST',
        PrefabLightSteel: 'PREFAB_LIGHT_STEEL',
        ReinforcedConcrete: 'REINFORCED_CONCRETE',
        SteelFrame: 'STEEL_FRAME',
        SteelFrameGlassCurtainWall: 'STEEL_FRAME_GLASS_CURTAIN_WALL',
        SteelFrameMasonryFacade: 'STEEL_FRAME_MASONRY_FACADE',
        SteelFrameMetalSiding: 'STEEL_FRAME_METAL_SIDING',
        SteelFrameVinylSiding: 'STEEL_FRAME_VINYL_SIDING',
        SteelFramedGlassCurtain: 'STEEL_FRAMED_GLASS_CURTAIN',
        TiltUp: 'TILT_UP',
        WoodFrame: 'WOOD_FRAME',
        WoodFrameBrickFacade: 'WOOD_FRAME_BRICK_FACADE'
    } as const;
    export type ConstructionTypeIDEnum = typeof ConstructionTypeIDEnum[keyof typeof ConstructionTypeIDEnum];
    export const HvacTypeIDEnum = {
        ChilledWater: 'CHILLED_WATER',
        ElectricAirConditioning: 'ELECTRIC_AIR_CONDITIONING',
        ElectricBaseBoard: 'ELECTRIC_BASE_BOARD',
        ForcedAirUnits: 'FORCED_AIR_UNITS',
        ForcedWarmColdAirConditioning: 'FORCED_WARM_COLD_AIR_CONDITIONING',
        HeatingVentilationAirConditioning: 'HEATING_VENTILATION_AIR_CONDITIONING',
        MixedTypes: 'MIXED_TYPES',
        None: 'NONE',
        OtherTypes: 'OTHER_TYPES',
        RefrigeratedAirConditioning: 'REFRIGERATED_AIR_CONDITIONING',
        Reznor: 'REZNOR',
        SpaceHeaters: 'SPACE_HEATERS',
        SuspendedGas: 'SUSPENDED_GAS',
        Unknown: 'UNKNOWN'
    } as const;
    export type HvacTypeIDEnum = typeof HvacTypeIDEnum[keyof typeof HvacTypeIDEnum];
    export const SprinklerTypeIDEnum = {
        Esfr: 'ESFR',
        FinishedSpaceOnly: 'FINISHED_SPACE_ONLY',
        Dry: 'DRY',
        Wet: 'WET',
        NoSprinkler: 'NO_SPRINKLER',
        Unknown: 'UNKNOWN'
    } as const;
    export type SprinklerTypeIDEnum = typeof SprinklerTypeIDEnum[keyof typeof SprinklerTypeIDEnum];
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
    export const ClassTypeIDEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type ClassTypeIDEnum = typeof ClassTypeIDEnum[keyof typeof ClassTypeIDEnum];
    export const TenancyTypeIDEnum = {
        Single: 'SINGLE',
        Multiple: 'MULTIPLE'
    } as const;
    export type TenancyTypeIDEnum = typeof TenancyTypeIDEnum[keyof typeof TenancyTypeIDEnum];
    export const GovernmentInterestIDEnum = {
        OwnerOccupied: 'OWNER_OCCUPIED',
        Leases: 'LEASES',
        None: 'NONE'
    } as const;
    export type GovernmentInterestIDEnum = typeof GovernmentInterestIDEnum[keyof typeof GovernmentInterestIDEnum];
    export const EnergyStarRatingIDEnum = {
        One: 'ONE',
        OnePointFive: 'ONE_POINT_FIVE',
        Two: 'TWO',
        TwoPointFive: 'TWO_POINT_FIVE',
        Three: 'THREE',
        ThreePointFive: 'THREE_POINT_FIVE',
        Four: 'FOUR',
        FourPointFive: 'FOUR_POINT_FIVE',
        Five: 'FIVE',
        FivePointFive: 'FIVE_POINT_FIVE',
        Six: 'SIX'
    } as const;
    export type EnergyStarRatingIDEnum = typeof EnergyStarRatingIDEnum[keyof typeof EnergyStarRatingIDEnum];
    export const WaterStarRatingIDEnum = {
        One: 'ONE',
        OnePointFive: 'ONE_POINT_FIVE',
        Two: 'TWO',
        TwoPointFive: 'TWO_POINT_FIVE',
        Three: 'THREE',
        ThreePointFive: 'THREE_POINT_FIVE',
        Four: 'FOUR',
        FourPointFive: 'FOUR_POINT_FIVE',
        Five: 'FIVE',
        FivePointFive: 'FIVE_POINT_FIVE'
    } as const;
    export type WaterStarRatingIDEnum = typeof WaterStarRatingIDEnum[keyof typeof WaterStarRatingIDEnum];
    export const GreenStarRatingIDEnum = {
        Zero: 'ZERO',
        One: 'ONE',
        Two: 'TWO',
        Three: 'THREE',
        Four: 'FOUR',
        Five: 'FIVE',
        Six: 'SIX'
    } as const;
    export type GreenStarRatingIDEnum = typeof GreenStarRatingIDEnum[keyof typeof GreenStarRatingIDEnum];
    export const PowerTypeEnum = {
        HighVoltagePowerSupp: 'HIGH_VOLTAGE_POWER_SUPP',
        SolarPowerSystemInst: 'SOLAR_POWER_SYSTEM_INST'
    } as const;
    export type PowerTypeEnum = typeof PowerTypeEnum[keyof typeof PowerTypeEnum];
    export const OfficeACEnum = {
        ChilledWater: 'CHILLED_WATER',
        ElectricAirConditioning: 'ELECTRIC_AIR_CONDITIONING',
        ElectricBaseBoard: 'ELECTRIC_BASE_BOARD',
        ForcedAirUnits: 'FORCED_AIR_UNITS',
        ForcedWarmColdAirConditioning: 'FORCED_WARM_COLD_AIR_CONDITIONING',
        HeatingVentilationAirConditioning: 'HEATING_VENTILATION_AIR_CONDITIONING',
        MixedTypes: 'MIXED_TYPES',
        None: 'NONE',
        OtherTypes: 'OTHER_TYPES',
        RefrigeratedAirConditioning: 'REFRIGERATED_AIR_CONDITIONING',
        Reznor: 'REZNOR',
        SpaceHeaters: 'SPACE_HEATERS',
        SuspendedGas: 'SUSPENDED_GAS',
        Unknown: 'UNKNOWN'
    } as const;
    export type OfficeACEnum = typeof OfficeACEnum[keyof typeof OfficeACEnum];
    export const LotSizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type LotSizeSourceIDEnum = typeof LotSizeSourceIDEnum[keyof typeof LotSizeSourceIDEnum];
    export const ZoningClassIDEnum = {
        Ag: 'AG',
        B: 'B',
        B3: 'B_3',
        I1: 'I_1',
        I2: 'I_2',
        Industrial: 'INDUSTRIAL',
        Commercial: 'COMMERCIAL',
        Residential: 'RESIDENTIAL'
    } as const;
    export type ZoningClassIDEnum = typeof ZoningClassIDEnum[keyof typeof ZoningClassIDEnum];
    export const PotentialZoningIDEnum = {
        Ag: 'AG',
        B: 'B',
        B3: 'B_3',
        I1: 'I_1',
        I2: 'I_2',
        Industrial: 'INDUSTRIAL',
        Commercial: 'COMMERCIAL',
        Residential: 'RESIDENTIAL'
    } as const;
    export type PotentialZoningIDEnum = typeof PotentialZoningIDEnum[keyof typeof PotentialZoningIDEnum];
    export const SurroundingLandUseEnum = {
        Ag: 'AG',
        B: 'B',
        B3: 'B_3',
        I1: 'I_1',
        I2: 'I_2',
        Industrial: 'INDUSTRIAL',
        Commercial: 'COMMERCIAL',
        Residential: 'RESIDENTIAL'
    } as const;
    export type SurroundingLandUseEnum = typeof SurroundingLandUseEnum[keyof typeof SurroundingLandUseEnum];
    export const RoofTypeIDEnum = {
        BuiltUp: 'BUILT_UP',
        Composition: 'COMPOSITION',
        Hip: 'HIP',
        MetalRoof: 'METAL_ROOF',
        Tpo: 'TPO',
        Epdm: 'EPDM',
        SolarPanels: 'SOLAR_PANELS',
        ModifiedBitumen: 'MODIFIED_BITUMEN',
        SinglePly: 'SINGLE_PLY',
        Other: 'OTHER'
    } as const;
    export type RoofTypeIDEnum = typeof RoofTypeIDEnum[keyof typeof RoofTypeIDEnum];
    export const BuildSpecStatusIDEnum = {
        Exists: 'EXISTS',
        BuildToSuit: 'BUILD_TO_SUIT',
        ToBeDetermined: 'TO_BE_DETERMINED',
        SpeculativeDevelopment: 'SPECULATIVE_DEVELOPMENT'
    } as const;
    export type BuildSpecStatusIDEnum = typeof BuildSpecStatusIDEnum[keyof typeof BuildSpecStatusIDEnum];
    export const SizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type SizeSourceIDEnum = typeof SizeSourceIDEnum[keyof typeof SizeSourceIDEnum];
    export const HardstandAreaSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type HardstandAreaSourceIDEnum = typeof HardstandAreaSourceIDEnum[keyof typeof HardstandAreaSourceIDEnum];
    export const ContributedGBASizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type ContributedGBASizeSourceIDEnum = typeof ContributedGBASizeSourceIDEnum[keyof typeof ContributedGBASizeSourceIDEnum];
    export const NRASizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type NRASizeSourceIDEnum = typeof NRASizeSourceIDEnum[keyof typeof NRASizeSourceIDEnum];
    export const GLASizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type GLASizeSourceIDEnum = typeof GLASizeSourceIDEnum[keyof typeof GLASizeSourceIDEnum];
    export const GLARSizeSourceIDEnum = {
        AerialEstimation: 'AERIAL_ESTIMATION',
        Appraiser: 'APPRAISER',
        BrokerAgent: 'BROKER_AGENT',
        CountyDataSource: 'COUNTY_DATA_SOURCE',
        PropertyOwner: 'PROPERTY_OWNER',
        Brochure: 'BROCHURE',
        ThirdParty: 'THIRD_PARTY',
        OnsiteMeasurement: 'ONSITE_MEASUREMENT'
    } as const;
    export type GLARSizeSourceIDEnum = typeof GLARSizeSourceIDEnum[keyof typeof GLARSizeSourceIDEnum];
    export const TypicalFloorSizeSourceIDEnum = {
        Broker: 'BROKER',
        Owner: 'OWNER',
        Propertymanager: 'PROPERTYMANAGER'
    } as const;
    export type TypicalFloorSizeSourceIDEnum = typeof TypicalFloorSizeSourceIDEnum[keyof typeof TypicalFloorSizeSourceIDEnum];
    export const OfficeHVACEnum = {
        Ac: 'AC',
        Heat: 'HEAT',
        Both: 'BOTH'
    } as const;
    export type OfficeHVACEnum = typeof OfficeHVACEnum[keyof typeof OfficeHVACEnum];
}


