/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertyECRESearchQuerydslDTO { 
    PropertyID?: number;
    CondoTypeID?: PropertyECRESearchQuerydslDTO.CondoTypeIDEnum;
    UseTypeID?: number;
    UseTypeName?: string;
    MasterPropertyId?: number;
    ListingID?: number;
    Latitude?: number;
    Longitude?: number;
}
export namespace PropertyECRESearchQuerydslDTO {
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
}


