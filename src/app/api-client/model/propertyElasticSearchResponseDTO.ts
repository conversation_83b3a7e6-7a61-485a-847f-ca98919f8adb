/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertyElasticSearchResponseDTO { 
    Address?: string;
    AddressText?: string;
    CityID?: number;
    CityName?: string;
    CondoTypeID?: PropertyElasticSearchResponseDTO.CondoTypeIDEnum;
    PropertyID?: number;
    PropertyName?: string;
    PropertyNameDisplay?: string;
    StateAbbr?: string;
    StateID?: number;
    StateName?: string;
}
export namespace PropertyElasticSearchResponseDTO {
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
}


