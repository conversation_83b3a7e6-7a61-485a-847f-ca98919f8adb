/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressDTO } from './addressDTO';
import { LocationDTO } from './locationDTO';


export interface PropertyMapSearchSizeResponseDTO { 
    PropertyID?: number;
    PropertyName?: string;
    YearBuilt?: number;
    CondoTypeID?: PropertyMapSearchSizeResponseDTO.CondoTypeIDEnum;
    UseTypeID?: number;
    UseTypeName?: string;
    SpecificUseID?: number;
    SpecificUseName?: string;
    ResearchTypeName?: string;
    MainPhotoURL?: string;
    HasNoBuildingFootprints?: boolean;
    ClassType?: PropertyMapSearchSizeResponseDTO.ClassTypeEnum;
    ConstructionStatus?: PropertyMapSearchSizeResponseDTO.ConstructionStatusEnum;
    CityName?: string;
    CondoUnit?: string;
    StateAbbr?: string;
    ParcelInfo?: string;
    TrueOwners?: string;
    RecordedOwners?: string;
    CountryCode?: string;
    BuildingSF?: number;
    LotSizeSF?: number;
    ContributedGBASizeSF?: number;
    PropertyResearchTypeID?: number;
    Address?: AddressDTO;
    Location?: LocationDTO;
    LotSizeSM?: number;
    LotSizeSMFormatted?: string;
    LotSizeACSM?: number;
    BuildingSizeSM?: number;
    BuildingSizeSMFormatted?: string;
    ContributedGBA_SM?: number;
    ContributedGBA_SMFormatted?: string;
}
export namespace PropertyMapSearchSizeResponseDTO {
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
    export const ClassTypeEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type ClassTypeEnum = typeof ClassTypeEnum[keyof typeof ClassTypeEnum];
    export const ConstructionStatusEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusEnum = typeof ConstructionStatusEnum[keyof typeof ConstructionStatusEnum];
}


