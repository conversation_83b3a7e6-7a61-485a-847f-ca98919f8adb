/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ChildFreeHoldsAdditionalUsesDTO } from './childFreeHoldsAdditionalUsesDTO';


export interface PropertyMasterRollupObject { 
    DockHigh?: number;
    Truckwell?: number;
    ParkingSpaces?: number;
    PassengerElevators?: number;
    ParkingElevators?: number;
    RetailFrontage?: number;
    FreighElevators?: number;
    ParkingRatio?: number;
    AwningsCount?: number;
    TrafficCount?: number;
    RailServed?: boolean;
    HasPortAccess?: boolean;
    IsOwnerOccupied?: boolean;
    HasResCoveredParking?: boolean;
    IsVented?: boolean;
    IsADAAccessible?: boolean;
    HasYardFenced?: boolean;
    HasYardUnfenced?: boolean;
    YardPaved?: boolean;
    HasYard?: boolean;
    HasSolar?: boolean;
    IncludeInAnalytics?: boolean;
    Mezzanine?: boolean;
    Awnings?: boolean;
    Lifts?: boolean;
    SpecificUseName?: string;
    TenancyTypeID?: string;
    ClassTypeID?: string;
    ConstructionTypeID?: string;
    ConstructionStatusID?: string;
    Volts?: string;
    Amps?: string;
    SprinklerTypeID?: string;
    HvacTypeID?: string;
    GovernmentInterestID?: string;
    BuildingWebsite?: string;
    ZoningCode?: string;
    PowerType?: string;
    SizeSourceID?: string;
    LotSizeSourceID?: string;
    ContributedGBASizeSourceID?: string;
    YearBuilt?: string;
    YearRenovated?: string;
    WaterStarRatingID?: string;
    EnergyStarRatingID?: string;
    GreenStarRatingID?: string;
    SmallestFloor?: string;
    LargestFloor?: string;
    TypicalFloorSize?: string;
    ClearHeightMin?: string;
    ClearHeightMax?: string;
    Vacancy?: string;
    BookValue?: string;
    BookValueDate?: string;
    HardstandArea?: string;
    ConstructionStartDate?: string;
    ActualCompletion?: string;
    TitleReferenceDate?: string;
    PropertyUse?: string;
    UseTypeID?: number;
    MainPhotoUrl?: string;
    ChildFreeHoldsAdditionalUses?: Array<ChildFreeHoldsAdditionalUsesDTO>;
    BuildSpecStatusID?: string;
    Floors?: string;
    RoofTypeID?: string;
    CurrentTitle?: string;
    HasReservedParkingSpaces?: boolean;
    HasSprinkler?: boolean;
    LiftsCount?: number;
    Phase?: string;
    HardstandAreaSourceID?: string;
    NoOfUnits?: number;
    BuildingSF?: number;
    BuildingSizeSM?: number;
    GradeLevelDriveIn?: number;
    Anchors?: number;
    NoOfAnchor?: number;
    TotalAnchorSF?: number;
    RetailFrontageM?: number;
    ReservedParkingSpaces?: number;
    UnreservedParkingSpaces?: number;
    HasUnreservedParkingSpaces?: boolean;
    HVAC?: boolean;
    AmenitiesType?: string;
    FeatureIDs?: string;
    GLASizeSourceID?: string;
    GLARSizeSourceID?: string;
    OfficeHVAC?: string;
    SmallestFloorSM?: string;
    LargestFloorSM?: string;
    NoOfOfficeFloor?: string;
    OfficeSF?: string;
    OfficeSM?: string;
    TypicalFloorSizeSM?: string;
    TIAllowance?: string;
    ClearHeightMinM?: string;
    ClearHeightMaxM?: string;
    ReservedParkingSpacesRatePerMonth?: string;
    UnreservedParkingSpacesRatePerMonth?: string;
    ContributedGBA_SF?: string;
    ContributedGBA_SM?: string;
    GRESBScoreMin?: string;
    GRESBScoreMax?: string;
    Mezzanine_Size_SF?: string;
    Mezzanine_Size_SM?: string;
    GLA_SF?: string;
    GLA_SM?: string;
    GLAR_SF?: string;
    GLAR_SM?: string;
    Awnings_Size_SF?: string;
    Awnings_Size_SM?: string;
    EstCompletionDate?: string;
    ClassTypeName?: string;
    TenancyName?: string;
    CondoTypeName?: string;
    BldgSizeSourceName?: string;
    EnergyStarRatingName?: string;
    WaterStarRatingName?: string;
    GreenStarRatingName?: string;
    ConstructionTypeName?: string;
    ConstructionStatusName?: string;
    HVACTypeName?: string;
    SprinklerTypeName?: string;
    BuildSpecStatusName?: string;
    RoofTypeName?: string;
    IsCraneServed?: boolean;
    GovernmentInterestName?: string;
    PowerTypeName?: string;
    TypicalFloorSizeSourceID?: string;
    HardstandAreaSourceName?: string;
    NRASizeSourceID?: string;
    NRASizeSourceName?: string;
    ContributedGBASizeSourceName?: string;
    GLASizeSourceName?: string;
    GLARSizeSourceName?: string;
    LotSizeSourceName?: string;
}

