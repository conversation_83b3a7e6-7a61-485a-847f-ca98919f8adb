/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PropertySearchResponseBaseDTO } from './propertySearchResponseBaseDTO';


export interface PropertySearchAllResponseDTO { 
    LastReviewedBy?: number;
    LastReviewedDate?: string;
    HasNoBuildingFootprints?: boolean;
    HasNoExistingParcelInTileLayer?: boolean;
    StrataProperties?: Array<PropertySearchResponseBaseDTO>;
    IsSkipped?: boolean;
    AuditStatus?: string;
    Floors?: number;
    YearBuilt?: number;
    MainPhotoUrl?: string;
    CondoUnit?: string;
    CreatedBy?: number;
    ModifiedBy?: number;
    CreatedDate?: string;
    ModifiedDate?: string;
    IsActive?: boolean;
    ResearchTypeID?: number;
    ResearchTypeName?: string;
    Comments?: string;
    MasterPropertyID?: number;
    LotSizeSF?: number;
    LotSizeSM?: number;
    ContributedGBASizeSF?: number;
    LotSizeSMFormatted?: string;
    BuildingSizeSM?: number;
    BuildingSizeSMFormatted?: string;
    ContributedGBA_SM?: number;
    ContributedGBA_SMFormatted?: string;
}

