/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchDetailsResponseDTO { 
    PropertyID?: number;
    ListingID?: number;
    IsActive?: boolean;
    PropertyName?: string;
    City?: string;
    County?: string;
    ZipCode?: string;
    PropertyType?: string;
    SpecificUses?: string;
    Address?: string;
    BuildingSize?: number;
    BuildingSizeSM?: string;
    Longitude?: number;
    Latitude?: number;
    MainPhotoUrl?: string;
    MediaUrl?: string;
    YearBuilt?: number;
    Floors?: number;
    PropertyComments?: string;
    State?: string;
    Country?: string;
    BuildingComments?: string;
    MarketName?: string;
    SubMarketName?: string;
    ConstructionStatusID?: PropertySearchDetailsResponseDTO.ConstructionStatusIDEnum;
    TotalAvailable?: string;
    TotalAvailableForSort?: number;
    TotalAvailableSM?: string;
    ListingTypeName?: string;
    AskingRate?: string;
    ListingCompanyName?: string;
    AgentName?: string;
    AskingSalePrice?: number;
    RecordTypeName?: string;
    Price?: string;
    SalePricePerSF?: number;
    SalePricePerSM?: string;
    TotalVacant?: number;
    TotalVacantSM?: string;
    AskingLeaseRatePerYrText?: string;
    LeaseTypeName?: string;
    CondoTypeID?: PropertySearchDetailsResponseDTO.CondoTypeIDEnum;
    Owners?: string;
    CountryCode?: string;
    StateCode?: string;
    CondoUnit?: string;
    MasterPropertyID?: number;
    HasNoBuildingFootprints?: boolean;
    ContributedGBA_SF?: number;
    ContributedGBA_SM?: string;
    ClassTypeName?: PropertySearchDetailsResponseDTO.ClassTypeNameEnum;
    MinDiv?: number;
    MinDivSM?: string;
    StreetNumberMinN?: number;
    StreetNumberMaxN?: number;
}
export namespace PropertySearchDetailsResponseDTO {
    export const ConstructionStatusIDEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusIDEnum = typeof ConstructionStatusIDEnum[keyof typeof ConstructionStatusIDEnum];
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
    export const ClassTypeNameEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type ClassTypeNameEnum = typeof ClassTypeNameEnum[keyof typeof ClassTypeNameEnum];
}


