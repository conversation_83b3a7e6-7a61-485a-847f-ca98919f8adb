/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { CircleRadiusDTO } from './circleRadiusDTO';


export interface PropertySearchECRERequestDTO { 
    CitySearchText?: Array<string>;
    CountySearchText?: Array<string>;
    ZipcodeSearchText?: Array<string>;
    SubLeaseFilter?: number;
    CoWorkingFilter?: number;
    Page: number;
    PageSize?: number;
    StrataFilter?: Array<PropertySearchECRERequestDTO.StrataFilterEnum>;
    Fitout?: boolean;
    SearchCriteriaJSON?: string;
    SelectedPolygon?: string;
    CountryId?: number;
    SortBy?: string;
    SortDirection?: string;
    IsMapSearch?: boolean;
    PolygonText?: string;
    PolygonTextArray?: Array<string>;
    AllLatlngArray?: Array<string>;
    LatlngArray?: string;
    CentreLatitude?: number;
    CentreLongitude?: number;
    CircleRadius?: number;
    CircleRadiusArray?: Array<CircleRadiusDTO>;
    CircleRadiusJSON?: string;
    SalePriceMin?: number;
    SalePriceMax?: number;
    ConstructionStatuses?: Array<PropertySearchECRERequestDTO.ConstructionStatusesEnum>;
    AmenitiesTypes?: Array<PropertySearchECRERequestDTO.AmenitiesTypesEnum>;
    SearchValue?: string;
    SpecificUseIds?: Array<number>;
    PropertyTypes?: Array<number>;
    BuildingClasses?: Array<PropertySearchECRERequestDTO.BuildingClassesEnum>;
    TenancyIds?: Array<PropertySearchECRERequestDTO.TenancyIdsEnum>;
    ListingType: string;
    PropertyIds?: Array<number>;
    PropertyName?: string;
    BuildingSizeMax?: number;
    BuildingSizeMin?: number;
    TotalAvailableMax?: number;
    TotalAvailableMin?: number;
    LotSizeMin?: number;
    LotSizeMax?: number;
    StateID: number;
    GreenStar?: Array<PropertySearchECRERequestDTO.GreenStarEnum>;
    ZipCodes?: Array<string>;
    CountyIds?: Array<number>;
    CityIds?: Array<number>;
    Ownership?: string;
    OwnerOccupiedStatus?: boolean;
    UserActivityJSON?: object;
    BuildingSizeSFMax?: number;
    BuildingSizeSFMin?: number;
    LotSizeSFMin?: number;
    LotSizeSFMax?: number;
    CreatedDateMax?: string;
    CreatedDateMaxFormat?: string;
    CreatedDateMin?: string;
    CreatedDateMinFormat?: string;
    SuiteLevel?: boolean;
    CompanyIds?: Array<number>;
    AgentIds?: Array<number>;
    SaleTypeIds?: Array<PropertySearchECRERequestDTO.SaleTypeIdsEnum>;
    AgreementTypeIds?: Array<PropertySearchECRERequestDTO.AgreementTypeIdsEnum>;
    SaleSizeMin?: number;
    SaleSizeMax?: number;
    SaleSizeSFMin?: number;
    SaleSizeSFMax?: number;
    LeaseRateMin?: number;
    LeaseRateMax?: number;
    IsContiguous?: boolean;
    LeaseRateTypeIds?: Array<number>;
    IsFullFloor?: boolean;
    Nelat?: number;
    Nelng?: number;
    Swlat?: number;
    Swlng?: number;
    MarketList?: Array<number>;
    SubMarketList?: Array<number>;
    NabersEnergy?: Array<PropertySearchECRERequestDTO.NabersEnergyEnum>;
    NabersWater?: Array<PropertySearchECRERequestDTO.NabersWaterEnum>;
    GresbscoreMin?: number;
    GresbscoreMax?: number;
}
export namespace PropertySearchECRERequestDTO {
    export const StrataFilterEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type StrataFilterEnum = typeof StrataFilterEnum[keyof typeof StrataFilterEnum];
    export const ConstructionStatusesEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusesEnum = typeof ConstructionStatusesEnum[keyof typeof ConstructionStatusesEnum];
    export const AmenitiesTypesEnum = {
        BathroomAmenities: 'BATHROOM_AMENITIES',
        BookableParkingBays: 'BOOKABLE_PARKING_BAYS',
        CarShareServices: 'CAR_SHARE_SERVICES',
        ChildcareCenter: 'CHILDCARE_CENTER',
        CloseByCafe: 'CLOSE_BY_CAFE',
        CloseByRestaurantsBars: 'CLOSE_BY_RESTAURANTS_BARS',
        CloseByRetail: 'CLOSE_BY_RETAIL',
        CloseByTransportLinks: 'CLOSE_BY_TRANSPORT_LINKS',
        CommunalWorkspace: 'COMMUNAL_WORKSPACE',
        Concierge: 'CONCIERGE',
        DisabledAmenities: 'DISABLED_AMENITIES',
        EvChargingStations: 'EV_CHARGING_STATIONS',
        EmployeeHealthOffers: 'EMPLOYEE_HEALTH_OFFERS',
        EndOfTrip: 'END_OF_TRIP',
        FunctionRooms: 'FUNCTION_ROOMS',
        KitchenAmenities: 'KITCHEN_AMENITIES',
        LandscapedGardens: 'LANDSCAPED_GARDENS',
        OffSiteParking: 'OFF_SITE_PARKING',
        OnSiteBuildingManagement: 'ON_SITE_BUILDING_MANAGEMENT',
        OnSiteCafe: 'ON_SITE_CAFE',
        OnSiteGym: 'ON_SITE_GYM',
        OnSiteParking: 'ON_SITE_PARKING',
        OnSiteRestaurantsBar: 'ON_SITE_RESTAURANTS_BAR',
        OnSiteRetail: 'ON_SITE_RETAIL',
        OnSiteSecurity: 'ON_SITE_SECURITY',
        RooftopTerrace: 'ROOFTOP_TERRACE',
        TaxiStand: 'TAXI_STAND',
        WellnessCentre: 'WELLNESS_CENTRE',
        WheelchairAccessibility: 'WHEELCHAIR_ACCESSIBILITY'
    } as const;
    export type AmenitiesTypesEnum = typeof AmenitiesTypesEnum[keyof typeof AmenitiesTypesEnum];
    export const BuildingClassesEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type BuildingClassesEnum = typeof BuildingClassesEnum[keyof typeof BuildingClassesEnum];
    export const TenancyIdsEnum = {
        Single: 'SINGLE',
        Multiple: 'MULTIPLE'
    } as const;
    export type TenancyIdsEnum = typeof TenancyIdsEnum[keyof typeof TenancyIdsEnum];
    export const GreenStarEnum = {
        Zero: 'ZERO',
        One: 'ONE',
        Two: 'TWO',
        Three: 'THREE',
        Four: 'FOUR',
        Five: 'FIVE',
        Six: 'SIX'
    } as const;
    export type GreenStarEnum = typeof GreenStarEnum[keyof typeof GreenStarEnum];
    export const SaleTypeIdsEnum = {
        Investment: 'INVESTMENT',
        OwnerOccupied: 'OWNER_OCCUPIED',
        VacantPossession: 'VACANT_POSSESSION',
        TransferOfOwnership: 'TRANSFER_OF_OWNERSHIP'
    } as const;
    export type SaleTypeIdsEnum = typeof SaleTypeIdsEnum[keyof typeof SaleTypeIdsEnum];
    export const AgreementTypeIdsEnum = {
        Conjunction: 'CONJUNCTION',
        Exclusive: 'EXCLUSIVE',
        Open: 'OPEN'
    } as const;
    export type AgreementTypeIdsEnum = typeof AgreementTypeIdsEnum[keyof typeof AgreementTypeIdsEnum];
    export const NabersEnergyEnum = {
        One: 'ONE',
        OnePointFive: 'ONE_POINT_FIVE',
        Two: 'TWO',
        TwoPointFive: 'TWO_POINT_FIVE',
        Three: 'THREE',
        ThreePointFive: 'THREE_POINT_FIVE',
        Four: 'FOUR',
        FourPointFive: 'FOUR_POINT_FIVE',
        Five: 'FIVE',
        FivePointFive: 'FIVE_POINT_FIVE',
        Six: 'SIX'
    } as const;
    export type NabersEnergyEnum = typeof NabersEnergyEnum[keyof typeof NabersEnergyEnum];
    export const NabersWaterEnum = {
        One: 'ONE',
        OnePointFive: 'ONE_POINT_FIVE',
        Two: 'TWO',
        TwoPointFive: 'TWO_POINT_FIVE',
        Three: 'THREE',
        ThreePointFive: 'THREE_POINT_FIVE',
        Four: 'FOUR',
        FourPointFive: 'FOUR_POINT_FIVE',
        Five: 'FIVE',
        FivePointFive: 'FIVE_POINT_FIVE'
    } as const;
    export type NabersWaterEnum = typeof NabersWaterEnum[keyof typeof NabersWaterEnum];
}


