/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchLeaseAndSaleResponseDTO { 
    ClassTypeID?: PropertySearchLeaseAndSaleResponseDTO.ClassTypeIDEnum;
    StreetNumberMinN?: number;
    StreetNumberMaxN?: number;
    AskingLeaseRatePerYearMin?: number;
    AskingLeaseRatePerYearMax?: number;
    TenancyTypeID?: PropertySearchLeaseAndSaleResponseDTO.TenancyTypeIDEnum;
    LeaseTypeID?: number;
    LotSizeSF?: number;
    LotSizeSM?: number;
    CompanyID?: number;
    EntityId?: number;
    SupportRoleTypeID?: PropertySearchLeaseAndSaleResponseDTO.SupportRoleTypeIDEnum;
    IsCondoSale?: boolean;
    ConstructionStatusID?: PropertySearchLeaseAndSaleResponseDTO.ConstructionStatusIDEnum;
    ListingStatusID?: number;
    ListingMangedByProviderStatus?: number;
    IsInternalListing?: boolean;
    CreatedDate?: string;
}
export namespace PropertySearchLeaseAndSaleResponseDTO {
    export const ClassTypeIDEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type ClassTypeIDEnum = typeof ClassTypeIDEnum[keyof typeof ClassTypeIDEnum];
    export const TenancyTypeIDEnum = {
        Single: 'SINGLE',
        Multiple: 'MULTIPLE'
    } as const;
    export type TenancyTypeIDEnum = typeof TenancyTypeIDEnum[keyof typeof TenancyTypeIDEnum];
    export const SupportRoleTypeIDEnum = {
        Researcher: 'RESEARCHER',
        SalesRep: 'SALES_REP',
        SupportAgent: 'SUPPORT_AGENT'
    } as const;
    export type SupportRoleTypeIDEnum = typeof SupportRoleTypeIDEnum[keyof typeof SupportRoleTypeIDEnum];
    export const ConstructionStatusIDEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusIDEnum = typeof ConstructionStatusIDEnum[keyof typeof ConstructionStatusIDEnum];
}


