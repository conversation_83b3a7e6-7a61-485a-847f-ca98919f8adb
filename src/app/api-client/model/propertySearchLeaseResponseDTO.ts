/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchLeaseResponseDTO { 
    ListingType?: PropertySearchLeaseResponseDTO.ListingTypeEnum;
    IsActive?: boolean;
    ClassTypeID?: PropertySearchLeaseResponseDTO.ClassTypeIDEnum;
    PropertyComments?: string;
    ListingStatusId?: number;
    YearBuilt?: number;
    Floors?: number;
    BuildingComments?: string;
    MainPhotoUrl?: string;
    CreatedDate?: string;
    CreatedBy?: number;
    ModifiedBy?: number;
    AdditionalAgents?: string;
}
export namespace PropertySearchLeaseResponseDTO {
    export const ListingTypeEnum = {
        Direct: 'DIRECT',
        Sublease: 'SUBLEASE',
        Exclusive: 'EXCLUSIVE',
        PortfolioSale: 'PORTFOLIO_SALE',
        CoWorking: 'CO_WORKING'
    } as const;
    export type ListingTypeEnum = typeof ListingTypeEnum[keyof typeof ListingTypeEnum];
    export const ClassTypeIDEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type ClassTypeIDEnum = typeof ClassTypeIDEnum[keyof typeof ClassTypeIDEnum];
}


