/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchRequestDTO { 
    PropertyTypes?: Array<number>;
    ListingType?: string;
    CityIds?: Array<number>;
    ZipCodes?: Array<string>;
    SpecificUseIds?: Array<number>;
    BuildingSizeMin?: number;
    BuildingSizeMax?: number;
    BuildingClasses?: Array<PropertySearchRequestDTO.BuildingClassesEnum>;
    PropertyName?: string;
    StreetMin?: string;
    StreetMax?: string;
    StreetName?: string;
    Page?: number;
    PageSize?: number;
    SortBy?: string;
    SortDirection?: string;
    PropertyId?: number;
    PropertyIds?: Array<number>;
    Tenancy?: Array<PropertySearchRequestDTO.TenancyEnum>;
    LotSizeSFMin?: number;
    LotSizeSFMax?: number;
    ShowOnlyListingsAssignedToMe?: boolean;
    StrataFilter?: number;
    ConstructionStatus?: Array<PropertySearchRequestDTO.ConstructionStatusEnum>;
    ModifiedDateMin?: string;
    ModifiedDateMax?: string;
    StateID?: number;
    CreatedDateMin?: string;
    CreatedDateMax?: string;
    LoginEntityId?: number;
    IsSkipped?: boolean;
    ResearchStatusIds?: Array<number>;
    StrataTypeIds?: Array<PropertySearchRequestDTO.StrataTypeIdsEnum>;
    ExcludeHidden?: boolean;
    ResearcherIds?: Array<number>;
    AuditStatus?: Array<number>;
    IsNotReviewed?: boolean;
    LastReviewedDateMin?: string;
    LastReviewedDateMax?: string;
    HasNoExistingParcelInTileLayer?: boolean;
    HasNoBuildingFootprints?: boolean;
    NotStrata?: boolean;
    ListingStatusIds?: Array<number>;
    LeaseRateMin?: number;
    LeaseRateMax?: number;
    TotalAvailableMin?: number;
    TotalAvailableMax?: number;
    SuiteLevel?: boolean;
    IsContiguous?: boolean;
    MetroId?: number;
    CountryId?: number;
    CouncilId?: number;
    SubMarketId?: number;
    NeighbourhoodId?: number;
    YearBuilt?: number;
    ParcelNumber?: number;
    SalePriceMin?: number;
    SalePriceMax?: number;
    SubLeaseCategory?: string;
    PolygonText?: string;
    Radius?: string;
    StartingIndex?: number;
    OffSetValue?: number;
    IsMapSearch?: boolean;
    SalePricePerSFMin?: number;
    SalePricePerSFMax?: number;
    CompanyIds?: Array<number>;
    CurrentLatitude?: number;
    CurrentLongitude?: number;
    IsTotalAvailable?: boolean;
    AgentIds?: Array<number>;
    LeaseRateTypeIds?: Array<number>;
    ListingID?: number;
    Floors?: number;
    CompanyId?: number;
    IncludeStrataPropertiesForMaster?: boolean;
}
export namespace PropertySearchRequestDTO {
    export const BuildingClassesEnum = {
        A: 'A',
        B: 'B',
        C: 'C',
        Pre: 'PRE',
        Prime: 'PRIME',
        Secondary: 'SECONDARY'
    } as const;
    export type BuildingClassesEnum = typeof BuildingClassesEnum[keyof typeof BuildingClassesEnum];
    export const TenancyEnum = {
        Single: 'SINGLE',
        Multiple: 'MULTIPLE'
    } as const;
    export type TenancyEnum = typeof TenancyEnum[keyof typeof TenancyEnum];
    export const ConstructionStatusEnum = {
        Planned: 'PLANNED',
        UnderConstruction: 'UNDER_CONSTRUCTION',
        UnderRenovation: 'UNDER_RENOVATION',
        Completed: 'COMPLETED',
        Proposed: 'PROPOSED',
        Tbd: 'TBD',
        Demolished: 'DEMOLISHED',
        Obsolete: 'OBSOLETE',
        Existing: 'EXISTING'
    } as const;
    export type ConstructionStatusEnum = typeof ConstructionStatusEnum[keyof typeof ConstructionStatusEnum];
    export const StrataTypeIdsEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type StrataTypeIdsEnum = typeof StrataTypeIdsEnum[keyof typeof StrataTypeIdsEnum];
}


