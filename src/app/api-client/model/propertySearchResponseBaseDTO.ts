/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PropertySearchAllResponseDTO } from './propertySearchAllResponseDTO';
import { PropertySearchLeaseAndSaleResponseDTO } from './propertySearchLeaseAndSaleResponseDTO';
import { PropertySearchSaleResponseDTO } from './propertySearchSaleResponseDTO';
import { AddressDTO } from './addressDTO';
import { LocationDTO } from './locationDTO';
import { PropertySearchLeaseResponseDTO } from './propertySearchLeaseResponseDTO';


export interface PropertySearchResponseBaseDTO { 
    AgentName?: string;
    AskingLeaseRatePerYrText?: string;
    AskingRate?: number;
    AskingSalePrice?: number;
    BuildingSizeSF?: number;
    BuildingSizeSM?: number;
    CityName?: string;
    CondoTypeID?: PropertySearchResponseBaseDTO.CondoTypeIDEnum;
    LeaseTypeName?: string;
    ListingCompanyName?: string;
    ListingID?: number;
    ModDateCounter?: number;
    ModifiedDate?: string;
    Price?: number;
    PropertyID?: number;
    PropertyName?: string;
    UseTypeID?: number;
    UseTypeName?: string;
    RecordTypeName?: PropertySearchResponseBaseDTO.RecordTypeNameEnum;
    SalePricePerSF?: number;
    SalePricePerSM?: number;
    SpecificUseID?: number;
    SpecificUseName?: string;
    StateName?: string;
    TotalAvailable?: number;
    TotalAvailableSM?: number;
    Address?: AddressDTO;
    Location?: LocationDTO;
    AllPropertiesDetails?: PropertySearchAllResponseDTO;
    LeasePropertiesDetails?: PropertySearchLeaseResponseDTO;
    SalePropertiesDetails?: PropertySearchSaleResponseDTO;
    LeaseAndSalePropertiesDetails?: PropertySearchLeaseAndSaleResponseDTO;
}
export namespace PropertySearchResponseBaseDTO {
    export const CondoTypeIDEnum = {
        NotStrata: 'NOT_STRATA',
        Strata: 'STRATA',
        MasterStrataRecord: 'MASTER_STRATA_RECORD',
        MasterFreehold: 'MASTER_FREEHOLD',
        ChildFreehold: 'CHILD_FREEHOLD'
    } as const;
    export type CondoTypeIDEnum = typeof CondoTypeIDEnum[keyof typeof CondoTypeIDEnum];
    export const RecordTypeNameEnum = {
        Lease: 'LEASE',
        Sale: 'SALE'
    } as const;
    export type RecordTypeNameEnum = typeof RecordTypeNameEnum[keyof typeof RecordTypeNameEnum];
}


