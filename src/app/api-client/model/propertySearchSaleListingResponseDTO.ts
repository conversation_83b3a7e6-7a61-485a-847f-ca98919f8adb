/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchSaleListingResponseDTO { 
    PropertyID?: number;
    ListingID?: number;
    PropertyName?: string;
    Address?: string;
    City?: string;
    ListingStatusName?: string;
    ListingStatusID?: number;
    GeneralUseID?: number;
    GeneralUse?: string;
    StrataUnit?: string;
    SaleSize?: number;
    SaleSizeSM?: number;
    AskingSalePrice?: number;
    Vacant?: boolean;
    SaleTypeID?: number;
    SaleTypeName?: string;
    CompanyName?: string;
    AgentName?: string;
    ModifiedDate?: string;
    ExclFromAvailability?: boolean;
    ListingMangedByProviderStatus?: number;
    IsInternalListing?: boolean;
    ResearcherEntityID?: number;
    ModDateCounter?: number;
}

