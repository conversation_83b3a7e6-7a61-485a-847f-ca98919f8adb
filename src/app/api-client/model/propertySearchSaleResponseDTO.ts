/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Sale listing type property search response
 */
export interface PropertySearchSaleResponseDTO { 
    Floors?: number;
    PropertyComments?: string;
    CreatedDate?: string;
    AdditionalBrokers?: string;
    ListingTypeID?: PropertySearchSaleResponseDTO.ListingTypeIDEnum;
    YearBuilt?: number;
    MainPhotoUrl?: string;
    IsActive?: boolean;
    BuildingComments?: string;
    ListingStatusId?: number;
    TotalVacant?: number;
    TotalVacantSM?: number;
    IsCondoSale?: boolean;
}
export namespace PropertySearchSaleResponseDTO {
    export const ListingTypeIDEnum = {
        Direct: 'DIRECT',
        Sublease: 'SUBLEASE',
        Exclusive: 'EXCLUSIVE',
        PortfolioSale: 'PORTFOLIO_SALE',
        CoWorking: 'CO_WORKING'
    } as const;
    export type ListingTypeIDEnum = typeof ListingTypeIDEnum[keyof typeof ListingTypeIDEnum];
}


