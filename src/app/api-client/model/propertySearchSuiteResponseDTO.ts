/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface PropertySearchSuiteResponseDTO { 
    PropertyID?: number;
    ListingID?: number;
    SuiteID?: number;
    PropertyName?: string;
    Address?: string;
    City?: string;
    GeneralUseID?: number;
    GeneralUse?: string;
    SpaceUseTypeName?: string;
    SpaceUseTypeID?: number;
    SuiteStatusID?: number;
    SuiteStatusName?: string;
    SpaceTypeID?: number;
    SpaceTypeName?: string;
    SuiteNumber?: string;
    AvailableSF?: number;
    AvailableSM?: number;
    AskingRateText?: string;
    MinDivSF?: number;
    MinDiv?: number;
    ExclAvailable?: boolean;
    IsContiguous?: boolean;
    CompanyName?: string;
    AgentName?: string;
    ModifiedDate?: string;
    IsVacant?: boolean;
    AskingRateHigh?: number;
    AskingRateLow?: number;
    ListingMangedByProviderStatus?: number;
    IsInternalListing?: boolean;
    ModDateCounter?: number;
    FloorNumber?: number;
    TIAllowance?: number;
}

