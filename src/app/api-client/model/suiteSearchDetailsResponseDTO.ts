/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface SuiteSearchDetailsResponseDTO { 
    PropertyID?: number;
    ListingID?: number;
    SuiteID?: number;
    PropertyName?: string;
    Address?: string;
    BuildingSize?: number;
    BuildingSizeSM?: string;
    ListingCompanyName?: string;
    AgentName?: string;
    FloorNumber?: string;
    SuiteNumber?: string;
    AvailableSF?: number;
    AvailableSM?: string;
    MaxSize?: number;
    MaxSizeSM?: number;
    AskingRateText?: string;
    IsVacant?: boolean;
    PossessionTypeID?: SuiteSearchDetailsResponseDTO.PossessionTypeIDEnum;
    DateAvailable?: string;
    SpaceUseTypeName?: SuiteSearchDetailsResponseDTO.SpaceUseTypeNameEnum;
    DateOnMarket?: string;
    ExclAvailable?: boolean;
    IsContiguous?: boolean;
    TiAllowance?: number;
    Fitout?: boolean;
    MinSF?: number;
    MinSM?: string;
}
export namespace SuiteSearchDetailsResponseDTO {
    export const PossessionTypeIDEnum = {
        ThirtyDays: 'THIRTY_DAYS',
        SixtyDays: 'SIXTY_DAYS',
        NinetyDays: 'NINETY_DAYS',
        CloseOfEscrow: 'CLOSE_OF_ESCROW',
        Completion: 'COMPLETION',
        Date: 'DATE',
        Now: 'NOW'
    } as const;
    export type PossessionTypeIDEnum = typeof PossessionTypeIDEnum[keyof typeof PossessionTypeIDEnum];
    export const SpaceUseTypeNameEnum = {
        Office: 'OFFICE',
        Flex: 'FLEX',
        Hospitality: 'HOSPITALITY',
        Industrial: 'INDUSTRIAL',
        Land: 'LAND',
        Medical: 'MEDICAL',
        MultiFamily: 'MULTI_FAMILY',
        Other: 'OTHER',
        Retail: 'RETAIL',
        SpecialUse: 'SPECIAL_USE',
        Telecom: 'TELECOM'
    } as const;
    export type SpaceUseTypeNameEnum = typeof SpaceUseTypeNameEnum[keyof typeof SpaceUseTypeNameEnum];
}


