/**
 * Phoenix API Documentation
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface UserPreferenceRequestDTO { 
    Type?: UserPreferenceRequestDTO.TypeEnum;
    Data?: string;
    Screen?: UserPreferenceRequestDTO.ScreenEnum;
}
export namespace UserPreferenceRequestDTO {
    export const TypeEnum = {
        Download: 'DOWNLOAD',
        Customize: 'CUSTOMIZE',
        MailPreferences: 'MAIL_PREFERENCES'
    } as const;
    export type TypeEnum = typeof TypeEnum[keyof typeof TypeEnum];
    export const ScreenEnum = {
        Transaction: 'TRANSACTION',
        RequiredPreference: 'REQUIRED_PREFERENCE',
        Tenant: 'TENANT',
        ReportPreferences: 'REPORT_PREFERENCES',
        LeaseTransaction: 'LEASE_TRANSACTION',
        DashboardActivity: 'DASHBOARD_ACTIVITY',
        Property: 'PROPERTY',
        MarketBriefMessagePreferences: 'MARKET_BRIEF_MESSAGE_PREFERENCES',
        MarketBriefPreferences: 'MARKET_BRIEF_PREFERENCES',
        MarketBriefSuite: 'MARKET_BRIEF_SUITE',
        MarketBriefTour: 'MARKET_BRIEF_TOUR',
        CommercialAuMarketBriefResponse: 'COMMERCIAL_AU_MARKET_BRIEF_RESPONSE',
        RecentlyAddedListing: 'RECENTLY_ADDED_LISTING',
        RecentListingActivity: 'RECENT_LISTING_ACTIVITY',
        PurchaseLog: 'PURCHASE_LOG',
        ExecutionDetails: 'EXECUTION_DETAILS',
        Suites: 'SUITES'
    } as const;
    export type ScreenEnum = typeof ScreenEnum[keyof typeof ScreenEnum];
}


