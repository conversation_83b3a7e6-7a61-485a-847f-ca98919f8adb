import { BrowserModule } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { AngularMyDatePickerModule } from 'angular-mydatepicker';
import { MatRippleModule } from '@angular/material/core';
import { ServicesModule } from './services/services.module';
import { MapModule } from './modules/map-module/map.module';
import { AppRoutingModule } from './app.routing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormGroup, FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppComponent } from './app.component';
import { LoginComponent } from './pages/login/login.component';
import { EditpropertyComponent } from './pages/editproperty/editproperty.component';
import { UiSwitchModule } from 'ngx-ui-switch';
import { DirectiveModule } from './directives/directives.module';
import { NotificationModule } from './modules/notification/notification.module';
import {MillionPipe} from '../app/Pipes/million';
import { NgSelectModule } from '@ng-select/ng-select';
import { AWSModule } from './modules/aws/aws.module';
import { MediaContainerComponent} from './pages/media/media.component'
import {NotesComponent } from './pages/notes/notes.component'
import {SharedModule }    from './modules/shared/shared.module';
import { NgxGalleryModule } from 'ngx-gallery-9';
import { ModalModule } from './modules/modal-module/modal.module';
import { ImageViewerModule } from "ngx-image-viewer";
import { DatePipe } from '@angular/common';
import { HeaderComponent } from './pages/header/header.component';
import { ExpressMapsearchComponent } from './pages/express-mapsearch/express-mapsearchcomponent';
import { LinkPropertyLookupModalComponent } from './pages/link-property-lookup-modal/link-property-lookup-modal.component';
import { TableModule } from 'primeng/table';
import { StrataComponent } from './pages/strata/strata.component';
import { BackgroundMediaUploadToasterModule } from './modules/background-media-upload/background-media-upload-toaster.module'
import { PropertySearchComponent } from './pages/property-search/property-search.component';
import { PropertyParkingSpaceModalComponent } from './modules/modal-module/modals/property-parking-space-modal/property-parking-space-modal.component';
import { DistanceMeasurementModalComponent } from './modules/modal-module/modals/distance-measurement-modal/distance-measurement-modal.component';
import { AreaMeasurementModalComponent } from './modules/modal-module/modals/area-measurement-modal/distance-measurement-modal/area-measurement-modal.component';
import { HttpClientModule } from '@angular/common/http';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { AddMultiFreeholdComponent } from './add-multi-freehold/add-multi-freehold.component';
import { SelectParcelsForPropertyComponent } from './pages/select-parcels-for-property/select-parcels-for-property.component';
import { MapSwitchModule } from './pages/map-switch/map-switch.module';
import { LocationDetailsComponent } from './pages/location-details/location-details.component';
import { LegalInformationComponent } from './pages/legal-information/legal-information.component';
import { IntersectionInfoComponent } from './pages/intersection-info/intersection-info.component';
import { OfficePropertyDetailsComponent } from './components/office/office-property-details/office-property-details.component';
import { OccupancyInternalDetailsComponent } from './components/common/occupancy-internal-details/occupancy-internal-details.component';
import { ParkingLiftsDetailsComponent } from './components/office/parking-lifts-details/parking-lifts-details.component';
import { YardLotHeightDetailsComponent } from './components/industrial/yard-lot-height-details/yard-lot-height-details.component';
import { IndustrialPropertyDetailsComponent } from './components/industrial/industrial-property-details/industrial-property-details.component';
import { OtherBuildingDetailsComponent } from './components/common/other-building-details/other-building-details.component';
import { PropertyUseDetailsComponent } from './components/common/property-use-details/property-use-details.component';
import { PolygonCopyModalComponent } from './modules/modal-module/modals/polygon-copy-modal/polygon-copy-modal.component';
import { LandPropertyDetailsComponent } from './components/land/land-property-details/land-property-details.component';
import { RetailPropertyDetailsComponent } from './components/retail/retail-property-details/retail-property-details.component';
import { HeightLoadingDetailsComponent } from './components/retail/height-loading-details/height-loading-details.component';
import { PropertyDetailsComponent } from './components/property-details/property-details.component';
import { ChangeLogComponent } from './pages/change-log/change-log.component';
import { ResearchStatusHistoryComponent } from './pages/research-status-history/research-status-history.component';
import { PropertyAllocationDetailsComponent } from './components/common/property-allocation-details/property-allocation-details.component';
import { ScriptLoaderService } from '../scriptLoader';
import { environment } from '../environments/environment';
import { AdditionalUseDetailsComponent } from './components/additional-use-details/additional-use-details.component';
import { AdditionalAddressComponent } from './components/common/additional-address/additional-address.component';
import { ParcelListComponent } from './components/common/parcel-list/parcel-list.component';
import { ApiModule, Configuration } from './api-client';
import { AuthInterceptor } from '../app/interceptors/auth-interceptors'

export function initScripts(scriptLoader: any): () => Promise<void> {
  return () => {
    return Promise.all([
      scriptLoader.loadScript('assets/js/bootstrap.min.js'),
      scriptLoader.loadScript(`https://maps.googleapis.com/maps/api/js?libraries=marker,drawing,geometry,places&key=${environment.GoogleMapApiKey}&callback=Function.prototype`)
    ]).then(() => {
      console.log('All scripts loaded');
    }).catch((err) => {
      console.error(err);
    });
  };
}

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    EditpropertyComponent,
    MillionPipe,
    MediaContainerComponent,
    NotesComponent,
    HeaderComponent,
    ExpressMapsearchComponent,
    LinkPropertyLookupModalComponent,
    StrataComponent,
    PropertySearchComponent,
    PropertyParkingSpaceModalComponent,
    DistanceMeasurementModalComponent,
    AreaMeasurementModalComponent,
    AddMultiFreeholdComponent,
    SelectParcelsForPropertyComponent,
    LocationDetailsComponent,
    LegalInformationComponent,
    IntersectionInfoComponent,
    OfficePropertyDetailsComponent,
    OccupancyInternalDetailsComponent,
    ParkingLiftsDetailsComponent,
    YardLotHeightDetailsComponent,
    IndustrialPropertyDetailsComponent,
    OtherBuildingDetailsComponent,
    PropertyUseDetailsComponent,
    PolygonCopyModalComponent,
    LandPropertyDetailsComponent,
    RetailPropertyDetailsComponent,
    HeightLoadingDetailsComponent,
    PropertyDetailsComponent,
    ChangeLogComponent,
    ResearchStatusHistoryComponent,
    PropertyAllocationDetailsComponent,
    AdditionalUseDetailsComponent,
    AdditionalAddressComponent,
    ParcelListComponent
  ],
  imports: [
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    AppRoutingModule,
    ServicesModule,
    MatRippleModule,
    MatTabsModule,
    MapModule,
    UiSwitchModule,
    NotificationModule,
    DirectiveModule,
    BrowserAnimationsModule ,
    AWSModule,
    SharedModule,
    NgxGalleryModule,
    ModalModule, NgSelectModule,
    TableModule,
    ImageViewerModule.forRoot(),
    BackgroundMediaUploadToasterModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    ProgressSpinnerModule,
    MapSwitchModule,
    AngularMyDatePickerModule,
    ApiModule.forRoot(() => new Configuration({
      basePath: 'https://api-phoenix-dev.arealytics.com.au'
    })),
    // NgbModule
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initScripts,
      deps: [ScriptLoaderService],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    DatePipe
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
