export const YesOrNoList = [ { label: 'Yes', value: true }, { label: 'No', value: false }];

export const YesNoFields = [ 'HasSprinkler','HasReservedParkingSpaces', 'HasUnreservedParkingSpaces', 'HVAC', 'Mezzanine', 'Awnings',
  'HasYardFenced', 'HasYardUnfenced', 'YardPaved', 'IsFloodPlain', 'EarthquakeZoneID', 'RailServed', 'HasPortAccess',
];
export const IndustrialYesNoFields = [ 'HasYard', 'HasYardFenced', 'YardPaved', 'RailServed', 'CraneServed', 'HasPortAccess', 'HasSprinkler', 'Lifts', 'IsVented'];
export const SourceDropdowns = ['HardstandAreaSourceID'];
export const SizeSourceID = 'SizeSourceID';

export const SourceFields = [ 'ContributedGBASizeSourceID', 'GLASizeSourceID', 'NRASizeSourceId', 'GLARSizeSourceID' ];
export const ZoningClasses = ['PotentialZoningID', 'SurroundingLandUse']

export const BindNamesAndValuesForPropertEntity = {
  TenancyTypeID: { name: 'TenancyName' , id: 'TenancyID' },
  BuildSpecStatusID: { name: 'BuildSpecStatusNames', id: 'BuildSpecStatusID' },
  HVAC: { name: 'label' , id: 'value' },
  HVACTypeID: { name: 'HVACTypeName' , id: 'HVACTypeID' },
  RoofTypeID: { name: 'RoofTypeName' , id: 'RoofTypeID' },
  GovernmentInterestID: { name: 'GovernmentInterestName' , id: 'GovernmentInterestID' },
  HasSprinkler: { name: 'label' , id: 'value' },
  AmenitiesTypeID: {},
  HasReservedParkingSpaces: { name: 'label' , id: 'value' },
  HasUnreservedParkingSpaces: { name: 'label' , id: 'value' },
  SprinklerTypeID: { name: 'SprinklerTypeName' , id: 'SprinklerTypeID' },
  HasYard: { name: 'label' , id: 'value' },
  HasYardFenced: { name: 'label' , id: 'value' },
  HasYardUnfenced: { name: 'label' , id: 'value' },
  IsFloodPlain: { name: 'label' , id: 'value' },
  EarthquakeZoneID: { name: 'label' , id: 'value' },
  YardPaved: { name: 'label' , id: 'value' },
  HardstandAreaSourceID: { name: 'SizeSourceName', id: 'SizeSourceID'},
  RailServed: { name: 'label' , id: 'value' },
  CraneServed: { name: 'label' , id: 'value' },
  HasPortAccess: { name: 'label' , id: 'value' },
  BuildSuitSpec: { name: 'BuildSpecStatusNames', id: 'BuildSpecStatusID' },
  SpecificUseID: { name: 'SpecificUsesName', id:'SpecificUsesID', useType: 'UseTypeID'},
  BuildingClass: { name:'ClassTypeName', id:'BuildingClassID', useType: 'UseTypeID'},
  EnergyStarRatingID: { name:'EnergyStarRatingName', id:'NABERSCertified' },
  ContributedGBASizeSourceID: { name:'SizeSourceName', id:'SizeSourceID' },
  GLASizeSourceID: { name:'SizeSourceName', id:'SizeSourceID' },
  NRASizeSourceId: { name:'SizeSourceName', id:'SizeSourceID' },
  GLARSizeSourceID: { name:'SizeSourceName', id:'SizeSourceID' },
  NabersWater: { name: 'EnergyStarRatingName', id:'NABERSCertified'},
  GreenStarRatingID: { name:'GreenStarRatingName', id:'GreenStarRatingID'},
  Mezzanine: { name: 'label', id: 'value'},
  Awnings: { name: 'label', id: 'value' },
  WaterStarRatingID: { name:'WaterStarRatingName', id:'NABERSWaterCertified'},
  PowerType: { name : 'PowerTypeName', id: 'PowerTypeID'},
  SurroundingLandUse: { name: 'ZoningClassName', id: 'ZoningClassID'},
  PotentialZoningID: { name: 'ZoningClassName', id: 'ZoningClassID'},
  LandUse: { name: 'LandUseName', id: 'LandUseID' },
  IsVented: { name: 'label', id:'value'},
  TypicalFloorSizeSourceID: { name: 'TypicalFloorPlateName', id:'TypicalFloorPlateID' },
  OfficeHVAC: { name: 'OfficeHVACName', id: 'OfficeHVACID' },
};

export const BindNamesWithLookupName = {
  TenancyTypeID: 'TenancyID',
  GovernmentInterestID: 'GovernmentInterestID',
  BuildSpecStatusID: 'BuildSpecStatusID',
  HVAC: 'HVAC',
  HVACTypeID: 'HVACTypeID',
  RoofTypeID: 'RoofTypeID',
  HasSprinkler: 'HasSprinkler',
  SprinklerTypeID: 'SprinklerTypeID',
  Lifts: 'Lifts',
  Features: 'FeatureID',
  PowerType: 'PowerTypeID',
  BuildingClass: 'BuildingClassID',
  SpecificUseID: 'SpecificUsesID',
  ContributedGBASizeSourceID: 'SizeSourceID',
  GLASizeSourceID: 'SizeSourceID',
  NRASizeSourceId: 'SizeSourceID',
  GLARSizeSourceID: 'SizeSourceID',
  EnergyStarRatingID: 'NABERSCertified',
  WaterStarRatingID: 'NABERSWaterCertified',
  Mezzanine: 'Mezzanine',
  Awnings: 'Awnings',
  HasYard: 'HasYard',
  HasYardFenced: 'HasYardFenced',
  YardPaved: 'YardPaved',
  HardstandAreaSourceID: 'SizeSourceID',
  RailServed: 'RailServed',
  CraneServed: 'CraneServed',
  HasPortAccess: 'HasPortAccess',
  LandUse: 'LandUseID',
  PotentialZoningID: 'ZoningClassID',
  HasYardUnfenced: 'HasYardUnfenced',
  SurroundingLandUse: 'ZoningClassID',
  IsFloodPlain: 'IsFloodPlain',
  EarthquakeZoneID: 'EarthquakeZoneID',
  HasReservedParkingSpaces: 'HasReservedParkingSpaces',
  HasUnreservedParkingSpaces: 'HasUnreservedParkingSpaces',
  IsVented: 'IsVented',
  GreenStarRatingID: 'GreenStarRatingID',
  AmenitiesTypeID: 'AmenitiesTypeID',
  TypicalFloorSizeSourceID: 'TypicalFloorPlateID',
  OfficeHVAC: 'OfficeHVACID'
}

export const OfficeChangeLogFields = {
  AmenitiesTypeID: 'AmenitiesTypeID'
};

export const IndustrialMultiSelectFields = {
  Features: 'Features'
}

export const BindFormControlToPropertyVariable = {
  ZoningCode: 'ZoningCode', 
  ConstructionStatus: 'ConstructionStatusID' ,
  ConstructionType: 'ConstructionTypeID',
  Floors: 'Floors',
  Condo: 'Condo',
  ContributedSourceComments: 'ContributedSourceComments',
  ContributedGBA_SF: 'ContributedGBA_SF',
  ContributedGBASource: 'ContributedGBASizeSourceID',
  GLA_SF: 'GLA_SF',
  IndustrialGLASource: 'GLASizeSourceID',
  OfficeSF: 'OfficeSF',
  OfficeNLASource: 'NrasizeSourceID',
  GLAR_SF: 'GLAR_SF',
  RetailGLARSource: 'GLARSizeSourceID',
  PropertyTypeName: 'UseTypeID',
}

export const BindLookupNameToVariable = {
  PrefixID: 'streetPrefixes',
  SprinklerTypeID: 'sprinklerTypes',
  BuildingClassID: 'buildingClass',
  NABERSCertified: 'NABERSList',
  StreetSuffix1: 'streetSufixes',
  QuadrantID: 'quadrants',
  CondoTypeID: 'condos',
  ConstructionStatusID: 'constructStatuses',
  RoofTypeID: 'roofTypes',
  SizeSourceID: 'sizeSource',
  ConstructionTypeID: 'constructTypes',
  ZoningClassID: 'zoningClasses',
  SpecificUsesID: 'allspecificuses',
  CityID: 'cities',
  StateID: 'states',
  CountryID: 'countryList',
  UseTypeID: 'propertyTypes',
  CouncilID: 'counties'
}

export const fieldUnitMap = {
  BuildingSF: { meter: 'BuildingSizeSM', feet: 'BuildingSF' },
  LotSizeSF: { meter: 'LotSizeSM', feet: 'LotSizeSF' },
  TotalAnchorSF: { meter: 'TotalAnchorSM', feet: 'TotalAnchorSF' },
  OfficeSF: { meter: 'OfficeSM', feet: 'OfficeSF' },
  RetailSF: { meter: 'RetailSM', feet: 'RetailSF' },
  SmallestFloor: { meter: 'SmallestFloorSM', feet: 'SmallestFloor' },
  LargestFloor: { meter: 'LargestFloorSM', feet: 'LargestFloor' },
  RetailFrontage: { meter: 'RetailFrontageM', feet: 'RetailFrontage' },
  ClearHeightMin: { meter: 'ClearHeightMinM', feet: 'ClearHeightMin' },
  ClearHeightMax: { meter: 'ClearHeightMaxM', feet: 'ClearHeightMax' },
  Depth: { meter: 'DepthM', feet: 'Depth' },
  Width: { meter: 'WidthM', feet: 'Width' },
  ContributedGBA_SF: { meter: 'ContributedGBA_SM', feet: 'ContributedGBA_SF' },
  Mezzanine_Size_SF: { meter: 'Mezzanine_Size_SM', feet: 'Mezzanine_Size_SF' },
  Awnings_Size_SF: { meter: 'Awnings_Size_SM', feet: 'Awnings_Size_SF' },
  GLA_SF: { meter: 'GLA_SM', feet: 'GLA_SF' },
  GLAR_SF: { meter: 'GLAR_SM', feet: 'GLAR_SF' },
  TypicalFloorSize: { meter: 'TypicalFloorSizeSM', feet: 'TypicalFloorSize' },
  HardstandArea: { meter: 'HardstandAreaSM', feet: 'HardstandArea' },
};

