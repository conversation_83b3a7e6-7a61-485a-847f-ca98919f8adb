// Angular core imports
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
// Third-party imports
import { Subscription } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
// Application-specific imports
import { LoginUserInfoDTO, PropertyDetailsDTO } from '../../../app/api-client';
import { MultiPolygon } from '../../models/Common';
import { PropertyAdditionalUse } from '../../models/propertyAdditionalUse';
import { CommunicationService } from '../../services/communication.service';
import { LoginService } from '../../services/login.service';
import { RollupObject } from '../../models/rollupObject';

@Component({
  selector: 'app-additional-use-details',
  templateUrl: './additional-use-details.component.html',
  styleUrls: ['./additional-use-details.component.css']
})
export class AdditionalUseDetailsComponent implements OnInit {
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  @Input() multifloors: MultiPolygon[];
  @Input() propertyTypes: any;
  @Input() allspecificuses: any;
  @Input() property: PropertyDetailsDTO;
  @Output() updateMultiFloors: EventEmitter<any> = new EventEmitter<any>();
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum;

  additionalUseList: any;
  updateAdditionalUsesListener: Subscription;
  clearAdditionalUsesListener: Subscription;
  additionalSpecificUseList: any = {};

  metricUnit : LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;
  UnitId: LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;

  constructor(
    private loginService: LoginService,
    private communicationService: CommunicationService
  ) {
    this.UnitId = this.loginService.UserInfo.UnitId;
    this.updateAdditionalUsesListener = this.communicationService.subscribe('updateAdditionalUses').subscribe(result => {
      if(!!result.data) {
        this.getAdditionalUses(result.data);
      }
    });
    this.clearAdditionalUsesListener = this.communicationService.subscribe('clearAdditionalUses').subscribe(result => {
      if(!!result.data) {
        this.additionalUseList = [];
        this.additionalSpecificUseList = {};
      }
    });
  }

  ngOnInit(): void {
  }

  getAdditionalUses(data) {
    const { multiFloors, useType } = data;
    const additionalSpecificUseList = {};
    // Filter floors with additional use and map them to include an isAdditionalUse property
    const additionalUseList: MultiPolygon[] = multiFloors.filter(floor => floor.additionalUse).map(use => ({ ...use, isAdditionalUse: true }));
    
    // Filter floors whose specific use does not match the main use type
    const mainUseList = multiFloors.filter(floor => floor.specificUse !== useType);
    
    // Combine additional and main use lists into a single list
    const usesList = [...additionalUseList, ...mainUseList];

    // Map each floor to a PropertyAdditionalUse object and populate the additionalSpecificUseList
    this.additionalUseList = usesList.map((floor) => {
      const propertyUse = this.propertyTypes.find((type) => floor.isAdditionalUse ? type.UseTypeID == floor.additionalUse : type.UseTypeID === floor.specificUse);
      const min = floor.minFloor ? floor.minFloor == 1 ? 'G' : floor.minFloor - 1 : null;
      const max = floor.maxFloor ? floor.maxFloor == 1 ? 'G' : floor.maxFloor - 1 : null;
      // Generate a section name combining floor range and property use label
      const sectionName =  propertyUse ? `${min ? min : ''} ${max ? ` - ${max}` : ''} (${propertyUse.UseTypeLabel})` : `${min ? min : ''} ${max ? ` - ${max}` : ''}`
      const data:PropertyAdditionalUse = {
        ... new PropertyAdditionalUse(),
        UseTypeID: floor.isAdditionalUse ? floor.additionalUse : floor.specificUse,
        UseTypeName: propertyUse?.UseTypeName,
        Floors: floor.floorCount,
        IsActive: 1,
        PolygonId: floor.BuildingFootPrintID ?? floor.localBuildingFootPrintID,
        SpecificUsesID: floor.isAdditionalUse ? floor.additionalSpecificUseTypeId : floor.mainSpecificUseTypeId,
        MinFloor: min,
        MaxFloor: max,
        Section: sectionName,
        floorSize: floor.floorSize,
        isAdditionalUse: floor.isAdditionalUse,
        PropertyAdditionalUseID: uuidv4()
      };
       // Populate the additionalSpecificUseList with matching uses from allspecificuses
      if(!!this.allspecificuses && this.allspecificuses.length > 0) {
        additionalSpecificUseList[data.PropertyAdditionalUseID] = [ ...this.allspecificuses ].filter((item) => {
          return floor.isAdditionalUse ? item.UseTypeID == floor.additionalUse : item.UseTypeID == floor.specificUse
        });
      }
      return data;
    });
    // Assign the populated additionalSpecificUseList to the class property
    this.additionalSpecificUseList = additionalSpecificUseList;
  }

  onChangeAdditionalSpecificUse(item) {
    if (this.multifloors && this.multifloors.length > 0 ) {
      this.multifloors.map(floor => {
        if (item.PolygonId == floor.BuildingFootPrintID || item.PolygonId == floor.localBuildingFootPrintID) {
          if(item.isAdditionalUse) {
            floor.additionalSpecificUseTypeId = item.SpecificUsesID;
          } else {
            floor.mainSpecificUseTypeId = item.SpecificUsesID;
          }
        }
        return item;
      });
    }
    this.updateMultiFloors.emit(true);
  }

}
