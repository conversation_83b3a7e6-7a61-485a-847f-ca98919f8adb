<div>
  <div class="col-md-12 p-0 expandBox">
    <div class="col-md-12 p-1 position-relative">
      <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" (click)="showAdditionalAddressSection()" data-target="#AdditionalAddress" aria-expanded='true'><i class="fa fa-building"></i>Additional Address
      </button>
      <input type="button" class="btn btn-primary right checkAbslt" style="right: 55px;" value="New Address" (click)="addAdditionalAddress()" />
    </div>
    <div class="row">
      <div id="AdditionalAddress" class="collapse show mb-2 mt-2 pl-2 pr-2" style="width:100%">
        <hr class="mt-0">
        <div class="col-md-12">
          <div class="form-group row m-0">
            <table class="table table-bordered table-striped table-sm" style="width: 100%;">
              <thead>
                <tr>
                  <th>Address</th>
                  <th>City</th>
                  <th>State</th>
                  <th>Postal code</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let address of additionalAddressList">
                  <td>{{address.AddressText}}</td>
                  <td>{{address.CityDisplayName}}</td>
                  <td>{{address.StateDisplayName}}</td>
                  <td>{{address.ZipCode}}</td>
                  <td class="icon-wrapper">
                    <img src="assets/images/edit.gif" (click)="updateAdditionalAddress(address)" />
                    <div (click)="deleteAdditionalAddressConfirmation(address)">
                      <i class="fas fa-trash deleteIcon" data-testId="additional-address-delete"></i>
                    </div>
                    <div (click)="viewChangeLog(address.AddressId)">
                      <i class="fa fa-history change-log-icon" aria-hidden="true" title="ChangeLog"></i>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<!--Additional Address modal-->
<div *ngIf="showAdditionalAddress">
  <imperium-modal [(visible)]="showAdditionalAddress" [title]="'Additonal Address'" [width]="'medium'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-additional-address-modal [property]="property" [editAdditionalAddress]="editAdditionalAddress" (onClose)="closeAdditionalAddressModal()" [propertyLookup]="propertyLookup">
      </app-additional-address-modal>
    </ng-template>
  </imperium-modal>
</div>

<div *ngIf="showChangeLogDetails">
  <imperium-modal [(visible)]="showChangeLogDetails" [title]="'Additional Address Changelog'" [width]="'large'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-changelog-modal [changelogType]="changelogType" [parentId]="parentId" class="change-log">
      </app-changelog-modal>
    </ng-template>
  </imperium-modal>
</div>
