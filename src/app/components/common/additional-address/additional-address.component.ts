// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
// Third-party imports
import { Subscription } from 'rxjs';
// Application-specific imports
import { AdditionalAddressDTO, ApiResponseListAdditionalAddressDTO, AuditLogEntityTypes, PropertyDetailsDTO } from '../../../../app/api-client';
import { confirmConfiguration } from '../../../modules/notification/models/confirmConfiguration';
import { CommunicationService } from '../../../services/communication.service';
import { SharedDataService } from '../../../services/shareddata.service';
import { AddressService } from '../../../services/address.service';
import { NotificationService } from '../../../modules/notification/service/notification.service';
import { HttpStatus } from '../../../enumerations/http-status-codes';
import { CommonStrings } from '../../../constants';

@Component({
  selector: 'app-additional-address',
  templateUrl: './additional-address.component.html',
  styleUrls: ['./additional-address.component.css']
})
export class AdditionalAddressComponent implements OnInit {
  @Input() property: PropertyDetailsDTO;
  @Input() propertyLookup: any;

  showAdditionalAddress: boolean;
  additionalAddressList: Array<AdditionalAddressDTO>;
  editAdditionalAddress: AdditionalAddressDTO;
  additionalAddrClick = false;

  fetchAdditionalAddressListener: Subscription;
  showChangeLogDetails = false;
  changelogType = AuditLogEntityTypes.AdditionalAddress;
  parentId = null;


  constructor(
    private _sharedDataService: SharedDataService,
    private _notificationService: NotificationService,
    private communicationService: CommunicationService,
    private addressService: AddressService
  ) {
    this.fetchAdditionalAddressListener = this.communicationService.subscribe('fetchAdditionalAddress').subscribe(result => {
      if (!!result.data ) {
        this.additionalAddressList = [];
        this.getAdditionalAddress(result.data, true);
      }
    });
   }

  ngOnInit(): void {
    this.additionalAddressList = [];
  }

  ngOnDestroy(): void {
    this._sharedDataService.additionalAddressList = [];
    this.fetchAdditionalAddressListener.unsubscribe();
  }

  showAdditionalAddressSection() {
    this.additionalAddrClick = true;
    this.getAdditionalAddress(this.property.PropertyID);
  }

  addAdditionalAddress() {
    this.editAdditionalAddress = {} as AdditionalAddressDTO;
    this.showAdditionalAddress = true;
  }

  getAdditionalAddress(propertyId, fetchNew = false) {
    if (!fetchNew && !!this._sharedDataService.additionalAddressList && this._sharedDataService.additionalAddressList.length > 0 && this._sharedDataService.additionalAddressList[0].PropertyID == propertyId) {
      this.additionalAddressList = this._sharedDataService.additionalAddressList;
    } else {
      const response_location = this.addressService.getAdditionalAddress(propertyId);
      response_location.subscribe((result: ApiResponseListAdditionalAddressDTO) => {
        if (!result.error) {
          const additionalAddress = result.responseData;
          if (additionalAddress.length > 0) {
            this.additionalAddressList = additionalAddress;
            this._sharedDataService.additionalAddressList = this.additionalAddressList;
            this._sharedDataService.additionalAddressList.forEach(element => {
              element.PropertyID = propertyId;
            });
          } else {
            this.additionalAddressList = [];
            this._sharedDataService.additionalAddressList = [];
          }
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.AdditionalAddressFetchFailed);
      });
    }
  }

  closeAdditionalAddressModal() {
    setTimeout(() => {
      this.getAdditionalAddress(this.property.PropertyID, true);
    }, 400);
    this.showAdditionalAddress = false;

  }


  deleteAdditionalAddressDetails(address) {
    const response_address = this.addressService.deleteAdditionalAddress(address.AddressId);
    response_address.subscribe(result => {
      if (!result || result.status === HttpStatus.CREATED) {
        this._notificationService.ShowErrorMessage(result.message);
      } else if (result && result.status === HttpStatus.OK) {
        this.getAdditionalAddress(this.property.PropertyID, true);
        this._notificationService.ShowSuccessMessage(CommonStrings.SuccessMessages.AdditionalAddressDeleteMessage)
      }
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.AdditionalAddressDeleteFailed);
    });
  }

  deleteAdditionalAddressConfirmation(address) {
    if (address) {
      const configuration: confirmConfiguration = new confirmConfiguration();
      configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteAdditionalAddressConfirmationMessage;
      configuration.Title = CommonStrings.DialogConfigurations.Title.AdditionalAddress;
      configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
      configuration.OkButton.Callback = () => {
        this.deleteAdditionalAddressDetails(address);
      }
      configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
      configuration.CancelButton.Callback = () => {

      }
      this._notificationService.CustomDialog(configuration);
    }

  }

  updateAdditionalAddress(address) {
    this.editAdditionalAddress = address;
    this.showAdditionalAddress = true;
  }

  viewChangeLog(parentid) {
    this.showChangeLogDetails = true;
    this.parentId = parentid;
  }

}
