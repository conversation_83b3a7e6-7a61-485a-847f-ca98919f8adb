<div [formGroup]="useTypeForm">
  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Contributed GBA</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputContributedGBA_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="ContributedGBA_SF" class="form-control" name="ContributedGBA_SM" [(ngModel)]="property.ContributedGBA_SF" [ngClass]="{'error-field':(useTypeForm?.controls['ContributedGBA_SF']?.enabled && !useTypeForm?.controls['ContributedGBA_SF']?.valid)}"
          (paste)="validatePasteInput($event, true)" data-testId="property-details-contributed-gba"
          (keypress)="validateIntegerInput($event, true)">
      </ng-container>
      <ng-template #masterRollupInputContributedGBA_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.ContributedGBA_SF}}" [value]="rollupMasterFreeholdFieldsObject?.ContributedGBA_SF" class="form-control" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Contributed GBA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="ContributedGBASource" [virtualScroll]="true" [items]="getDropdownFromLookup('ContributedGBASizeSourceID')" placeholder="--Select--" [(ngModel)]="property.ContributedGBASizeSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceEnum" [ngClass]="{'error-field':(useTypeForm?.controls['ContributedGBASource']?.enabled && !useTypeForm?.controls['ContributedGBASource']?.valid)}"
      data-testId="property-details-contributed-source">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.ContributedGBASizeSourceName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="rollupMasterFreeholdFieldsObject?.ContributedGBASizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
    <div class="col-md-5 label" for="text-input">Industrial GLA</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGLA_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="GLA_SF" class="form-control" [(ngModel)]="property.GLA_SF" [ngClass]="{'error-field':(useTypeForm?.controls['GLA_SF']?.enabled && !useTypeForm?.controls['GLA_SF']?.valid)}"
          (paste)="validatePasteInput($event, true)" data-testId="property-details-industrial-gla"
          (keypress)="validateIntegerInput($event, true)"
        >
      </ng-container>
      <ng-template #masterRollupInputGLA_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLA_SF}}" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.GLA_SF" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
    <div class="col-md-5 label" for="text-input">Industrial GLA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="IndustrialGLASource" [virtualScroll]="true" bindLabel="SizeSourceName" bindValue="SizeSourceEnum" placeholder="--Select--" [(ngModel)]="property.GLASizeSourceID" [items]="getDropdownFromLookup('GLASizeSourceID')" [ngClass]="{'error-field':(useTypeForm?.controls['IndustrialGLASource']?.enabled && !useTypeForm?.controls['IndustrialGLASource']?.valid)}"
      data-testId="property-details-gla-source">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLASizeSourceName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="rollupMasterFreeholdFieldsObject?.GLASizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label">Office NLA</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputOfficeSF">
        <input type="text" integer numericOnly allowNegative="false" class="form-control" formControlName="OfficeSF" name="OfficeSF" id="OfficeSF" [(ngModel)]="property.OfficeSF" value="" [ngClass]="{'error-field':(useTypeForm?.controls['OfficeSF']?.enabled && !useTypeForm?.controls['OfficeSF']?.valid)}"
          (paste)="validatePasteInput($event, true)" data-testId="property-details-office-nla"
          (keypress)="validateIntegerInput($event, true)"
        >
        <div class="prop-validator error" *ngIf="property.OfficeSF && useTypeForm.get('OfficeSF')?.errors?.errMsg">
          {{ useTypeForm.get('OfficeSF')?.errors?.errMsg }}
        </div>
      </ng-container>
      <ng-template #masterRollupInputOfficeSF>
        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.OfficeSF}}" [value]="rollupMasterFreeholdFieldsObject?.OfficeSF" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Office NLA Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="OfficeNLASource" [items]="getDropdownFromLookup('NRASizeSourceId')" [virtualScroll]="true" placeholder="--Select--" [(ngModel)]="property.NRASizeSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceEnum" [ngClass]="{'error-field':(useTypeForm?.controls['OfficeNLASource']?.enabled && !useTypeForm?.controls['OfficeNLASource']?.valid)}"
      data-testId="property-details-nla-source">
      </ng-select>
      <input type="text" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" title="{{rollupMasterFreeholdFieldsObject?.NRASizeSourceName}}" [value]="rollupMasterFreeholdFieldsObject?.NRASizeSourceName" class="form-control" readonly>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Retail GLAR</div>
    <div class="col-md-7">
      <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGLAR_SF">
        <input type="text" integer numericOnly allowNegative="false" formControlName="GLAR_SF" class="form-control" [(ngModel)]="property.GLAR_SF" [ngClass]="{'error-field':!useTypeForm?.controls['GLAR_SF']?.valid}"
          (paste)="validatePasteInput($event, true)" data-testId="property-details-retail-glar"
          (keypress)="validateIntegerInput($event, true)"
        >
      </ng-container>
      <ng-template #masterRollupInputGLAR_SF>
        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLAR_SF}}" [value]="rollupMasterFreeholdFieldsObject?.GLAR_SF" class="form-control" readonly>
      </ng-template>
    </div>
  </div>

  <div class="row label-value-wrapper">
    <div class="col-md-5 label" for="text-input">Retail GLAR Source</div>
    <div class="col-md-7">
      <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="RetailGLARSource" [items]="getDropdownFromLookup('GLARSizeSourceID')" [virtualScroll]="true" placeholder="--Select--" [(ngModel)]="property.GLARSizeSourceID" bindLabel="SizeSourceName" bindValue="SizeSourceEnum" [ngClass]="{'error-field':(useTypeForm?.controls['RetailGLARSource']?.enabled && !useTypeForm?.controls['RetailGLARSource']?.valid)}"
      data-testId="property-details-glar-source">
      </ng-select>
      <input type="text" title="{{rollupMasterFreeholdFieldsObject?.GLARSizeSourceName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="rollupMasterFreeholdFieldsObject?.GLARSizeSourceName" class="form-control" readonly>
    </div>
  </div>
</div>
