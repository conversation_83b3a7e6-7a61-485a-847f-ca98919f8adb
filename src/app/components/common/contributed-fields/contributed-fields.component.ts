// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
// RxJS imports
import { debounceTime } from 'rxjs/operators';
// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { YesNoFields, YesOrNoList, BindNamesWithLookupName } from '../../../../app/common/constants';
import { IndustrialControls } from '../../../../app/enumerations/IndustrialControlKeys';
import { OfficeControls } from '../../../../app/enumerations/officeControlKeys';
import { UseTypes } from '../../../../app/enumerations/useTypes';
import { LoginService } from '../../../../app/services/login.service';
import { updateValidation, validateIntegerInput, validatePasteInput } from '../../../../app/utils';
import { DebounceTimeConfigFormControlDelayInMS } from '../../../constants';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-contributed-fields',
  templateUrl: './contributed-fields.component.html',
  styleUrls: ['./contributed-fields.component.css']
})
export class ContributedFieldsComponent implements OnInit {
  @Input() condo : PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() useTypeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  @Input() lookupDropdowns: any;
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() isContributedGBARequired = false;
  EnumCondoTypeNames = PropertyDetailsDTO?.CondoTypeIDEnum;
  propertyUseTypes = UseTypes;
  validateIntegerInput = validateIntegerInput;
    validatePasteInput = validatePasteInput;

  constructor(private _loginService: LoginService) { }

  ngOnInit(): void {
    if (this.isContributedGBARequired) {
      this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.setValidators([Validators.required]);
    }
    this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.ContributedGBASource, 'ContributedGBASizeSourceID', OfficeControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(OfficeControls?.ContributedGBA_SF)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.OfficeSF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.OfficeNLASource, 'NRASizeSourceId', OfficeControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(OfficeControls?.OfficeSF)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.GLAR_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.RetailGLARSource, 'GLARSizeSourceID', OfficeControls, this.property, this.useTypeForm);
      }
    );

    this.useTypeForm?.get(OfficeControls?.GLAR_SF)?.updateValueAndValidity();

    this.useTypeForm?.get(IndustrialControls?.GLA_SF)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, IndustrialControls?.IndustrialGLASource, 'GLASizeSourceID', IndustrialControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(IndustrialControls?.GLA_SF)?.updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

}
