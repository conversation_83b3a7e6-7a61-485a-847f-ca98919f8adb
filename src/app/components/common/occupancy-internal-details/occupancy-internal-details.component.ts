// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
// Third-party imports
import { IAngularMyDpOptions } from 'angular-mydatepicker';
// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { CommonStrings } from '../../../constants';
import { YesOrNoList, YesNoFields, BindNamesWithLookupName } from '../../../common/constants';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { updateValidation, validateIntegerInput, validatePasteInput } from '../../../utils';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-occupancy-internal-details',
  templateUrl: './occupancy-internal-details.component.html',
  styleUrls: ['./occupancy-internal-details.component.css']
})
export class OccupancyInternalDetailsComponent implements OnInit {
  EnumCondoTypeNames = PropertyDetailsDTO?.CondoTypeIDEnum;
  @Input() useTypeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() condo: PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  buildSuitSpecList = [];
  hasConstructionDateError = false;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false
  };
  dateFormat: string;
  constructionDateErrorMsg = CommonStrings?.ErrorMessages?.ConstructionStartDateError;
   validateIntegerInput = validateIntegerInput;
    validatePasteInput = validatePasteInput;

  constructor(private _loginService:LoginService,  public yesOrNoService: YesOrNoService) { }

  ngOnInit(): void {
    this.dateFormat = this._loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.dateFormat = this.dateFormat;

    this.useTypeForm?.get(OfficeControls?.HVAC)?.valueChanges?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.HVACTypeID, 'HVACTypeID', OfficeControls,this.property,this.useTypeForm);
      }
    );

    this.useTypeForm?.get(OfficeControls?.ConstructionStartDate)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
    this.useTypeForm?.get(OfficeControls?.EstCompletionDate)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
    this.useTypeForm?.get(OfficeControls?.ActualCompletion)?.valueChanges?.subscribe(
      (value) => {
        this.checkForConstructionDateValidation();
      }
    );
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  checkboxChange(event, field) {
    const value = event.target.checked ? true : false;
    if (field == OfficeControls.IsOwnerOccupied) {
      this.property.IsOwnerOccupied = value;
    } else if (field === OfficeControls.IsADAAccessible) {
      this.property.IsADAAccessible = value;
    } else if (field === OfficeControls.IncludeinAnalytics) {
      this.property.IncludeInAnalytics = value;
    } else if (field === OfficeControls.HasSolar) {
      this.property.HasSolar = value;
    }
  }

  checkForConstructionDateValidation() {
    this.hasConstructionDateError = false;

    const constructionStartRaw = this.useTypeForm.get(OfficeControls?.ConstructionStartDate)?.value;
    const estCompletionRaw = this.useTypeForm.get(OfficeControls?.EstCompletionDate)?.value;
    const actualCompletionRaw = this.useTypeForm.get(OfficeControls?.ActualCompletion)?.value;

    // Extract jsDate and convert to Date object
    const constructionStart = constructionStartRaw?.singleDate?.jsDate ? new Date(constructionStartRaw.singleDate.jsDate) : null;
    const estCompletion = estCompletionRaw?.singleDate?.jsDate ? new Date(estCompletionRaw.singleDate.jsDate) : null;
    const actualCompletion = actualCompletionRaw?.singleDate?.jsDate ? new Date(actualCompletionRaw.singleDate.jsDate) : null;

    if (constructionStart) {
      const isEstCompletionValid = estCompletion ? estCompletion > constructionStart : true;
      const isActualCompletionValid = actualCompletion ? actualCompletion > constructionStart : true;
      this.hasConstructionDateError = !(isEstCompletionValid && isActualCompletionValid);
    }

    // Set the validity of the ConstructionStartDate control
    const constructionStartControl = this.useTypeForm?.get(OfficeControls?.ConstructionStartDate);
    if (this.hasConstructionDateError) {
      constructionStartControl?.setErrors({ invalidDateRange: true }); // Add an error
    } else {
      constructionStartControl?.setErrors(null); // Clear errors
    }
  }

}
