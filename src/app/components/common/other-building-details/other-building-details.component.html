<div [formGroup]="useTypeForm" class="details-wrapper">
  <div>
    <div class="title">Other Building Details</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinklers</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasSprinkler" [items]="getDropdownFromLookup('HasSprinkler')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasSprinkler"
              labelForId="HasSprinkler">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasSprinkler)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinkler Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="SprinklerTypeID" [items]="getDropdownFromLookup('SprinklerTypeID')"
              [virtualScroll]="true" bindLabel="SprinklerTypeName" bindValue="SprinklerTypeEnum"
              placeholder="--Select--" [(ngModel)]="property.SprinklerTypeID"
              labelForId="SprinklerType">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" readonly title="{{rollupMasterFreeholdFieldsObject?.SprinklerTypeName}}" [value]="rollupMasterFreeholdFieldsObject?.SprinklerTypeName" type="text" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
          <div class="col-md-5 label">Office HVAC</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="OfficeHVAC" [items]="getDropdownFromLookup('OfficeHVAC')"
              [virtualScroll]="true" bindLabel="OfficeHVACName" bindValue="OfficeHvacEnum"
              placeholder="--Select--" [(ngModel)]="property.OfficeHVAC"
              labelForId="OfficeHVAC">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" type="text" title="{{rollupMasterFreeholdFieldsObject?.OfficeHVAC}}" [value]="rollupMasterFreeholdFieldsObject?.OfficeHVAC" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Lifts</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="Lifts" [items]="getDropdownFromLookup('Lifts')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.Lifts"
              labelForId="Lifts">
            </ng-select>
            <input type="text" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.Lifts)" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Lifts Count</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputLiftsCount">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="LiftsCount" [(ngModel)]="property.LiftsCount" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputLiftsCount>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.LiftsCount" title="{{rollupMasterFreeholdFieldsObject?.LiftsCount}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
          <div class="col-md-5 label">Features</div>
          <div class="col-md-7">
            <ng-select formControlName="Features" [items]="getDropdownFromLookup('Features')" [multiple]="true"
              [virtualScroll]="true" bindLabel="FeatureName" bindValue="FeaturesEnum"
              placeholder="--Select--" [(ngModel)]="features"
              labelForId="Features" (change)="onChangeFeatures()">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Power, Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="PowerType" [items]="getDropdownFromLookup('PowerType')"
              [virtualScroll]="true" bindLabel="PowerTypeName" bindValue="PowerTypeID"
              placeholder="--Select--" [(ngModel)]="property.PowerType"
              labelForId="PowerType">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" title="{{rollupMasterFreeholdFieldsObject?.PowerTypeName}}" type="text" [value]="rollupMasterFreeholdFieldsObject?.PowerTypeName" class="form-control" readonly>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Power Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="PowerComments" [(ngModel)]="property.PowerComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="BuildingComments" [(ngModel)]="property.BuildingComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Website</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputBuildingWebsite">
              <input type="text" formControlName="BuildingWebsite" [(ngModel)]="property.BuildingWebsite" class="form-control"
                [ngClass]="{'error-field': !isBuildingWebsiteValueValid}">
            </ng-container>
            <ng-template #masterRollupInputBuildingWebsite>
              <input type="text" title="{{rollupMasterFreeholdFieldsObject?.BuildingWebsite}}" [value]="rollupMasterFreeholdFieldsObject?.BuildingWebsite" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
          <div class="col-md-5 label">Phase</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputPhase">
              <input type="text" formControlName="Phase" [(ngModel)]="property.Phase" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputPhase>
              <input type="text" title="{{rollupMasterFreeholdFieldsObject?.Phase}}" [value]="rollupMasterFreeholdFieldsObject?.Phase" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
          <div class="col-md-5 label">Volts</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputVolts">
              <input type="text" formControlName="Volts" [(ngModel)]="property.Volts" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputVolts>
              <input type="text" title="{{rollupMasterFreeholdFieldsObject?.Volts}}" [value]="rollupMasterFreeholdFieldsObject?.Volts" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
          <div class="col-md-5 label">Amps</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputAmps">
              <input type="text" formControlName="Amps" [(ngModel)]="property.Amps" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputAmps>
              <input type="text" title="{{rollupMasterFreeholdFieldsObject?.Amps}}" [value]="rollupMasterFreeholdFieldsObject?.Amps" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
