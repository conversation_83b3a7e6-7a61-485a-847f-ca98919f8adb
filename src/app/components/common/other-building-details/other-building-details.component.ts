// Angular core imports
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';

// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { IndustrialYesNoFields, YesOrNoList, BindNamesWithLookupName } from '../../../common/constants';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { ResearchType } from '../../../enumerations/researchType';
import { UseTypes } from '../../../enumerations/useTypes';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { isValidWebsite } from '../../../utils';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-other-building-details',
  templateUrl: './other-building-details.component.html',
  styleUrls: ['./other-building-details.component.css']
})
export class OtherBuildingDetailsComponent implements OnInit {

  @Input() useTypeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  features: any = [];
  propertyUseTypes = UseTypes;
  EnumCondoTypeNames = PropertyDetailsDTO?.CondoTypeIDEnum;
  isBuildingWebsiteValueValid = true;
  
  constructor(private _loginService:LoginService,  public yesOrNoService: YesOrNoService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.property || changes.condo) {
      this.toggleFeatureBasedOnCondo();
    }
    if (changes.property) {
      this.updateFeatures(changes.property?.currentValue);
    }
  }

  updateFeatures(property) {
    this.features = property?.FeatureIDs?.split(',');
    this.features = this.features.map((item) => item.trim());
  }

  toggleFeatureBasedOnCondo(){
    if(this.condo === this.EnumCondoTypeNames.MasterFreehold) {
      this.useTypeForm.controls[IndustrialControls.Features]?.disable();
    } else {
      this.useTypeForm.controls[IndustrialControls.Features]?.enable();
    }
  }


  ngOnInit(): void {
    this.updateFeatures(this.property)
    this.useTypeForm.get(IndustrialControls.HasSprinkler).valueChanges.subscribe(
      (HasSprinkler) => {
        this.updateHasSprinklerValidations(HasSprinkler);
      }
    );

    this.useTypeForm.get(IndustrialControls.Lifts).valueChanges.subscribe(
      (Lifts) => {
        this.updateLiftsValidations(Lifts);
      }
    );

    this.useTypeForm?.get(IndustrialControls.BuildingWebsite)?.valueChanges?.subscribe(
      (value) => {
         this.isBuildingWebsiteValueValid = isValidWebsite(value);
      }
    );
  }

  updateHasSprinklerValidations(HasSprinkler) {
    if (!HasSprinkler) {
      this.useTypeForm.controls[IndustrialControls.SprinklerTypeID].disable();
      this.useTypeForm.get(IndustrialControls.SprinklerTypeID).clearValidators();
      this.property.SprinklerTypeID = null;
    } else {
      this.useTypeForm.controls[IndustrialControls.SprinklerTypeID].enable();
      if (this.propertyStatus && this.propertyStatus !== ResearchType.Hidden) {
        this.useTypeForm.get(IndustrialControls.SprinklerTypeID).setValidators([Validators.required]);
      }
    }
    this.useTypeForm.get(IndustrialControls.SprinklerTypeID).updateValueAndValidity();
  }

  updateLiftsValidations(Lifts) {
    if (!Lifts) {
      this.useTypeForm.controls[IndustrialControls.LiftsCount].disable();
      this.useTypeForm.get(IndustrialControls.LiftsCount).clearValidators();
      this.property.LiftsCount = null;
    } else {
      this.useTypeForm.controls[IndustrialControls.LiftsCount].enable();
    }
    this.useTypeForm.get(IndustrialControls.LiftsCount).updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (IndustrialYesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }
  
  onChangeFeatures() {
    this.property.FeatureIDs = this.features.join(',');
  }
}
