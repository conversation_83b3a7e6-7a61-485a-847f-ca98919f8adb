// Angular Core Imports
import { Component, Input, OnInit, OnDestroy } from '@angular/core';

// Third-Party Libraries
import { Subscription } from 'rxjs';

// Application Services
import { CommunicationModel, CommunicationService } from '../../../services/communication.service';
import { SharedDataService } from '../../../services/shareddata.service';
import { LoginService } from '../../../services/login.service';
import { ParcelService } from '../../../../app/services/parcel.service';
import { NotificationService } from '../../../modules/notification/service/notification.service';
// Models and DTOs
import { mapEditPropertyDTO } from '../../../DTO/mapEditPropertyDTO';
import { ApiResponseListParcelPropertyDTO, AuditLogEntityTypes, ParcelPropertyDTO, PropertyDetailsDTO } from '../../../../app/api-client';
import { CommonStrings } from '../../../constants';

@Component({
  selector: 'app-parcel-list',
  templateUrl: './parcel-list.component.html',
  styleUrls: ['./parcel-list.component.css']
})
export class ParcelListComponent implements OnInit, OnDestroy {
  @Input() isAnExistingProperty: boolean;
  @Input() property: PropertyDetailsDTO;
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() initialDetails: mapEditPropertyDTO;

  countryId = this.loginService.UserInfo.CountryID;
  entityId = this.loginService.UserInfo.EntityID;

  parcelTabClick = false;
  showParcelDetails = false;
  propertyParcelList: ParcelPropertyDTO[] = [];
  selectedParcel: ParcelPropertyDTO;
  shouldUpdateLotSize = false;
  showParcelChangeLogDetails = false;
  changelogType = AuditLogEntityTypes.Parcel;
  parentId = null;

  private fetchParcelsSubscription: Subscription;

  constructor(
    private sharedDataService: SharedDataService,
    private communicationService: CommunicationService,
    private loginService: LoginService,
    private parcelService: ParcelService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.fetchParcelsSubscription = this.communicationService
      .subscribe('fetchParcels')
      .subscribe(({ data }) => {
        if (data) {
          this.shouldUpdateLotSize = data.shouldUpdate;
          this.fetchParcels();
        }
      });
  }

  ngOnDestroy(): void {
    this.fetchParcelsSubscription?.unsubscribe();
  }

  showPropertyParcel(): void {
    this.parcelTabClick = true;
    this.getParcelDetails();
  }

  getParcelDetails(forceFetch = false): void {
    const cachedParcels = this.sharedDataService.selectedPropertyParcel;
    const isMatchingProperty = cachedParcels?.length > 0 && cachedParcels[0].PropertyID === this.property.PropertyID;

    if (!forceFetch && isMatchingProperty) {
      this.propertyParcelList = cachedParcels;
      this.broadcastParcelSizeAndCount();
    } else {
      this.fetchParcels();
    }
  }

  fetchParcels(): void {
    this.parcelService.fetchParcels(this.property.PropertyID).subscribe((result: ApiResponseListParcelPropertyDTO) => {
      if (result?.error) {
         this.notificationService.ShowErrorMessage(result.message);
        return;
      }
      const parcels = result?.responseData;
      if (parcels?.length) {
        this.propertyParcelList = parcels.map(parcel => ({
          ...parcel
        }));
        this.sharedDataService.selectedPropertyParcel = this.propertyParcelList;
        const totalSize = this.getTotalParcelSize();
        if (this.shouldUpdateLotSize) {
          this.propertyCopy.LotSizeSF = this.property.LotSizeSF;
          this.property.LotSizeSF = totalSize;
          this.broadcastPropertySave(totalSize);
        }
        this.broadcastParcelSizeAndCount();
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchParcels);
    });
  }

  getTotalParcelSize(): number {
    return this.propertyParcelList.reduce((acc, parcel) => acc + (parseFloat(parcel.ParcelSizeSM as any) || 0), 0);
  }

  broadcastParcelSizeAndCount(): void {
    const size = this.getTotalParcelSize();
    const count = this.propertyParcelList.length;

    const message = new CommunicationModel();
    message.Key = 'updateParcelSizeAndCount';
    message.data = { size, count };

    this.communicationService.broadcast(message);
  }

  broadcastPropertySave(size: number): void {
    const message = new CommunicationModel();
    message.Key = 'propertySaveOnParcelChange';
    message.data = { size };

    this.communicationService.broadcast(message);
  }

  addParcelDetails(): void {
    this.selectedParcel = {} as ParcelPropertyDTO;
    this.showParcelDetails = true;
  }

  updateParcelDetails(parcel: ParcelPropertyDTO): void {
    this.selectedParcel = {
      ...parcel
    };
    this.showParcelDetails = true;
  }

  closeParcelModal(): void {
    this.showParcelDetails = false;
    this.shouldUpdateLotSize = true;
    this.getParcelDetails(true);
  }

  selectParcels(): void {
    const parcelNos = this.propertyParcelList.map(parcel => parcel.ParcelNo);

    const message = new CommunicationModel();
    message.Key = 'selectParcels';
    message.data = { ...this.initialDetails, parcelNos };

    this.communicationService.broadcast(message);
  }

  viewChangeLog(parentid) {
    this.showParcelChangeLogDetails = true;
    this.parentId = parentid;
  }
}
