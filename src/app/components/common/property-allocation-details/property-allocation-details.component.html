<div class="col-lg-12 page_sub_head1">
  <b>Property Allocation</b>
</div>
<br>
<div class="form-group" style="overflow-x:auto;">
  <table class="table table-bordered table-striped table-sm">
    <thead>
      <tr>
        <th style="min-width: 120px">Property Use</th>
        <th style="min-width: 80px">Floor Min</th>
        <th style="min-width: 80px">Floor Max</th>
        <th style="min-width: 90px">Floor Count</th>
        <th style="min-width: 120px">
          <span *ngIf="UnitId == metricUnit">Floor Size {{UnitDisplayTextSize}}</span>
          <span *ngIf="UnitId != metricUnit">Floor Size {{UnitDisplayTextSize}}</span>
        </th>
        <th style="min-width: 100px">% Allocation</th>
        <th style="min-width: 120px">Notes</th>
        <th style="min-width: 120px">Modified By</th>
        <th style="min-width: 110px">Modified Date</th>
      </tr>
    </thead>
    <tbody *ngFor="let propertyAllocation of propertyAllocationList">
      <tr *ngIf="!!propertyAllocation.IsActive">
        <td>{{propertyAllocation.UseTypeName}}</td>
        <td>
          <span *ngIf="propertyAllocation.MinFloorNumber == 1">G</span>
          <span
            *ngIf="propertyAllocation.MinFloorNumber && propertyAllocation.MinFloorNumber != 1">{{propertyAllocation.MinFloorNumber
            - 1}}</span>
          <span *ngIf="!propertyAllocation.MinFloorNumber">{{propertyAllocation.MinFloorNumber}}</span>
        </td>
        <td>
          <span *ngIf="propertyAllocation.MaxFloorNumber == 1">G</span>
          <span
            *ngIf="propertyAllocation.MaxFloorNumber && propertyAllocation.MaxFloorNumber != 1">{{propertyAllocation.MaxFloorNumber
            - 1}}</span>
          <span *ngIf="!propertyAllocation.MaxFloorNumber">{{propertyAllocation.MaxFloorNumber}}</span>
        </td>
        <td>
          <ng-container>
            {{propertyAllocation.Floors | number: '.2-2'}}
          </ng-container>
        </td>
        <td>{{propertyAllocation.FloorSizeSM | number: '.2-2'}}</td>
        <td>
          <ng-container>
            {{propertyAllocation.allocationPercentage | number: '.2-2'}}%
          </ng-container>
        </td>
        <td [title]="(propertyAllocation.Notes)?(propertyAllocation.Notes):''" class="item-note">
          <div class="ellipsis">{{propertyAllocation.Notes}}</div>
        </td>
        <td>{{propertyAllocation.ModifiedByName}}</td>
        <td>{{propertyAllocation.ModifiedDate | date:dateFormat}}</td>
      </tr>
    </tbody>
  </table>
</div>
