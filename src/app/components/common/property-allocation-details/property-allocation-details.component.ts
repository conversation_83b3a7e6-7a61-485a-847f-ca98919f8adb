import { Component, Input, OnInit } from '@angular/core';
import { LoginUserInfoDTO } from '../../../api-client';
@Component({
  selector: 'app-property-allocation-details',
  templateUrl: './property-allocation-details.component.html',
  styleUrls: ['./property-allocation-details.component.css']
})
export class PropertyAllocationDetailsComponent implements OnInit {

  @Input() UnitId: LoginUserInfoDTO.UnitIdEnum;
  @Input() metricUnit: LoginUserInfoDTO.UnitIdEnum;
  @Input() UnitDisplayTextSize: any;
  propertyAllocationList: any[] = [];
  @Input() propertyTypes: any;
  @Input() dateFormat: string;
  constructor() {
  }

  ngOnInit(): void {}

  updateAllocationsData(multifloors, propertyId) {
    const totalBuildingSqm = multifloors.reduce((acc, floor) => Number(floor.floorSize) * Number(floor.floorCount) + acc, 0);
    let propertyAllocations = [];
    const footprintsAllocated = [];
    
    multifloors.forEach(floor => {
      const isAllocationPresent = propertyAllocations.some(alloc => floor.BuildingFootPrintID ? alloc.SectionID == floor.BuildingFootPrintID : alloc.SectionID == floor.localBuildingFootPrintID);
      //Create new allocation if not present
      if(!isAllocationPresent) {
        const propertyUse = this.propertyTypes.find((useType) => useType.UseTypeID === floor.specificUse)?.UseTypeName;
          const allocation = {
            PropertyID: propertyId,
            UseTypeID: floor.specificUse,
            SpecificUsesID: null,
            UseTypeName: propertyUse,
            Floors: floor.floorCount?.toString(),
            FloorSizeSM: Number(floor.floorSize),
            Notes: floor.description,
            IsDefault: 1,
            IsActive: 1,
            MinFloorNumber: floor.minFloor,
            MaxFloorNumber: floor.maxFloor,
            LocalAllocationId: floor.BuildingFootPrintID ? null : floor.localBuildingFootPrintID,
            SectionID: floor.BuildingFootPrintID ? floor.BuildingFootPrintID : floor.localBuildingFootPrintID,
            allocationPercentage: (Number(floor.floorSize) * Number(floor.floorCount)) / totalBuildingSqm * 100,
            ModifiedByName: floor.ModifiedByName,
            ModifiedDate: floor.ModifiedDate
          };
          footprintsAllocated.push(floor.BuildingFootPrintID);
          propertyAllocations.push(allocation);
      } else { //Edit the existing allocation
        propertyAllocations.map(alloc => {
          if (floor.BuildingFootPrintID ? alloc.SectionID == floor.BuildingFootPrintID : alloc.SectionID == floor.localBuildingFootPrintID) {
            const propertyUse = this.propertyTypes.find((useType) => useType.UseTypeID === floor.specificUse)?.UseTypeName;
            alloc.UseTypeID = floor.specificUse;
            alloc.UseTypeName = propertyUse;
            alloc.Floors = floor.floorCount?.toString();
            alloc.FloorSizeSM = Number(floor.floorSize),
            alloc.Notes = floor.description,
            alloc.IsDefault = 1;
            alloc.IsActive = 1;
            alloc.MinFloorNumber = floor.minFloor;
            alloc.MaxFloorNumber = floor.maxFloor;
            alloc.allocationPercentage = (Number(floor.floorSize) * Number(floor.floorCount)) / totalBuildingSqm * 100;
          }
          return alloc;
        });
      }
    });
    //Sorting Allocations
    propertyAllocations = propertyAllocations.sort((a, b) => (a.IsDefault < b.IsDefault) ? 1 : -1);
    //Update the allocations data in edit property component
    this.propertyAllocationList = JSON.parse(JSON.stringify(propertyAllocations));
  }
}
