<div [formGroup]="useTypeForm">
    <div class="row">
        <div class="col-md-6">
            <div class="row label-value-wrapper" *ngIf="property.UseTypeID != propertyUseTypes.Retail">
                <div *ngIf="property.UseTypeID == propertyUseTypes.Industrial" class="col-md-5 label" for="text-input">Building Grade</div>
                <div *ngIf="property.UseTypeID != propertyUseTypes.Industrial" class="col-md-5 label" for="text-input">Building Class</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="BuildingClass" [items]="getDropdownFromLookup('BuildingClass')" [virtualScroll]="true"
                        bindLabel="ClassTypeName" bindValue="ClassTypeEnum" placeholder="--Select--" data-testId="property-details-building-grade"
                        [(ngModel)]="property.ClassTypeID"></ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.ClassTypeName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="rollupMasterFreeholdFieldsObject?.ClassTypeName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Specific Use</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="SpecificUse" [items]="getDropdownFromLookup('SpecificUseID')" [virtualScroll]="true"
                        bindLabel="SpecificUsesName" bindValue="SpecificUsesID" placeholder="--Select--" data-testId="property-details-specific-use"
                        [(ngModel)]="property.SpecificUseID"></ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.SpecificUseName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" readonly [value]="rollupMasterFreeholdFieldsObject?.SpecificUseName" type="text" class="form-control">
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Building Size
                    (GBA)
                </div>
                <div class="col-md-7">
                <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputBuildingSF">
                    <input type="text" maxlength="11" numericOnly [allowNegative]="false" [allowDecimal]="true" (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)" data-testId="property-details-building-size"
                        class="form-control maxnumbervalidation" formControlName="BuildingSF" [(ngModel)]="property.BuildingSF" (blur)="validateSizeFieldsAgainstBuildingSize()"
                    >
                </ng-container>
                <ng-template #masterRollupInputBuildingSF>
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.BuildingSF}}"
                        class="form-control maxnumbervalidation"  [value]="rollupMasterFreeholdFieldsObject?.BuildingSF"
                        [readonly]="condo === EnumCondoTypeNames.MasterStrataRecord ||condo === EnumCondoTypeNames.MasterFreehold">
                </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="condo != EnumCondoTypeNames.MasterStrataRecord && condo !==EnumCondoTypeNames.MasterFreehold">
                <div class="col-md-5 label">Minimum Floor Size</div>
                <div class="col-md-7">
                  <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="SmallestFloor" [(ngModel)]="property.SmallestFloor" class="form-control"
                  [ngClass]="{'error-field': !useTypeForm.controls['SmallestFloor']?.valid}" (paste)="validatePasteInput($event, true)" data-testId="property-details-min-floor"
                  (keypress)="validateIntegerInput($event, true)">
                  <div class="prop-validator error" *ngIf="useTypeForm.controls['SmallestFloor']?.errors?.errMsg">{{useTypeForm.controls['SmallestFloor']?.errors?.errMsg}}</div>
                    <div *ngIf="useTypeForm.controls['SmallestFloor']?.invalid && (useTypeForm.controls['SmallestFloor'].dirty || useTypeForm.controls['SmallestFloor'].touched)">
                      <div *ngIf="useTypeForm.controls['SmallestFloor'].errors?.numeric" class="error-message">Please enter a valid number.</div>
                    </div>
                </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo === EnumCondoTypeNames.MasterFreehold">
                <div class="col-md-5 label">Minimum Floor Size</div>
                <div class="col-md-7">
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.SmallestFloor}}" [value]="rollupMasterFreeholdFieldsObject?.SmallestFloor" class="form-control" readonly>
                </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo != EnumCondoTypeNames.MasterStrataRecord && condo !==EnumCondoTypeNames.MasterFreehold">
            <div class="col-md-5 label">Maximum Floor Size</div>
            <div class="col-md-7">
                <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="LargestFloor" [(ngModel)]="property.LargestFloor" class="form-control"
                [ngClass]="{'error-field': !useTypeForm.controls['LargestFloor']?.valid}" (paste)="validatePasteInput($event, true)" data-testId="property-details-max-floor"
                (keypress)="validateIntegerInput($event, true)">
                <div class="prop-validator error" *ngIf="useTypeForm.controls['LargestFloor']?.errors?.errMsg">{{useTypeForm.controls['LargestFloor']?.errors?.errMsg}}</div>
                <div *ngIf="useTypeForm.controls['LargestFloor']?.invalid && (useTypeForm.controls['LargestFloor'].dirty || useTypeForm.controls['LargestFloor'].touched)">
                    <div *ngIf="useTypeForm.controls['LargestFloor'].errors?.numeric" class="error-message">Please enter a valid number.</div>
                </div>
            </div>
            </div>
            <div class="row label-value-wrapper" *ngIf="condo === EnumCondoTypeNames.MasterFreehold">
                <div class="col-md-5 label">Maximum Floor Size</div>
                <div class="col-md-7">
                    <input type="text" title="{{rollupMasterFreeholdFieldsObject?.LargestFloor}}" [value]="rollupMasterFreeholdFieldsObject?.LargestFloor" class="form-control" readonly>
                </div>
            </div>
            <app-contributed-fields
                [condo]="condo"
                [useTypeForm]="useTypeForm"
                [property]="property"
                [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
                [lookupDropdowns]="lookupDropdowns"
                [propertyCopy]="propertyCopy"
                [dataArray]="dataArray"
            ></app-contributed-fields>
        </div>

        <div class="col-md-6">
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Year Built</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputYearBuilt">
                        <input type="text" integer numericOnly [allowNegative]="false" [allowDecimal]="false" maxLength="4" formControlName="YearBuilt"
                            class="form-control" [(ngModel)]="property.YearBuilt" (paste)="validatePasteInput($event)" data-testId="property-details-year-built"
                            (keypress)="validateIntegerInput($event)">
                            <div class="prop-validator error" *ngIf="useTypeForm.get('YearBuilt')?.errors?.errMsg">
                                {{ useTypeForm.get('YearBuilt')?.errors?.errMsg }}
                              </div>
                    </ng-container>
                    <ng-template #masterRollupInputYearBuilt>
                        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.YearBuilt}}" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.YearBuilt" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Year Renovated</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputYearRenovated">
                        <input type="text" integer numericOnly [allowNegative]="false" [allowDecimal]="false" maxLength="4" formControlName="YearRenovated"
                            class="form-control" [(ngModel)]="property.YearRenovated" (paste)="validatePasteInput($event)" data-testId="property-details-year-renovated"
                            (keypress)="validateIntegerInput($event)">
                    </ng-container>
                    <ng-template #masterRollupInputYearRenovated>
                        <input type="text" title="{{rollupMasterFreeholdFieldsObject?.YearRenovated}}" class="form-control"  [value]="rollupMasterFreeholdFieldsObject?.YearRenovated" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">NABERS Energy</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="EnergyStarRatingID" [items]="getDropdownFromLookup('EnergyStarRatingID')" [virtualScroll]="true"
                        bindLabel="EnergyStarRatingName" bindValue="EnergyStarRatingEnum" placeholder="--Select--" data-testId="property-details-nabers-energy"
                        [(ngModel)]="property.EnergyStarRatingID" name="EnergyStar" id="EnergyStar"></ng-select>
                    <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" title="{{rollupMasterFreeholdFieldsObject?.EnergyStarRatingName}}" [value]="rollupMasterFreeholdFieldsObject?.EnergyStarRatingName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">NABERS Water</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="WaterStarRatingID" [items]="getDropdownFromLookup('WaterStarRatingID')" [virtualScroll]="true"
                        bindLabel="WaterStarRatingName" bindValue="WaterStarRatingEnum" placeholder="--Select--" data-testId="property-details-water-rating"
                        [(ngModel)]="property.WaterStarRatingID" name="WaterStar" labelForId="WaterStar">
                    </ng-select>
                    <input title="{{rollupMasterFreeholdFieldsObject?.WaterStarRatingName}}" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="rollupMasterFreeholdFieldsObject?.WaterStarRatingName" type="text" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Green Star Rating</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="GreenStarRatingID" [items]="getDropdownFromLookup('GreenStarRatingID')" [virtualScroll]="true"
                        bindLabel="GreenStarRatingName" bindValue="GreenStarRatingEnum" placeholder="--Select--" data-testId="property-details-green-star"
                        [(ngModel)]="property.GreenStarRatingID" name="GreenStar" labelForId="GreenStar">
                    </ng-select>
                    <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" title="{{rollupMasterFreeholdFieldsObject?.GreenStarRatingName}}" [value]="rollupMasterFreeholdFieldsObject?.GreenStarRatingName" class="form-control" type="text" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper">
                <div class="col-md-5 label GresbScore" for="text-input">GRESB Score </div>
                <div class="col-md-7 row">
                    <div class="col-md-6 GresbScoreMin">
                        <div class="form-control-label" for="text-input">Min</div>
                        <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGRESBScoreMin">
                            <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" maxlength="11"
                                formControlName="GRESBScoreMin" name="GRESBScoreMin" class="form-control maxnumbervalidation"
                                [(ngModel)]="property.GRESBScoreMin" data-testId="property-details-gresb-minscore"
                                [ngClass]="{'error-field':( !useTypeForm?.controls['GRESBScoreMin']?.valid)}" (paste)="validatePasteInput($event, true)"
                                (keypress)="validateIntegerInput($event, true)">
                                 <div class="prop-validator error GresbScore-msg" *ngIf="useTypeForm.get('GRESBScoreMin')?.errors?.errMsg">
                                    {{ useTypeForm.get('GRESBScoreMin')?.errors?.errMsg }}</div>
                        </ng-container>
                        <ng-template #masterRollupInputGRESBScoreMin>
                            <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.GRESBScoreMin}}" [value]="rollupMasterFreeholdFieldsObject?.GRESBScoreMin" readonly>
                        </ng-template>
                    </div>
                    <div class="col-md-6 GresbScoreMax">
                        <div class="form-control-label " for="text-input">Max</div>
                        <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGRESBScoreMax">
                            <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" maxlength="11"
                                formControlName="GRESBScoreMax" name="GRESBScoreMax" class="form-control maxnumbervalidation"
                                [(ngModel)]="property.GRESBScoreMax" data-testId="property-details-gresb-maxscore"
                                [ngClass]="{'error-field':( !useTypeForm?.controls['GRESBScoreMax']?.valid)}"
                                (paste)="validatePasteInput($event, true)"
                                (keypress)="validateIntegerInput($event, true)">
                        </ng-container>
                        <ng-template #masterRollupInputGRESBScoreMax>
                            <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.GRESBScoreMax}}" [value]="rollupMasterFreeholdFieldsObject?.GRESBScoreMax" readonly>
                        </ng-template>
                    </div>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Office Mezzanine</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="OfficeMezzanine" [items]="getDropdownFromLookup('Mezzanine')"
                        [virtualScroll]="true" bindLabel="label" bindValue="value" placeholder="--Select--" data-testId="property-details-office-mezzanine"
                        [(ngModel)]="property.Mezzanine" name="OfficeMezzanine" id="OfficeMezzanine">
                    </ng-select>
                    <input type="text" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.Mezzanine)" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Office Mezzanine Size</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputMezzanine_Size_SF">
                        <input type="text" maxlength="11" integer numericOnly [allowNegative]="false" [allowDecimal]="true"
                        class="form-control maxnumbervalidation" formControlName="Mezzanine_Size_SF" data-testId="property-details-mezzanine size"
                        [(ngModel)]="property.Mezzanine_Size_SF"
                        (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)"
                        [ngClass]="{'error-field':(useTypeForm?.controls['Mezzanine_Size_SF']?.enabled && !useTypeForm?.controls['Mezzanine_Size_SF']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputMezzanine_Size_SF>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.Mezzanine_Size_SF}}" [value]="rollupMasterFreeholdFieldsObject?.Mezzanine_Size_SF" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings</div>
                <div class="col-md-7">
                    <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="Awnings" [items]="getDropdownFromLookup('Awnings')" [virtualScroll]="true"
                    bindLabel="label" bindValue="value" placeholder="--Select--" [(ngModel)]="property.Awnings" data-testId="property-details-awnings"
                        name="Awnings" id="Awnings">
                    </ng-select>
                    <input type="text" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.Awnings)" class="form-control" readonly>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings Count</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputAwningsCount">
                        <input type="text" maxlength="11" numericOnly [allowNegative]="false" [allowDecimal]="false"
                            (paste)="validatePasteInput($event)" data-testId="property-details-awnings-count"
                            (keypress)="validateIntegerInput($event)"
                            formControlName="AwningsCount" class="form-control maxnumbervalidation" [(ngModel)]="property.AwningsCount"
                            [ngClass]="{'error-field':(useTypeForm?.controls['AwningsCount']?.enabled && !useTypeForm?.controls['AwningsCount']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputAwningsCount>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.AwningsCount}}" [value]="rollupMasterFreeholdFieldsObject?.AwningsCount" readonly>
                    </ng-template>
                </div>
            </div>

            <div class="row label-value-wrapper" *ngIf="property.UseTypeID == propertyUseTypes.Industrial">
                <div class="col-md-5 label">Awnings Size</div>
                <div class="col-md-7">
                    <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputAwnings_Size_SF">
                        <input type="text" maxlength="11" numericOnly [allowNegative]="false" data-testId="property-details-awnings-size"
                        formControlName="Awnings_Size_SF" class="form-control maxnumbervalidation"
                        [(ngModel)]="property.Awnings_Size_SF"
                        (paste)="validatePasteInput($event, true)"
                        (keypress)="validateIntegerInput($event, true)"
                        [ngClass]="{'error-field':(useTypeForm?.controls['Awnings_Size_SF']?.enabled && !useTypeForm?.controls['Awnings_Size_SF']?.valid)}">
                    </ng-container>
                    <ng-template #masterRollupInputAwnings_Size_SF>
                        <input type="text" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.Awnings_Size_SF}}" [value]="rollupMasterFreeholdFieldsObject?.Awnings_Size_SF" readonly>
                    </ng-template>
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label">Contributed Source Comments</div>
                <div class="col-md-7">
                    <textarea rows="2" formControlName="ContributedSourceComments" [(ngModel)]="property.ContributedSourceComments" class="form-control"
                    data-testId="property-details-contributed-comments"></textarea>
                </div>
            </div>

        </div>

    </div>
</div>