// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
// RxJS imports
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { YesOrNoList, YesNoFields, BindNamesWithLookupName } from '../../../common/constants';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { UseTypes } from '../../../enumerations/useTypes';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { GRESBScoreMaxValidator, GRESBScoreMinValidator, maxFloorValidator, minFloorValidator,
  updateValidation, validateBuildingSize, validateIntegerInput, validatePasteInput, yearBuiltValidator, yearRenovatedValidator } from '../../../utils';
import { DebounceTimeConfigFormControlDelayInMS } from '../../../constants';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-property-use-details',
  templateUrl: './property-use-details.component.html',
  styleUrls: ['./property-use-details.component.css']
})
export class PropertyUseDetailsComponent implements OnInit {

  maskOptions_precision_two: any;
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum;
  yearBuildError: boolean = false;
  yearRenovatedError: boolean = false;
  specificUses: any;
  UnitDisplayTextSize: any;
  propertyUseTypes = UseTypes;
  public NABERSList: any;
  public NABERSWaterList: any;
  public GreenStarRatingList: any;
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() useTypeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() propertyTypeValues;
  @Input() lookupDropdowns: any;
  @Input() condo : PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  isContributedGBASourceValid: boolean;
  isOfficeNLASourceValid: boolean;
  isRetailGLARSourceValid: boolean;
  isIndustrialGLASourceValid: boolean;
  showMinFloorError: boolean = false;
  showMaxFloorError: boolean = false;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  constructor(private _loginService: LoginService, public yesOrNoService: YesOrNoService) {
    this.maskOptions_precision_two = { prefix: '' };
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
  }

  ngOnInit(): void {
    this.useTypeForm?.get(IndustrialControls?.OfficeMezzanine)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, IndustrialControls?.Mezzanine_Size_SF, 'Mezzanine_Size_SF', IndustrialControls, this.property, this.useTypeForm);
      }
    );
    this.useTypeForm?.get(IndustrialControls?.Awnings)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, IndustrialControls?.AwningsCount, 'AwningsCount', IndustrialControls, this.property, this.useTypeForm);
        updateValidation(value, IndustrialControls?.Awnings_Size_SF, 'Awnings_Size_SF', IndustrialControls, this.property, this.useTypeForm);
      }
    );


    //Validations for Year built and Year renovated
    this.useTypeForm?.get(OfficeControls?.YearBuilt)?.setValidators([
      yearBuiltValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.YearBuilt)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.YearRenovated)?.setValidators([
      yearRenovatedValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.YearRenovated)?.updateValueAndValidity();
    this.useTypeForm.get(OfficeControls?.YearBuilt)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {
      this.useTypeForm.get(OfficeControls?.YearRenovated)?.updateValueAndValidity({ emitEvent: false });
    });
    this.useTypeForm.get(OfficeControls?.YearRenovated)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.useTypeForm.get(OfficeControls?.YearBuilt)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });

    //Validations for GRESB Scire min and max
    this.useTypeForm?.get(OfficeControls?.GRESBScoreMax)?.setValidators([
      GRESBScoreMaxValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.GRESBScoreMax)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.GRESBScoreMin)?.setValidators([
      GRESBScoreMinValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.GRESBScoreMin)?.updateValueAndValidity();
    this.useTypeForm.get(OfficeControls?.GRESBScoreMax)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {
      this.useTypeForm.get(OfficeControls?.GRESBScoreMin)?.updateValueAndValidity({ emitEvent: false });
    });
    this.useTypeForm.get(OfficeControls?.GRESBScoreMin)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.useTypeForm.get(OfficeControls?.GRESBScoreMax)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });


    this.useTypeForm?.get(OfficeControls?.SmallestFloor)?.setValidators([
      minFloorValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.SmallestFloor)?.updateValueAndValidity();
    this.useTypeForm?.get(OfficeControls?.LargestFloor)?.setValidators([
      maxFloorValidator
    ]);
    this.useTypeForm?.get(OfficeControls?.LargestFloor)?.updateValueAndValidity();
    this.useTypeForm.get(OfficeControls?.SmallestFloor)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {
      this.useTypeForm.get(OfficeControls?.LargestFloor)?.updateValueAndValidity({ emitEvent: false });
    });
    this.useTypeForm.get(OfficeControls?.SmallestFloor)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.useTypeForm.get(OfficeControls?.LargestFloor)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });


  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field]
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  validateSizeFieldsAgainstBuildingSize() {
    validateBuildingSize(this.property.BuildingSF, this.property, this.useTypeForm);
  }

}



