import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { numericValidator } from '../../../utils';
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-industrial-property-details',
  templateUrl: './industrial-property-details.component.html',
  styleUrls: ['./industrial-property-details.component.css']
})
export class IndustrialPropertyDetailsComponent implements OnInit {
  @Input() industrialForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  
  constructor() { }

  ngOnInit(): void {
    this.addControls();
  }

  addControls() {
    Object.keys(IndustrialControls).forEach((controlName: IndustrialControls) => {
      if (IndustrialControls.DockHigh === controlName || IndustrialControls.Truckwell === controlName) {
        this.industrialForm.addControl(controlName, new FormControl('', [Validators.min(0), Validators.max(999)]))
      } else if (IndustrialControls.ClearHeightMax === controlName || IndustrialControls.ClearHeightMin === controlName) {
        this.industrialForm.addControl(controlName, new FormControl('', {
          validators: [Validators.min(3), Validators.max(999)], updateOn: 'blur'
        }))
      } else if ([IndustrialControls.SmallestFloor, IndustrialControls.LargestFloor].includes(controlName)) {
        this.industrialForm.addControl(controlName, new FormControl('', [numericValidator()]));
      } else {
        this.industrialForm.addControl(controlName, new FormControl(''));
      }
    });
  }

}
