<div [formGroup]="industrialForm" class="details-wrapper">
  <div>
    <div class="row">
      <div class="col-md-6">
        <div class="title">Loading & Height</div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">On Grade Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGradeLevelDriveIn">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" step="1" formControlName="GradeLevelDriveIn" [(ngModel)]="property.GradeLevelDriveIn" class="form-control"
              (paste)="validatePasteInput($event)"
              (keypress)="validateIntegerInput($event)">
            </ng-container>
            <ng-template #masterRollupInputGradeLevelDriveIn>
              <input type="number" step="1"  [value]="rollupMasterFreeholdFieldsObject?.GradeLevelDriveIn" title="{{rollupMasterFreeholdFieldsObject?.GradeLevelDriveIn}}" class="form-control"
                (paste)="validatePasteInput($event)" (keypress)="validateIntegerInput($event)" readOnly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Dock High Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputDockHigh">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="DockHigh" [(ngModel)]="property.DockHigh" class="form-control"
            (keypress)="validateIntegerInput($event)"
            (paste)="validatePasteInput($event)"
              [ngClass]="{'error-field':(!industrialForm.controls['DockHigh']?.valid)}">
            </ng-container>
            <ng-template #masterRollupInputDockHigh>
              <input type="number"  [value]="rollupMasterFreeholdFieldsObject?.DockHigh" title="{{rollupMasterFreeholdFieldsObject?.DockHigh}}"
                class="form-control" readonly>
            </ng-template>
            <div class="validation-error" *ngIf="(industrialForm.controls['DockHigh']?.hasError('max')||industrialForm.controls['DockHigh']?.hasError('min'))">
              Value must be between 0 and 999
            </div>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Recessed/Truckwell Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputTruckwell">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="Truckwell" [(ngModel)]="property.Truckwell" class="form-control"
              (keypress)="validateIntegerInput($event)"
              (paste)="validatePasteInput($event)"
              [ngClass]="{'error-field':(!industrialForm.controls['Truckwell']?.valid)}">
              <div class="validation-error" *ngIf="(industrialForm.controls['Truckwell'].hasError('max')||industrialForm.controls['Truckwell'].hasError('min'))">
                Value must be between 0 and 999</div>
            </ng-container>
            <ng-template #masterRollupInputTruckwell>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.Truckwell" title="{{rollupMasterFreeholdFieldsObject?.Truckwell}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Clear Height</div>
          <div class="col-md-7 row">
            <div class="col-md-6">
              <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputClearHeightMin">
                <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="ClearHeightMin" [(ngModel)]="property.ClearHeightMin" class="form-control"
                (paste)="validatePasteInput($event, true)"
                (keypress)="validateIntegerInput($event, true)"
                [ngClass]="{'error-field':(industrialForm.controls['ClearHeightMin']?.errors?.errMsg || !industrialForm.controls['ClearHeightMin']?.valid)}">
                <div class="prop-validator error error-msg" *ngIf="industrialForm?.get('ClearHeightMin')?.errors?.errMsg">
                  {{ industrialForm.get('ClearHeightMin')?.errors?.errMsg }}</div>
              </ng-container>
              <ng-template #masterRollupInputClearHeightMin >
                <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ClearHeightMin" title="{{rollupMasterFreeholdFieldsObject?.ClearHeightMin}}" class="form-control" readonly>
              </ng-template>
            </div>
            <div class="col-md-6">
              <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputClearHeightMax">
                <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="ClearHeightMax" [(ngModel)]="property.ClearHeightMax" class="form-control"
                (paste)="validatePasteInput($event, true)"
                (keypress)="validateIntegerInput($event, true)"
                [ngClass]="{'error-field':(industrialForm.controls['ClearHeightMax']?.errors?.errMsg || !industrialForm.controls['ClearHeightMax']?.valid)}">
                <div class="prop-validator error error-msg" *ngIf="industrialForm?.get('ClearHeightMax')?.errors?.errMsg">
                  {{ industrialForm.get('ClearHeightMax')?.errors?.errMsg }}</div>
              </ng-container>
              <ng-template #masterRollupInputClearHeightMax>
                <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ClearHeightMax" title="{{rollupMasterFreeholdFieldsObject?.ClearHeightMax}}" class="form-control" readonly>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="title">Yard/Lot/Parking</div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Yard</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasYard" [items]="getDropdownFromLookup('HasYard')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasYard"
              labelForId="HasYard">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasYard)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Yard, Fenced</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasYardFenced" [items]="getDropdownFromLookup('HasYardFenced')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasYardFenced"
              labelForId="HasYardFenced">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasYardFenced)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Hardstand</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="YardPaved" [items]="getDropdownFromLookup('YardPaved')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.YardPaved"
              labelForId="YardPaved">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.YardPaved)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Hardstand Area</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputHardstandArea">
              <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="HardstandArea"
                [(ngModel)]="property.HardstandArea" class="form-control" [style.width]="property.YardPaved ? '80%' : '100%'"
                (paste)="validatePasteInput($event, true)"
                (keypress)="validateIntegerInput($event, true)"
                [ngClass]="{'error-field':(industrialForm?.controls['HardstandArea']?.enabled && !industrialForm?.controls['HardstandArea']?.valid)}">
              <a *ngIf="property.YardPaved" href="javascript://" (click)="onHardstandAreaClicked()" class="mapBox"
                [tabindex]="-1"><img src="assets/images/DrawMap.png" [tabindex]="-1"></a>
            </ng-container>
            <ng-template #masterRollupInputHardstandArea>
              <input type="text"
                [value]="rollupMasterFreeholdFieldsObject?.HardstandArea" title="{{rollupMasterFreeholdFieldsObject?.HardstandArea}}" class="form-control" [style.width]="rollupMasterFreeholdFieldsObject?.YardPaved ? '80%' : '100%'" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Hardstand Area Source</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HardstandAreaSource" [items]="getDropdownFromLookup('HardstandAreaSourceID')"
              [virtualScroll]="true" bindLabel="SizeSourceName" bindValue="SizeSourceEnum"
              placeholder="--Select--" [(ngModel)]="property.HardstandAreaSourceID"
              labelForId="HardStandAreaSource"
              [ngClass]="{'error-field':(industrialForm?.controls['HardstandAreaSource']?.enabled && !industrialForm?.controls['HardstandAreaSource']?.valid)}">
            </ng-select>
            <input type="text" *ngIf="condo === EnumCondoTypeNames.MasterFreehold" title="{{rollupMasterFreeholdFieldsObject?.HardstandAreaSourceName}}" [value]="rollupMasterFreeholdFieldsObject?.HardstandAreaSourceName" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Parking Spaces</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputParkingSpaces">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="ParkingSpaces" [(ngModel)]="property.ParkingSpaces" class="form-control" (blur)="onParkingChange()">
            </ng-container>
            <ng-template #masterRollupInputParkingSpaces>
              <input type="number"  [value]="rollupMasterFreeholdFieldsObject?.ParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.ParkingSpaces}}"
                class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Parking Ratio</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputParkingRatio">
              <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="ParkingRatio" [(ngModel)]="property.ParkingRatio" class="form-control" readonly>
            </ng-container>
            <ng-template #masterRollupInputParkingRatio>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ParkingRatio" title="{{rollupMasterFreeholdFieldsObject?.ParkingRatio}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Rail Served</div>
          <div class="col-md-7">
            <ng-select  *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="RailServed" [items]="getDropdownFromLookup('RailServed')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.RailServed"
              labelForId="RailServed">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.RailServed)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Crane Served</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="CraneServed" [items]="getDropdownFromLookup('CraneServed')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.IsCraneServed"
              labelForId="CraneServed">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.IsCraneServed)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Port Access</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasPortAccess" [items]="getDropdownFromLookup('HasPortAccess')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasPortAccess"
              labelForId="HasPortAccess">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasPortAccess)" type="text" class="form-control"  readonly>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="true">
  <imperium-modal [width]="'large'" [height]="'large'" [(visible)]="showHardStandArea"
    [title]="'Hardstand Area Measurement'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-area-measurement-modal
        [latLng]="{lat: property.Location.Latitude, lng: property.Location.Longitude}"
        [areaPolygon]="hardstandAreaPolygon"
        (onAreaSave)="onHardstandAreaSave($event)"></app-area-measurement-modal>
    </ng-template>
  </imperium-modal>
</div>

