// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';

// RxJS imports
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { IndustrialYesNoFields, YesOrNoList, BindNamesWithLookupName } from '../../../common/constants';
import { IndustrialControls } from '../../../enumerations/IndustrialControlKeys';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { clearHeightMaxValidator, clearHeightMinValidator, validateIntegerInput, validatePasteInput } from '../../../utils';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-yard-lot-height-details',
  templateUrl: './yard-lot-height-details.component.html',
  styleUrls: ['./yard-lot-height-details.component.css']
})
export class YardLotHeightDetailsComponent implements OnInit {

  @Input() industrialForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() condo: PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  hardstandAreaPolygon: { area: number, polygon: any };
  showHardStandArea = false;
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum;
  
  constructor(private _loginService:LoginService, public yesOrNoService: YesOrNoService) { }

  ngOnInit(): void {

    this.industrialForm.get(IndustrialControls.YardPaved).valueChanges
      .pipe(debounceTime(100))
      .subscribe((YardPavedyn) => {
        this.updateYardPavedValidations(YardPavedyn);
      });

    this.industrialForm.get(IndustrialControls.HasYard).valueChanges
      .pipe(debounceTime(100))
      .subscribe((HasYard) => {
        this.updateHasYardValidations(HasYard);
      });

    this.industrialForm?.get(IndustrialControls?.ClearHeightMax)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMaxValidator
    ]);
    this.industrialForm?.get(IndustrialControls?.ClearHeightMax)?.updateValueAndValidity();
    this.industrialForm?.get(IndustrialControls?.ClearHeightMin)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMinValidator
    ]);
    this.industrialForm?.get(IndustrialControls?.ClearHeightMin)?.updateValueAndValidity();
    this.industrialForm.get(IndustrialControls?.ClearHeightMax)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {
      this.industrialForm.get(IndustrialControls?.ClearHeightMin)?.updateValueAndValidity({ emitEvent: false });
    });
    this.industrialForm.get(IndustrialControls?.ClearHeightMin)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.industrialForm.get(IndustrialControls?.ClearHeightMax)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });
    this.industrialForm.get(IndustrialControls?.BuildingSF)?.valueChanges?.pipe(distinctUntilChanged())
      .subscribe(() => {
        this.onParkingChange();
      });
  }

  updateYardPavedValidations(YardPavedyn) {
    if (!YardPavedyn) {
      this.industrialForm.controls[IndustrialControls.HardstandArea].disable();
      this.industrialForm.controls[IndustrialControls.HardstandArea].clearValidators();
      this.industrialForm.controls[IndustrialControls.HardstandArea].markAsUntouched();
      this.industrialForm.controls[IndustrialControls.HardstandAreaSource].disable();
      this.industrialForm.controls[IndustrialControls.HardstandAreaSource].clearValidators();
      this.industrialForm.controls[IndustrialControls.HardstandAreaSource].markAsUntouched();
      this.property.HardstandAreaSourceID = null;
      this.property.HardstandArea = null;
      this.hardstandAreaPolygon = undefined;
    } else {
      this.industrialForm.controls[IndustrialControls.HardstandArea].enable();
      this.industrialForm.controls[IndustrialControls.HardstandArea].setValidators([Validators.required]); 
      this.industrialForm.controls[IndustrialControls.HardstandAreaSource].enable();
      this.industrialForm.controls[IndustrialControls.HardstandAreaSource].setValidators([Validators.required]);
  
      if (this.property.HardstandAreaSourceID) {
        this.industrialForm.controls[IndustrialControls.HardstandAreaSource].setValue(this.property.HardstandAreaSourceID, { emitEvent: false });
      }
      if (this.property.HardstandArea) {
        this.industrialForm.controls[IndustrialControls.HardstandArea].setValue(this.property.HardstandArea, { emitEvent: false });
      }
    }
    // Update validity
    this.industrialForm.controls[IndustrialControls.HardstandArea].updateValueAndValidity();
    this.industrialForm.controls[IndustrialControls.HardstandAreaSource].updateValueAndValidity();
  }


  updateHasYardValidations(HasYard) {
    if (!HasYard) {
      this.industrialForm.controls[IndustrialControls.HasYardFenced].disable();
      this.industrialForm.controls[IndustrialControls.YardPaved].disable();
      if (this.property.HasYardFenced != null || this.property.HasYardFenced != undefined) {
        this.property.HasYardFenced = null
        this.industrialForm.get(IndustrialControls.HasYardFenced).markAsDirty();
      }
      if (this.property.YardPaved != null || this.property.YardPaved != undefined) {
        this.property.YardPaved = null;
        this.industrialForm.get(IndustrialControls.YardPaved).markAsDirty();
      }
      if(this.property.HardstandArea ){
        this.property.HardstandArea = null;
        this.hardstandAreaPolygon = undefined;
        this.industrialForm.get(IndustrialControls.HardstandArea).markAsDirty;
        this.industrialForm.get(IndustrialControls.HardstandArea).clearValidators();
      }
      if(this.property.HardstandAreaSourceID){
        this.property.HardstandAreaSourceID = null;
        this.industrialForm.get(IndustrialControls.HardstandAreaSource).markAsDirty;
        this.industrialForm.get(IndustrialControls.HardstandAreaSource).clearValidators();
      }
    } else {
      this.industrialForm.controls[IndustrialControls.HasYardFenced].enable();
      this.industrialForm.controls[IndustrialControls.YardPaved].enable();
    }
    this.industrialForm.get(IndustrialControls.HasYardFenced).updateValueAndValidity();
    this.industrialForm.get(IndustrialControls.YardPaved).updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field];
    if (IndustrialYesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  onHardstandAreaClicked() {
    this.showHardStandArea = true;
  }

  onHardstandAreaSave({ area, polygon }) {
    if (area) {
      this.property.HardstandArea = area;
      this.industrialForm.get(IndustrialControls.HardstandArea).markAsDirty();
      if (this.property.HardstandAreaSourceID !== PropertyDetailsDTO.HardstandAreaSourceIDEnum?.AerialEstimation) {
        this.property.HardstandAreaSourceID = PropertyDetailsDTO.HardstandAreaSourceIDEnum?.AerialEstimation; //If the area is calculated from the map,default the HardstandSizeSource to Aerial Estimation        
      }
      this.hardstandAreaPolygon = { area, polygon }
    }
    this.showHardStandArea = false;
  }

  onParkingChange() {
    if (this.property.ParkingSpaces && this.property.BuildingSF) {
      const ratio = (this.property.ParkingSpaces * 100) / this.property.BuildingSF;
      this.property.ParkingRatio = parseFloat(Number(ratio).toFixed(2)).toString();
    }
  }

}
