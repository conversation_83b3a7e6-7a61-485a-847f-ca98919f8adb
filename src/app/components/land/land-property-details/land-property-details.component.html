<div [formGroup]="landForm" class="occupancy-wrapper">
  <div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Land Use</div>
          <div class="col-md-7">
            <ng-select formControlName="LandUseID" [items]="getDropdownFromLookup('LandUse')"
              [virtualScroll]="true" bindLabel="LandUseName" bindValue="LandUseID"
              placeholder="--Select--" [(ngModel)]="property.LandUse"
              labelForId="LandUse">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Highest & Best Use</div>
          <div class="col-md-7">
            <ng-select formControlName="SpecificUse" [items]="getDropdownFromLookup('SpecificUseID')"
              [virtualScroll]="true" bindLabel="SpecificUsesName" bindValue="SpecificUsesID"
              placeholder="--Select--" [(ngModel)]="property.SpecificUseID"
              labelForId="SpecificUseID">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Zoning</div>
          <div class="col-md-7">
            <input type="text" formControlName="Zoning" [(ngModel)]="property.Zoning" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Zoning Code</div>
          <div class="col-md-7">
            <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="ZoningCode" [(ngModel)]="property.ZoningCode" class="form-control">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Potential Zoning</div>
          <div class="col-md-7">
            <ng-select formControlName="PotentialZoningID" [items]="getDropdownFromLookup('PotentialZoningID')"
              [virtualScroll]="true" bindLabel="ZoningClassName" bindValue="ZoningClassEnum"
              placeholder="--Select--" [(ngModel)]="property.PotentialZoningID"
              labelForId="PotentialZoningID">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Has Fenced Yard</div>
          <div class="col-md-7">
            <ng-select formControlName="HasYardFenced" [items]="getDropdownFromLookup('HasYardFenced')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasYardFenced"
              labelForId="HasYardFenced">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Has Unfenced Yard</div>
          <div class="col-md-7">
            <ng-select formControlName="HasYardUnfenced" [items]="getDropdownFromLookup('HasYardUnfenced')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasYardUnfenced"
              labelForId="HasYardUnfenced">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Has Paved Yard</div>
          <div class="col-md-7">
            <ng-select formControlName="YardPaved" [items]="getDropdownFromLookup('YardPaved')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.YardPaved"
              labelForId="YardPaved">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Utility Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="UtilityComments" [(ngModel)]="property.UtilityComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Book Value</div>
          <div class="col-md-7">
            <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="BookValue" [(ngModel)]="property.BookValue" class="form-control">
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Surrounding Land Use</div>
          <div class="col-md-7">
            <ng-select formControlName="SurroundingLandUse" [items]="getDropdownFromLookup('SurroundingLandUse')"
              [virtualScroll]="true" bindLabel="ZoningClassName" bindValue="ZoningClassEnum"
              placeholder="--Select--" [(ngModel)]="property.SurroundingLandUse"
              labelForId="SurroundingLandUse">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">NLA (SQM)</div>
          <div class="col-md-7">
            <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="NLA_SF" [(ngModel)]="property.NLA_SF" class="form-control"
            (paste)="validatePasteInput($event, true)"
            (keypress)="validateIntegerInput($event, true)">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">NLA (Hectares)</div>
          <div class="col-md-7">
            <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="NLAac" [(ngModel)]="property.NLAac" class="form-control"
            (paste)="validatePasteInput($event, true)"
            (keypress)="validateIntegerInput($event, true)">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Width and Depth</div>
          <div class="col-md-7 row">
            <div class="col-md-5">
              <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="Width" [(ngModel)]="property.Width" class="form-control"
              (paste)="validatePasteInput($event, true)"
              (keypress)="validateIntegerInput($event, true)">
            </div>
            <div class="col-md-2">x</div>
            <div class="col-md-5">
              <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="Depth" [(ngModel)]="property.Depth" class="form-control"
              (paste)="validatePasteInput($event, true)"
              (keypress)="validateIntegerInput($event, true)">
            </div>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Flood Plain</div>
          <div class="col-md-7">
            <ng-select formControlName="IsFloodPlain" [items]="getDropdownFromLookup('IsFloodPlain')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.IsFloodPlain"
              labelForId="IsFloodPlain">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Earthquake Zone</div>
          <div class="col-md-7">
            <ng-select formControlName="EarthquakeZoneID" [items]="getDropdownFromLookup('EarthquakeZoneID')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.EarthquakeZoneID"
              labelForId="EarthquakeZoneID">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Rail Served</div>
          <div class="col-md-7">
            <ng-select formControlName="RailServed" [items]="getDropdownFromLookup('RailServed')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.RailServed"
              labelForId="RailServed">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Port Access</div>
          <div class="col-md-7">
            <ng-select formControlName="HasPortAccess" [items]="getDropdownFromLookup('HasPortAccess')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasPortAccess"
              labelForId="HasPortAccess">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Property Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="PropertyComments" [(ngModel)]="property.PropertyComments" class="form-control"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Book Value Date</div>
          <div class="col-md-7">
            <input class="form-control" placeholder="Click to select a date"
              formControlName="BookValueDate" angular-mydatepicker name="BookValueDate"
              (click)="bookValueDate.toggleCalendar()" [(ngModel)]="property.BookValueDate"
              [options]="myDpOptions"
              #bookValueDate="angular-mydatepicker" />
              <i class="far fa-calendar-alt" aria-hidden="true" (click)="bookValueDate.toggleCalendar()"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
