// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
// Third-party imports
import { IAngularMyDpOptions } from 'angular-mydatepicker';
// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { BindNamesWithLookupName, YesNoFields, YesOrNoList } from '../../../common/constants';
import { LandControls } from '../../../enumerations/landControlKeys';
import { LoginService } from '../../../services/login.service';
import { validateIntegerInput, validatePasteInput } from '../../../utils';

@Component({
  selector: 'app-land-property-details',
  templateUrl: './land-property-details.component.html',
  styleUrls: ['./land-property-details.component.css']
})
export class LandPropertyDetailsComponent implements OnInit {

  @Input() landForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false
  };
  dateFormat: string;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  constructor(private _loginService:LoginService) { }

  ngOnInit(): void {
    this.dateFormat = this._loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.dateFormat = this.dateFormat;
    this.addControls();
  }

  addControls() {
    Object.keys(LandControls).forEach((controlName: LandControls) => {
      this.landForm.addControl(controlName, new FormControl(''));
    });
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field];
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

}
