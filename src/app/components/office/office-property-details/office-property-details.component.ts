import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { numericValidator } from '../../../utils';
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-office-property-details',
  templateUrl: './office-property-details.component.html',
  styleUrls: ['./office-property-details.component.css'],
})
export class OfficePropertyDetailsComponent implements OnInit {
  @Input() officeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;

  constructor() { }

  ngOnInit(): void {
    this.addControls();
  }

  addControls() {
    Object.keys(OfficeControls).forEach((controlName: OfficeControls) => {
      if ([OfficeControls.SmallestFloor, OfficeControls.LargestFloor, OfficeControls.TypicalFloorSizeSM].includes(controlName)) {
        this.officeForm.addControl(controlName, new FormControl('', [numericValidator()]));
      } else {
        this.officeForm.addControl(controlName, new FormControl(''));
      }
    });
  }
  
}
