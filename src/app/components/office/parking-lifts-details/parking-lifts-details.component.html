<div [formGroup]="officeForm" class="details-wrapper">
  <div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Typical Floor Size</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputTypicalFloorSizeSM">
              <input type="text" numericOnly [allowDecimal]="true" [allowNegative]="false" formControlName="TypicalFloorSizeSM" [(ngModel)]="property.TypicalFloorSize" class="form-control"
              [ngClass]="{'error-field': !officeForm.controls['TypicalFloorSizeSM']?.valid}" (paste)="validatePasteInput($event, true)"
              (keypress)="validateIntegerInput($event, true)" data-testId="property-details-typical-size">
            </ng-container>
            <ng-template #masterRollupInputTypicalFloorSizeSM>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TypicalFloorSize" title="{{rollupMasterFreeholdFieldsObject?.TypicalFloorSize}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Typical Floor Plate Source</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="TypicalFloorSizeSourceID"
              [items]="getDropdownFromLookup('TypicalFloorSizeSourceID')" [virtualScroll]="true" bindLabel="TypicalFloorPlateName"
              bindValue="TypicalFloorPlateEnum" placeholder="--Select--" [(ngModel)]="property.TypicalFloorSizeSourceID"
              labelForId="TypicalFloorSizeSourceID" data-testId="property-details-typical-source"
              [ngClass]="{'error-field':(officeForm?.controls['TypicalFloorSizeSourceID']?.enabled && !officeForm?.controls['TypicalFloorSizeSourceID']?.valid)}">
            </ng-select>
            <input type="text" [value]="rollupMasterFreeholdFieldsObject?.TypicalFloorSizeSourceID" *ngIf="condo === EnumCondoTypeNames.MasterFreehold"
              title="{{rollupMasterFreeholdFieldsObject?.TypicalFloorSizeSourceID}}" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper" *ngIf="property.UseTypeID === propertyUseTypes.Apartments">
          <div class="col-md-5 label"># of Units</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputNoOfUnits">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="NoOfUnits"
                [(ngModel)]="property.NoOfUnits" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputNoOfUnits>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.NoOfUnits" title="{{rollupMasterFreeholdFieldsObject?.NoOfUnits}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinklers</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasSprinkler" [items]="getDropdownFromLookup('HasSprinkler')"
              [virtualScroll]="true" bindLabel="label" bindValue="value" data-testId="property-details-sprinkler"
              placeholder="--Select--" [(ngModel)]="property.HasSprinkler"
              labelForId="HasSprinkler">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasSprinkler)" type="text" class="form-control"  readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Sprinkler Type</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="SprinklerTypeID" [items]="getDropdownFromLookup('SprinklerTypeID')"
              [virtualScroll]="true" bindLabel="SprinklerTypeName" bindValue="SprinklerTypeEnum" data-testId="property-details-sprinkler-type"
              placeholder="--Select--" [(ngModel)]="property.SprinklerTypeID"
              labelForId="SprinklerType">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" readonly title="{{rollupMasterFreeholdFieldsObject?.SprinklerTypeName}}" [value]="rollupMasterFreeholdFieldsObject?.SprinklerTypeName" type="text" class="form-control">
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Amenities Type</div>
          <div class="col-md-7">
            <ng-select formControlName="AmenitiesTypeID" [items]="getDropdownFromLookup('AmenitiesTypeID')" [multiple]="true"
              [virtualScroll]="true" bindLabel="AmenitiesTypeName" bindValue="AmenitiesTypeEnum" data-testId="property-details-amenities-type"
              placeholder="--Select--" [(ngModel)]="amenitiesType" [ngClass]="{'scrollable-dropdown': condo === EnumCondoTypeNames.MasterFreehold}"
              labelForId="AmenitiesType" (change)="onAmenitiesChange()">
            </ng-select>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Amenities Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="Amenities" [(ngModel)]="property.Amenities" class="form-control" data-testId="property-details-amenities-comments"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Comments</div>
          <div class="col-md-7">
            <textarea rows="2" formControlName="BuildingComments" [(ngModel)]="property.BuildingComments" class="form-control" data-testId="property-details-building-comments"></textarea>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Building Website</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputBuildingWebsite">
              <input type="text" formControlName="BuildingWebsite" [(ngModel)]="property.BuildingWebsite" class="form-control"
                [ngClass]="{'error-field': !isBuildingWebsiteValueValid}" data-testId="property-details-building-website">
            </ng-container>
            <ng-template #masterRollupInputBuildingWebsite>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.BuildingWebsite" title="{{rollupMasterFreeholdFieldsObject?.BuildingWebsite}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">Parking</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Reserved Parking</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold"  formControlName="HasReservedParkingSpaces" [items]="getDropdownFromLookup('HasReservedParkingSpaces')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasReservedParkingSpaces"
              labelForId="HasReservedParkingSpaces">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasReservedParkingSpaces)" type="text" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Reserved</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputReservedParkingSpaces">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="ReservedParkingSpaces"
                [(ngModel)]="property.ReservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputReservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Reserved Rate/mo</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputReservedParkingSpacesRatePerMonth">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="ReservedParkingSpacesRatePerMonth"
              [(ngModel)]="property.ReservedParkingSpacesRatePerMonth" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputReservedParkingSpacesRatePerMonth>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ReservedParkingSpacesRatePerMonth" title="{{rollupMasterFreeholdFieldsObject?.ReservedParkingSpacesRatePerMonth}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Unreserved Parking</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasUnreservedParkingSpaces" [items]="getDropdownFromLookup('HasUnreservedParkingSpaces')"
              [virtualScroll]="true" bindLabel="label" bindValue="value"
              placeholder="--Select--" [(ngModel)]="property.HasUnreservedParkingSpaces"
              labelForId="HasUnreservedParkingSpaces">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasUnreservedParkingSpaces)" type="text" class="form-control" readonly>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Unreserved</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputUnreservedParkingSpaces">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="UnreservedParkingSpaces"
                [(ngModel)]="property.UnreservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputUnreservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Unreserved Rate/mo</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputUnreservedParkingSpacesRatePerMonth">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="UnreservedParkingSpacesRatePerMonth"
              [(ngModel)]="property.UnreservedParkingSpacesRatePerMonth" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputUnreservedParkingSpacesRatePerMonth>
              <input type="text" 
              [value]="rollupMasterFreeholdFieldsObject?.UnreservedParkingSpacesRatePerMonth" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.UnreservedParkingSpacesRatePerMonth}}" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="title">Lifts</div>
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Passenger</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputPassengerElevators">
              <input formControlName="PassengerElevators" [(ngModel)]="property.PassengerElevators" class="form-control" 
              type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" >
            </ng-container>
            <ng-template #masterRollupInputPassengerElevators>
              <input [value]="rollupMasterFreeholdFieldsObject?.PassengerElevators" title="{{rollupMasterFreeholdFieldsObject?.PassengerElevators}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Freight</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputFreighElevators">
              <input formControlName="FreighElevators" [(ngModel)]="property.FreighElevators" class="form-control" type="text"
                numericOnly [allowDecimal]="false" [allowNegative]="false">
            </ng-container>
            <ng-template #masterRollupInputFreighElevators>
              <input  [value]="rollupMasterFreeholdFieldsObject?.FreighElevators" title="{{rollupMasterFreeholdFieldsObject?.FreighElevators}}" class="form-control" type="text" readonly>
            </ng-template>
            </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label"># of Parking</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputFreighParkingElevators">
              <input formControlName="ParkingElevators" [(ngModel)]="property.ParkingElevators" class="form-control"
              type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" >
            </ng-container>
            <ng-template #masterRollupInputFreighParkingElevators>
              <input  [value]="rollupMasterFreeholdFieldsObject?.ParkingElevators" title="{{rollupMasterFreeholdFieldsObject?.ParkingElevators}}" class="form-control" type="text" readonly>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
