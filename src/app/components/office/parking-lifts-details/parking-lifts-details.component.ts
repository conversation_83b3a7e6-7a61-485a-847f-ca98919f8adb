// Angular core imports
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';

// RxJS imports
import { debounceTime } from 'rxjs/operators';

// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { UseTypes } from '../../../../app/enumerations/useTypes';
import { YesNoFields, YesOrNoList, BindNamesWithLookupName } from '../../../common/constants';
import { OfficeControls } from '../../../enumerations/officeControlKeys';
import { ResearchType } from '../../../enumerations/researchType';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { isValidWebsite, updateValidation, validateIntegerInput, validatePasteInput } from '../../../utils';
import { DebounceTimeConfigFormControlDelayInMS } from '../../../constants';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-parking-lifts-details',
  templateUrl: './parking-lifts-details.component.html',
  styleUrls: ['./parking-lifts-details.component.css']
})
export class ParkingLiftsDetailsComponent implements OnInit {
  @Input() officeForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum
  amenitiesType;
  isBuildingWebsiteValueValid = true;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
   propertyUseTypes = UseTypes;

  constructor(private _loginService:LoginService, public yesOrNoService: YesOrNoService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.property || changes.condo) {
      this.toggleFeatureBasedOnCondo();
    }
    if (changes.property) {
      this.updateAmenitiesType(changes.property?.currentValue);
    }
  }

  ngOnInit(): void {
    this.updateAmenitiesType(this.property);
    this.officeForm.get(OfficeControls.HasReservedParkingSpaces).valueChanges.subscribe(
      (HasReservedCoveredParking) => {
        this.updateHasReservedCoveredParkingValidations(HasReservedCoveredParking);
      }
    );
    this.officeForm.get(OfficeControls.HasSprinkler).valueChanges.subscribe(
      (HasSprinkler) => {
        this.updateHasSprinklerValidations(HasSprinkler);
      }
    );
    this.officeForm.get(OfficeControls.HasUnreservedParkingSpaces).valueChanges.subscribe(
      (Hasunreservedcoveredparking) => {
        this.updateHasUnReservedCoveredParkingValidations(Hasunreservedcoveredparking)
      }
    );

     this.officeForm.get(OfficeControls.BuildingWebsite)?.valueChanges?.subscribe(
          (value) => {
            this.isBuildingWebsiteValueValid = isValidWebsite(value);
          }
        );

    this.officeForm?.get(OfficeControls?.TypicalFloorSizeSM)?.valueChanges?.pipe(debounceTime(DebounceTimeConfigFormControlDelayInMS))?.subscribe(
      (value) => {
        updateValidation(value, OfficeControls?.TypicalFloorSizeSourceID, 'TypicalFloorSizeSourceID', OfficeControls, this.property, this.officeForm);
      }
    );
    this.officeForm?.get(OfficeControls?.TypicalFloorSizeSM)?.updateValueAndValidity();
  }

  getDropdownFromLookup(field: string) {
    const key = BindNamesWithLookupName[field];
    if (YesNoFields.includes(field)) {
      return YesOrNoList;
    } else {
      return this.lookupDropdowns[key] || [];
    }
  }

  updateHasReservedCoveredParkingValidations(HasReservedCoveredParking) {
    if (!HasReservedCoveredParking) {
      this.officeForm.controls[OfficeControls.ReservedParkingSpaces].disable();
      this.officeForm.controls[OfficeControls.ReservedParkingSpacesRatePerMonth].disable();
    } else {
      this.officeForm.controls[OfficeControls.ReservedParkingSpaces].enable();
      this.officeForm.controls[OfficeControls.ReservedParkingSpacesRatePerMonth].enable();
    }
    this.officeForm.get(OfficeControls.ReservedParkingSpaces).updateValueAndValidity();
    this.officeForm.get(OfficeControls.ReservedParkingSpacesRatePerMonth).updateValueAndValidity();
  }

  updateHasSprinklerValidations(HasSprinkler) {
    if (!HasSprinkler) {
      this.officeForm.controls[OfficeControls.SprinklerTypeID].disable();
      this.officeForm.get(OfficeControls.SprinklerTypeID).clearValidators();
      this.property.SprinklerTypeID = null;
    } else {
      this.officeForm.controls[OfficeControls.SprinklerTypeID].enable();
      if (this.propertyStatus && this.propertyStatus !== ResearchType.Hidden) {
        this.officeForm.get(OfficeControls.SprinklerTypeID).setValidators([Validators.required]);
      }
    }
    this.officeForm.get(OfficeControls.SprinklerTypeID).updateValueAndValidity();
  }

  updateHasUnReservedCoveredParkingValidations(Hasunreservedcoveredparking) {
    if (!Hasunreservedcoveredparking) {
      this.officeForm.controls[OfficeControls.UnreservedParkingSpaces].disable();
      this.officeForm.controls[OfficeControls.UnreservedParkingSpacesRatePerMonth].disable();
    } else {
      this.officeForm.controls[OfficeControls.UnreservedParkingSpaces].enable();
      this.officeForm.controls[OfficeControls.UnreservedParkingSpacesRatePerMonth].enable();
    }
    this.officeForm.get(OfficeControls.UnreservedParkingSpaces).updateValueAndValidity();
    this.officeForm.get(OfficeControls.UnreservedParkingSpacesRatePerMonth).updateValueAndValidity();
  }

  updateAmenitiesType(property) {
    this.amenitiesType = property?.AmenitiesType?.split(',');
    this.amenitiesType = this.amenitiesType.map((item) => item.trim());
  }

  toggleFeatureBasedOnCondo(){
    if (this.condo === this.EnumCondoTypeNames.MasterFreehold) {
      this.officeForm.controls[OfficeControls.AmenitiesTypeID]?.disable();
    } else {
      this.officeForm.controls[OfficeControls.AmenitiesTypeID]?.enable();
    }
  }

  onAmenitiesChange() {
    this.property.AmenitiesType = this.amenitiesType?.join(',');
  }
}
