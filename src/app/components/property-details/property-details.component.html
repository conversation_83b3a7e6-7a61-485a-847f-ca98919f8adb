<div *ngIf="property.UseTypeID === propertyUseTypes.Office || property.UseTypeID == propertyUseTypes.Apartments || property.UseTypeID == propertyUseTypes.SpecialUse">
  <app-office-property-details
    [officeForm]="officeForm"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="propertyStatus"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-office-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Industrial">
  <app-industrial-property-details
    [industrialForm]="industrialForm"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="propertyStatus"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-industrial-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Retail">
  <app-retail-property-details
    [retailForm]="retailForm"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="propertyStatus"
    [condo]="condo"
    [rollupMasterFreeholdFieldsObject]="rollupMasterFreeholdFieldsObject"
  ></app-retail-property-details>
</div>
<div *ngIf="property.UseTypeID === propertyUseTypes.Land">
  <app-land-property-details
    [landForm]="landForm"
    [property]="property"
    [dataArray]="dataArray"
    [propertyCopy]="propertyCopy"
    [lookupDropdowns]="lookupDropdowns"
    [propertyStatus]="propertyStatus"
  ></app-land-property-details>
</div>
