import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { UseTypes } from '../../enumerations/useTypes';
import { PropertyDetailsDTO } from '../../../app/api-client';
import { RollupObject } from '../../models/rollupObject';

@Component({
  selector: 'app-property-details',
  templateUrl: './property-details.component.html',
  styleUrls: ['./property-details.component.css']
})
export class PropertyDetailsComponent implements OnInit {
  @Input() propertyForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  propertyUseTypes = UseTypes

  constructor() { }

  ngOnInit(): void {
  }

  get officeForm(): FormGroup {
    return this.propertyForm.get('OfficeForm') as FormGroup;
  }
  
  get industrialForm(): FormGroup {
    return this.propertyForm.get('IndustrialForm') as FormGroup;
  }
  
  get retailForm(): FormGroup {
    return this.propertyForm.get('RetailForm') as FormGroup;
  }
  
  get landForm(): FormGroup {
    return this.propertyForm.get('LandForm') as FormGroup;
  }

}
