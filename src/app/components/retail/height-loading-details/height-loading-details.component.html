<div [formGroup]="retailForm">
    <div class="row">
      <div class="col-md-6">
        <div class="title">Use Details</div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Retail Frontage</div>
          <div class="col-md-7">
            <img class="icon" src="/assets/images/DrawMap.png" (click)="onRetailFrontageClicked()">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputRetailFrontage">
              <input type="text" maxlength="11" numericOnly [allowDecimal]="true" [allowNegative]="false"
                (paste)="validatePasteInput($event, true)"
                (keypress)="validateIntegerInput($event, true)"
                formControlName="RetailFrontage" name="RetailFrontage" id="RetailFrontage" class="form-control maxnumbervalidation"
                [(ngModel)]="property.RetailFrontage">
            </ng-container>
            <ng-template #masterRollupInputRetailFrontage>
              <input type="text" 
                 name="RetailFrontage" id="RetailFrontage" class="form-control" title="{{rollupMasterFreeholdFieldsObject?.RetailFrontage}}"
                [value]="rollupMasterFreeholdFieldsObject?.RetailFrontage" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Traffic Count</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputTrafficCount">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" maxLength="4" formControlName="TrafficCount"
                class="form-control" name="TrafficCount" [(ngModel)]="property.TrafficCount">
            </ng-container>
            <ng-template #masterRollupInputTrafficCount>
              <input type="text" class="form-control" [value]="rollupMasterFreeholdFieldsObject?.TrafficCount" title="{{rollupMasterFreeholdFieldsObject?.TrafficCount}}" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input"># of Anchors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputNoOfAnchor">
              <input type="text" maxlength="11" numericOnly [allowNegative]="false" [allowDecimal]="false" formControlName="Anchors"
                name="Anchors" id="TotalAnchorSF" class="form-control maxnumbervalidation" [(ngModel)]="property.Anchors">
            </ng-container>
            <ng-template #masterRollupInputNoOfAnchor>
              <input type="text"  title="{{rollupMasterFreeholdFieldsObject?.NoOfAnchor}}"
                name="Anchors" id="TotalAnchorSF" class="form-control maxnumbervalidation"
                [value]="rollupMasterFreeholdFieldsObject?.NoOfAnchor" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Anchor Size</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputTotalAnchorSF">
              <input type="text" maxlength="11" numericOnly [allowNegative]="false" formControlName="TotalAnchorSF"
                name="TotalAnchorSF" id="TotalAnchorSF" class="form-control maxnumbervalidation"
                [(ngModel)]="property.TotalAnchorSF">
            </ng-container>
            <ng-template #masterRollupInputTotalAnchorSF>
              <input type="text" title="{{rollupMasterFreeholdFieldsObject?.TotalAnchorSF}}"
                name="TotalAnchorSF" id="TotalAnchorSF" class="form-control maxnumbervalidation"
                [value]="rollupMasterFreeholdFieldsObject?.TotalAnchorSF" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Covered Parking</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputReservedParkingSpaces">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="ReservedParkingSpaces"
                [(ngModel)]="property.ReservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputReservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.ReservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Uncovered parking</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputUnreservedParkingSpaces">
              <input type="text" numericOnly [allowDecimal]="false" [allowNegative]="false" formControlName="UnreservedParkingSpaces"
                [(ngModel)]="property.UnreservedParkingSpaces" class="form-control">
            </ng-container>
            <ng-template #masterRollupInputUnreservedParkingSpaces>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.UnreservedParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Total Parking Spaces</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputParkingSpaces">
              <input type="text" numericOnly [allowNegative]="false" [allowDecimal]="false" formControlName="ParkingSpaces" [(ngModel)]="property.ParkingSpaces"
              class="form-control" (blur)="onParkingChange()">
            </ng-container>
            <ng-template #masterRollupInputParkingSpaces>
              <input type="number"
                [value]="rollupMasterFreeholdFieldsObject?.ParkingSpaces" title="{{rollupMasterFreeholdFieldsObject?.ParkingSpaces}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Parking Ratio</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputParkingRatio">
              <input type="text" numericOnly [allowNegative]="false" [allowDecimal]="true" formControlName="ParkingRatio" [(ngModel)]="property.ParkingRatio" class="form-control"
               readonly>
            </ng-container>
            <ng-template #masterRollupInputParkingRatio>
              <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ParkingRatio" title="{{rollupMasterFreeholdFieldsObject?.ParkingRatio}}" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
      </div>
  
      <div class="col-md-6">
        <div class="title">Loading & Height</div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">On Grade Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputGradeLevelDriveIn">
              <input type="text" numericOnly [allowNegative]="false" [allowDecimal]="false" formControlName="GradeLevelDriveIn" [(ngModel)]="property.GradeLevelDriveIn"
              class="form-control">
            </ng-container>
            <ng-template #masterRollupInputGradeLevelDriveIn>
              <input type="number" title="{{rollupMasterFreeholdFieldsObject?.GradeLevelDriveIn}}"
                [value]="rollupMasterFreeholdFieldsObject?.GradeLevelDriveIn" class="form-control" readonly>
            </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Dock High Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputDockHigh">
              <input type="text" formControlName="DockHigh" [(ngModel)]="property.DockHigh" class="form-control"
              [ngClass]="{'error-field':(!retailForm.controls['DockHigh']?.valid)}">
            </ng-container>
            <ng-template #masterRollupInputDockHigh>
              <input type="number"  [value]="rollupMasterFreeholdFieldsObject?.DockHigh" title="{{rollupMasterFreeholdFieldsObject?.DockHigh}}"
                class="form-control" readonly>
            </ng-template>
            <div class="validation-error"
              *ngIf="(retailForm.controls['DockHigh']?.hasError('max')||retailForm.controls['DockHigh']?.hasError('min'))">
              Value must be between 0 and 999
            </div>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Recessed/Truckwell Doors</div>
          <div class="col-md-7">
            <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputTruckwell">
              <input type="text" formControlName="Truckwell" [(ngModel)]="property.Truckwell" class="form-control"
              [ngClass]="{'error-field':(!retailForm.controls['Truckwell']?.valid)}">
            <div class="validation-error"
              *ngIf="(retailForm.controls['Truckwell'].hasError('max')||retailForm.controls['Truckwell'].hasError('min'))">
              Value must be between 0 and 999</div>
            </ng-container>
           <ng-template #masterRollupInputTruckwell>
            <input type="text" [value]="rollupMasterFreeholdFieldsObject?.Truckwell" title="{{rollupMasterFreeholdFieldsObject?.Truckwell}}" class="form-control" readonly>
           </ng-template>
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Clear Height</div>
          <div class="col-md-7 row">
            <div class="col-md-6">
              <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputClearHeightMin">
                <input type="text" formControlName="ClearHeightMin" [(ngModel)]="property.ClearHeightMin" class="form-control"
                  [ngClass]="{'error-field':(retailForm?.controls['ClearHeightMin']?.errors?.errMsg)}">
                  <div class="prop-validator error error-msg" *ngIf="retailForm?.get('ClearHeightMin')?.errors?.errMsg && !retailForm?.get('ClearHeightMin')?.valid">
                    {{ retailForm?.get('ClearHeightMin')?.errors?.errMsg }}</div>
              </ng-container>
              <ng-template #masterRollupInputClearHeightMin>
                <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ClearHeightMin" title="{{rollupMasterFreeholdFieldsObject?.ClearHeightMin}}" class="form-control" readonly>
              </ng-template>
              </div>
            <div class="col-md-6">
              <ng-container *ngIf="condo !== EnumCondoTypeNames.MasterFreehold; else masterRollupInputClearHeightMax">
                <input type="text" formControlName="ClearHeightMax" [(ngModel)]="property.ClearHeightMax" class="form-control"
                  [ngClass]="{'error-field':(retailForm?.controls['ClearHeightMax']?.errors?.errMsg)}">
                  <div class="prop-validator error error-msg" *ngIf="retailForm?.get('ClearHeightMax')?.errors?.errMsg || !retailForm?.get('ClearHeightMax')?.valid">
                    {{ retailForm?.get('ClearHeightMax')?.errors?.errMsg }}</div>
              </ng-container>
              <ng-template #masterRollupInputClearHeightMax>
                <input type="text" [value]="rollupMasterFreeholdFieldsObject?.ClearHeightMax" title="{{rollupMasterFreeholdFieldsObject?.ClearHeightMax}}" class="form-control" readonly>
              </ng-template>
            </div>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Yard</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasYard" [items]="getDropdownFromLookup('HasYard')" [virtualScroll]="true"
              bindLabel="label" bindValue="value" placeholder="--Select--" [(ngModel)]="property.HasYard"
              labelForId="HasYard">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasYard)" type="text" class="form-control"  readonly>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label">Yard, Fenced</div>
          <div class="col-md-7">
            <ng-select *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="HasYardFenced" [items]="getDropdownFromLookup('HasYardFenced')"
              [virtualScroll]="true" bindLabel="label" bindValue="value" placeholder="--Select--"
              [(ngModel)]="property.HasYardFenced" labelForId="HasYardFenced">
            </ng-select>
            <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold"  [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.HasYardFenced)" type="text" class="form-control"  readonly>
          </div>
        </div>
  
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Vented</div>
          <div class="col-md-7">
            <ng-select  *ngIf="condo !== EnumCondoTypeNames.MasterFreehold" formControlName="IsVented" [items]="getDropdownFromLookup('IsVented')" [virtualScroll]="true"
              bindLabel="label" bindValue="value" placeholder="--Select--" [(ngModel)]="property.IsVented"
              labelForId="IsVented"></ng-select>
              <input *ngIf="condo === EnumCondoTypeNames.MasterFreehold" [value]="yesOrNoService.getYesOrNoLabel(rollupMasterFreeholdFieldsObject?.IsVented)" type="text" class="form-control" readonly>
          </div>
        </div>
      </div>
    </div>
  </div>
<!--*********************** Retail Frontage Measurement ************************* -->
<div *ngIf="showRetailFrontage">
  <imperium-modal [width]="'xl-large'" [height]="'large'" [(visible)]="showRetailFrontage"
    [title]="'Retail Frontage Measurement'" [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-distance-measurement-modal [latLng]="{lat: property.Location.Latitude, lng: property.Location.Longitude}"
        [retailFrontagePolyline]="retailFrontagePolyline"
        (onRetailFrontageSave)="onReatilFrontageSave($event)"></app-distance-measurement-modal>
    </ng-template>
  </imperium-modal>
</div>
