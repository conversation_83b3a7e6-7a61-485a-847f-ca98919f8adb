// Angular core imports
import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
// RxJS imports
import { distinctUntilChanged } from 'rxjs/operators';
// Application-specific imports
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { IndustrialYesNoFields, YesOrNoList } from '../../../common/constants';
import { RetailControls } from '../../../enumerations/RetailControlKeys';
import { LoginService } from '../../../services/login.service';
import { YesOrNoService } from '../../../services/yes-or-no.service';
import { clearHeightMaxValidator, clearHeightMinValidator, validateIntegerInput, validatePasteInput } from '../../../utils';
import { RollupObject } from '../../../models/rollupObject';

@Component({
  selector: 'app-height-loading-details',
  templateUrl: './height-loading-details.component.html',
  styleUrls: ['./height-loading-details.component.css']
})
export class HeightLoadingDetailsComponent implements OnInit {

  @Input() retailForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: PropertyDetailsDTO.CondoTypeIDEnum;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  clearHeightMinError = false;
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  showRetailFrontage = false;
  retailFrontagePolyline: { distance: number, polyline: any };

  constructor(private _loginService: LoginService, public yesOrNoService: YesOrNoService) { }

  ngOnInit(): void {
    this.retailForm?.get(RetailControls?.ClearHeightMax)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMaxValidator
    ]);
    this.retailForm?.get(RetailControls?.ClearHeightMax)?.updateValueAndValidity();
    this.retailForm?.get(RetailControls?.ClearHeightMin)?.setValidators([
      Validators.min(3),
      Validators.max(999),
      clearHeightMinValidator
    ]);
    this.retailForm?.get(RetailControls?.ClearHeightMin)?.updateValueAndValidity();
    this.retailForm.get(RetailControls?.ClearHeightMax)?.valueChanges?.pipe(distinctUntilChanged()).subscribe(() => {  // Only trigger if the value has actually changed
      this.retailForm.get(RetailControls?.ClearHeightMin)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
    });
    this.retailForm.get(RetailControls?.ClearHeightMin)?.valueChanges?.pipe(distinctUntilChanged()) // Only trigger if the value has actually changed
      .subscribe(() => {
        this.retailForm.get(RetailControls?.ClearHeightMax)?.updateValueAndValidity({ emitEvent: false }); // Prevent cycle
      });
    this.retailForm.get(RetailControls?.BuildingSF)?.valueChanges?.pipe(distinctUntilChanged())
      .subscribe(() => {
        this.onParkingChange();
      });
  }


  getDropdownFromLookup(field: string) {
    if (IndustrialYesNoFields.includes(field)) {
      return YesOrNoList;
    }
  }

  onParkingChange() {
    if (this.property.ParkingSpaces && this.property.BuildingSF) {
      const ratio = (this.property.ParkingSpaces * 100) / this.property.BuildingSF;
      this.property.ParkingRatio = parseFloat(Number(ratio).toFixed(2)).toString();
    }
  }

  onRetailFrontageClicked() {
    this.showRetailFrontage = true;
  }

  onReatilFrontageSave({ distance, polyline }) {
    if (distance) {
      this.property.RetailFrontage = distance;
      this.retailForm.get(RetailControls?.RetailFrontage)?.markAsDirty();
      this.retailFrontagePolyline = { distance, polyline }
    }
    this.showRetailFrontage = false;
  }
}


