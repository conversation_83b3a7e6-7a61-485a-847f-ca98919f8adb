import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { RetailControls } from '../../../enumerations/RetailControlKeys';
import { numericValidator } from '../../../utils';
import { PropertyDetailsDTO } from '../../../../app/api-client';
import { RollupObject } from '../../../models/rollupObject';
@Component({
  selector: 'app-retail-property-details',
  templateUrl: './retail-property-details.component.html',
  styleUrls: ['./retail-property-details.component.css']
})
export class RetailPropertyDetailsComponent implements OnInit {

  @Input() retailForm: FormGroup;
  @Input() property: PropertyDetailsDTO;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;
  @Input() lookupDropdowns: any;
  @Input() propertyStatus: any;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  
  constructor() { }

  ngOnInit(): void {
    this.addControls();
  }

  addControls() {
    Object.keys(RetailControls).forEach((controlName: RetailControls) => {
      if (RetailControls.DockHigh === controlName || RetailControls.Truckwell === controlName) {
        this.retailForm.addControl(controlName, new FormControl('', [Validators.min(0), Validators.max(999)]))
      } else if (RetailControls.ClearHeightMax === controlName || RetailControls.ClearHeightMin === controlName) {
        this.retailForm.addControl(controlName, new FormControl('', {
          validators: [Validators.min(3), Validators.max(999)], updateOn: 'blur'
        }))
      }  else if ([RetailControls.SmallestFloor, RetailControls.LargestFloor].includes(controlName)) {
        this.retailForm.addControl(controlName, new FormControl('', [numericValidator()]));
      } else {
        this.retailForm.addControl(controlName, new FormControl(''));
      }
    });
  }

}



