export enum OfficeControls {
  BuildingClass = 'BuildingClass',
  SpecificUse = 'SpecificUse',
  BuildingSF = 'BuildingSF',
  ContributedGBASource = 'ContributedGBASource',
  ContributedGBA_SF = 'ContributedGBA_SF',
  OfficeSF = 'OfficeSF',
  OfficeNLASource = 'OfficeNLASource',
  GLAR_SF = 'GLAR_SF',
  RetailGLARSource = 'RetailGLARSource',
  YearBuilt = 'YearBuilt',
  YearRenovated = 'YearRenovated',
  EnergyStarRatingID = 'EnergyStarRatingID',
  WaterStarRatingID = 'WaterStarRatingID',
  GreenStarRatingID = 'GreenStarRatingID',
  SmallestFloor = 'SmallestFloor',
  LargestFloor = 'LargestFloor',
  TypicalFloorSizeSM = 'TypicalFloorSizeSM',
  HasSprinkler = 'HasSprinkler',
  SprinklerTypeID = 'SprinklerTypeID',
  AmenitiesTypeID = 'AmenitiesTypeID',
  Amenities = 'Amenities',
  BuildingComments = 'BuildingComments',
  BuildingWebsite = 'BuildingWebsite',
  HasReservedParkingSpaces =  'HasReservedParkingSpaces',
  ReservedParkingSpaces = 'ReservedParkingSpaces',
  ReservedParkingSpacesRatePerMonth = 'ReservedParkingSpacesRatePerMonth',
  HasUnreservedParkingSpaces =  'HasUnreservedParkingSpaces',
  UnreservedParkingSpaces = 'UnreservedParkingSpaces',
  UnreservedParkingSpacesRatePerMonth = 'UnreservedParkingSpacesRatePerMonth',
  PassengerElevators = 'PassengerElevators',
  FreighElevators = 'FreighElevators',
  ParkingElevators = 'ParkingElevators',
  TenancyTypeID = 'TenancyTypeID',
  IsOwnerOccupied = 'IsOwnerOccupied',
  GovernmentInterestID = 'GovernmentInterestID',
  BuildSpecStatusID = 'BuildSpecStatusID',
  OccupancyPercent = 'OccupancyPercent',
  Vacancy = 'Vacancy',
  HVAC = 'HVAC',
  HVACTypeID = 'HVACTypeID',
  RoofTypeID = 'RoofTypeID',
  HasSolar = 'HasSolar',
  BookValue = 'BookValue',
  BookValueDate = 'BookValueDate',
  IsADAAccessible = 'IsADAAccessible',
  ConstructionStartDate = 'ConstructionStartDate',
  EstCompletionDate = 'EstCompletionDate',
  ActualCompletionDate = 'ActualCompletionDate',
  ActualCompletion = 'ActualCompletion',
  IncludeinAnalytics = 'IncludeinAnalytics',
  InternalComments = 'InternalComments',
  CurrentTitle = 'CurrentTitle',
  TitleReferenceDate = 'TitleReferenceDate',
  GRESBScoreMin = 'GRESBScoreMin',
  GRESBScoreMax = 'GRESBScoreMax',
  EnergyStarRatingName = 'EnergyStarRatingName',
  TypicalFloorSizeSourceID = 'TypicalFloorSizeSourceID',
  ContributedSourceComments = 'ContributedSourceComments',
  TIAllowance = 'TIAllowance',
  NoOfUnits = 'NoOfUnits',
}

export const CheckboxFields = ['IsOwnerOccupied', 'IsADAAccessible', 'IncludeinAnalytics', 'HasSolar'];
