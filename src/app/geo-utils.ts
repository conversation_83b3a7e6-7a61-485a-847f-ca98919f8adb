export function sanitizePolygonString(
  footprint: string | null | undefined
): string {
  if (!footprint) return "";
  const trimmedFootprint = footprint.trim();
  if (trimmedFootprint.startsWith("MULTIPOLYGON")) {
    return trimmedFootprint;
  }
  if (trimmedFootprint.startsWith("POLYGON")) {
    return trimmedFootprint.replace(/POLYGON\s*\(\(/, "").replace(/\)\)$/, "");
  }
  return trimmedFootprint;
}
