export interface MultiPolygon {
    specificUse?: number;
    minFloor?: number;
    maxFloor?: number;
    floorCount?: number;
    floorSize?: number;
    shape?: string;
    BuildingFootPrintID?: number;
    localBuildingFootPrintID?: string;
    description?: string;
    additionalUse?: number;
    mainSpecificUseTypeId?: number;
    additionalSpecificUseTypeId?: number;
    isAdditionalUse?: boolean;
    ModifiedByName?: string;
    ModifiedDate?: string;
}

export enum ConstructTypeStatus {
    Retired = 'Retired',
    InUse = 'In Use',
}
