import { MediaDTO } from '../api-client';

export class MediaType {
    MediaTypeID: number;
    MediaTypeName: string;
    IsActive: boolean;
    CreatedDate: any;
    MediaTypeEnum: MediaDTO.MediaTypeIDEnum;
}
export class MediaSubType {
    MediaSubTypeID: number;
    MediaSubTypeName: string;
    IsActive: boolean;
    MediaSubTypeEnum: MediaDTO.MediaSubTypeIDEnum;
}
export class MediaSource {
    MediaSourceID: number;
    MediaSourceName: string;
    IsActive: boolean;
    MediaSourceEnum: MediaDTO.MediaSourceIDEnum;
}
