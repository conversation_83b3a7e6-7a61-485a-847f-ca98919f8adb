import { PropertyDetailsDTO } from '../../app/api-client';
import { AddressDto } from './address-dto';
import { LocationDto } from './location-dto';
import { RollupObject } from './rollupObject';

export class PropertyDetails implements PropertyDetailsDTO {
  CreatedBy?: number = undefined;
  CreatedDate?: string = undefined;
  ModifiedBy?: number = undefined;
  ModifiedDate?: string = undefined;
  PropertyID?: number = undefined;
  PropertyName?: string = undefined;
  ParentPropertyID?: number = undefined;
  IsActive?: boolean = true;
  YearBuilt?: number = undefined;
  YearRenovated?: number = undefined;
  Floors?: number = undefined;
  ConstructionStatusID?: PropertyDetailsDTO.ConstructionStatusIDEnum = undefined;
  ConstructionTypeID?: PropertyDetailsDTO.ConstructionTypeIDEnum = undefined;
  HvacTypeID?: PropertyDetailsDTO.HvacTypeIDEnum = undefined;
  SprinklerTypeID?: PropertyDetailsDTO.SprinklerTypeIDEnum = undefined;
  CondoTypeID?: PropertyDetailsDTO.CondoTypeIDEnum = undefined;
  UseTypeID?: number = undefined;
  UseTypeName?: string = undefined;
  SpecificUseID?: number = undefined;
  SpecificUseName?: string = undefined;
  EstimatedCompletionDate?: string = undefined;
  IsADAAccessible?: boolean = false;
  IsVented?: boolean = false;
  IsOwnerOccupied?: boolean = false;
  ClassTypeID?: PropertyDetailsDTO.ClassTypeIDEnum = undefined;
  TenancyTypeID?: PropertyDetailsDTO.TenancyTypeIDEnum = undefined;
  IsEnergyStar?: boolean = false;
  MixedUseAllocation?: string = undefined;
  BuildingWebsite?: string = undefined;
  BuildingComments?: string = undefined;
  MetroId?: number = undefined;
  MarketId?: number = undefined;
  SubMarketID?: number = undefined;
  GovernmentInterestID?: PropertyDetailsDTO.GovernmentInterestIDEnum = undefined;
  HasSolar?: boolean = false;
  TrafficCount?: number = undefined;
  EnergyStarRatingID?: PropertyDetailsDTO.EnergyStarRatingIDEnum = undefined;
  WaterStarRatingID?: PropertyDetailsDTO.WaterStarRatingIDEnum = undefined;
  GreenStarRatingID?: PropertyDetailsDTO.GreenStarRatingIDEnum = undefined;
  CurrentTitle?: string = undefined;
  TIAllowance?: number = undefined;
  ActualCompletion?: string = undefined;
  TitleReferenceDate?: string = undefined;
  LandUse?: number = undefined;
  LastReviewedBy?: number = undefined;
  LastReviewedDate?: string = undefined;
  ConstructionStartDate?: string = undefined;
  BookValue?: number = undefined;
  BookValueDate?: string = undefined;
  ParcelInfo?: string = undefined;
  MainPhotoUrl?: string = undefined;
  Zoning?: string = undefined;
  UseAddressAsPropertyName?: boolean = true;
  Amenities?: string = undefined;
  PassengerElevators?: number = undefined;
  ParkingElevators?: number = undefined;
  FreighElevators?: number = undefined;
  DockHigh?: number = undefined;
  Truckwell?: number = undefined;
  YardPaved?: boolean = false;
  PowerComments?: string = undefined;
  UtilityComments?: string = undefined;
  PropertyComments?: string = undefined;
  NoOfUnits?: number = undefined;
  Lifts?: boolean = false;
  LiftsCount?: number = undefined;
  PowerType?: PropertyDetailsDTO.PowerTypeEnum = undefined;
  Vacancy?: number = undefined;
  BayWidth?: number = undefined;
  BayDepth?: number = undefined;
  Depth?: number = undefined;
  Width?: number = undefined;
  IncludeInAnalytics?: boolean = false;
  OfficeAC?: PropertyDetailsDTO.OfficeACEnum = undefined;
  LotSizeSourceID?: PropertyDetailsDTO.LotSizeSourceIDEnum = undefined;
  ZoningClassID?: PropertyDetailsDTO.ZoningClassIDEnum = undefined;
  ZoningCode?: string = undefined;
  PotentialZoningID?: PropertyDetailsDTO.PotentialZoningIDEnum = undefined;
  SurroundingLandUse?: PropertyDetailsDTO.SurroundingLandUseEnum = undefined;
  RailServed?: boolean = false;
  IsFloodPlain?: boolean = false;
  SmallestFloor?: number = undefined;
  LargestFloor?: number = undefined;
  RoofTypeID?: PropertyDetailsDTO.RoofTypeIDEnum = undefined;
  HasYardFenced?: boolean = false;
  HasYardUnfenced?: boolean = false;
  BuildSpecStatusID?: PropertyDetailsDTO.BuildSpecStatusIDEnum = undefined;
  LegalDescription?: string = undefined;
  InternalComments?: string = undefined;
  ParkingSpaces?: number = undefined;
  HasSprinkler?: boolean = false;
  SizeSourceID?: PropertyDetailsDTO.SizeSourceIDEnum = undefined;
  HasPortAccess?: boolean = false;
  HasYard?: boolean = false;
  HasResCoveredParking?: boolean = false;
  ParkingRatio?: string = undefined;
  EarthquakeZoneID?: number = undefined;
  GeoscapePropertyID?: string = undefined;
  CounsilTaxID?: string = undefined;
  ValuerGeneralID?: string = undefined;
  ClearHeightMin?: number = undefined;
  ClearHeightMax?: number = undefined;
  Volts?: number = undefined;
  Phase?: number = undefined;
  Amps?: number = undefined;
  RetailFrontage?: number = undefined;
  TypicalFloorSize?: number = undefined;
  HardstandArea?: number = undefined;
  HardstandAreaSourceID?: PropertyDetailsDTO.SizeSourceIDEnum = undefined;
  ContributedGBASizeSourceID?: PropertyDetailsDTO.SizeSourceIDEnum = undefined;
  Mezzanine?: boolean = false;
  Awnings?: boolean = false;
  AwningsCount?: number = undefined;
  BuildingSize?: number = undefined;
  MinFloorSize?: number = undefined;
  MaxFloorSize?: number = undefined;
  ResearchTypeName?: string = undefined;
  TrueOwners?: string = undefined;
  RecordedOwners?: string = undefined;
  IsSkipped?: boolean = false;
  IsMultiplePolygonsNeeded?: boolean = false;
  NeedsResearchComments?: string = undefined;
  HasNoBuildingFootprints?: boolean = false;
  HasNoExistingParcelInTileLayer?: boolean = false;
  MasterPropertyId?: number = undefined;
  PropertyCreatedByName?: string = undefined;
  PropertyModifiedByName?: string = undefined;
  PropertyLastReviewedByName?: string = undefined;
  ContributedSourceComments?: string = undefined;
  GbasizeSource?: string = undefined;
  NRASizeSourceID?: PropertyDetailsDTO.NRASizeSourceIDEnum = undefined;
  CondoUnit?: string = undefined;
  NoOfOfficeFloor?: number = undefined;
  OccupancyPercent?: number = undefined;
  OccupiedPercentage?: number = undefined;
  GRESBScore?: number = undefined;
  GRESBScoreMin?: number = undefined;
  GRESBScoreMax?: number = undefined;
  EstCompletionDate?: string = undefined;
  Complex?: string = undefined;
  LGA?: string = undefined;
  GradeLevelDriveIn?: number = undefined;
  ReservedParkingSpaces?: number = undefined;
  ReservedParkingSpacesRatePerMonth?: number = undefined;
  HasReservedParkingSpaces?: boolean = false;
  UnreservedParkingSpaces?: number = undefined;
  UnreservedParkingSpacesRatePerMonth?: number = undefined;
  HasUnreservedParkingSpaces?: boolean = false;
  TotalAnchorSF?: number = undefined;
  HVAC?: boolean = false;
  IsCraneServed?: boolean = false;
  Anchors?: number = undefined;
  GLASizeSourceID?: PropertyDetailsDTO.SizeSourceIDEnum = undefined;
  GLARSizeSourceID?: PropertyDetailsDTO.SizeSourceIDEnum = undefined;
  LotSizeAcres?: number = undefined;
  RetailSF?: number = undefined;
  OfficeSF?: number = undefined;
  BuildingSF?: number = undefined;
  LotSizeSF?: number = undefined;
  TotalSaleSizeSF?: number = undefined;
  ContributedGBA_SF?: number = undefined;
  GLA_SF?: number = undefined;
  GLAR_SF?: number = undefined;
  Mezzanine_Size_SF?: number = undefined;
  Awnings_Size_SF?: number = undefined;
  LotSizeAc?: number = undefined;
  NLA_SF?: number = undefined;
  NLAac?: number = undefined;
  ContributedGBA_SM?: number = undefined;
  GLA?: number = undefined;
  GLAR?: number = undefined;
  PropertyResearchTypeID?: number = undefined;
  TypicalFloorSizeSourceID?: PropertyDetailsDTO.TypicalFloorSizeSourceIDEnum = undefined;
  OfficeHVAC?: PropertyDetailsDTO.OfficeHVACEnum = undefined;
  IsReviewed?: boolean = false;
  FeatureIDs?: string = undefined;
  PropertyAuditStatusID?: number;
  Address: AddressDto = new AddressDto();
  Location: LocationDto = new LocationDto();
  RollupObject: RollupObject = new RollupObject()

  constructor(init?: Partial<PropertyDetails>) {
    Object.assign(this, init);
  }
}
