import { PropertySearchRequestDTO, PropertySearchResponseBaseDTO } from "../api-client";

export interface PropertySearchDTO extends PropertySearchRequestDTO {
    OffsetValue: number;
    StartingIndex: number;
    FrontEndStartingIndex: number;
    FrontEndOffsetValue: number;
    LastReviewedDateMaxFormatted?: any;
    LastReviewedDateMinFormatted?: any;
    Zipcode?: string;
    Pids?: string;
}

export interface PropertySearchList extends PropertySearchResponseBaseDTO {
    IsSelected?: boolean;
}
