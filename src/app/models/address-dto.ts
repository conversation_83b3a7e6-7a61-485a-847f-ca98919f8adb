import { AddressDTO } from '../../app/api-client';
export class AddressDto implements AddressDTO {
  AddressId?: number = undefined;
  AddressTypeId?: AddressDTO.AddressTypeIdEnum = undefined;
  AddressStreetNumber?: string = undefined;
  AddressStreetName?: string = undefined;
  AddressText?: string = undefined;
  ZipCode?: string = undefined;
  ParentTableId?: AddressDTO.ParentTableIdEnum = undefined;
  Sequence?: number = undefined;
  StreetSuffix1?: number = undefined;
  StreetSuffix2?: number = undefined;
  Zip4?: string = undefined;
  FloorNumber?: string = undefined;
  Address1?: string = undefined;
  Address2?: string = undefined;
  StreetNumberMin?: string = undefined;
  StreetNumberMax?: string = undefined;
  StreetPrefix1?: AddressDTO.StreetPrefix1Enum = undefined;
  StreetPrefix2?: AddressDTO.StreetPrefix2Enum = undefined;
  BuildingNumber?: string = undefined;
  ComplexName?: string = undefined;
  PrimaryStreet?: string = undefined;
  PrimaryAccess?: string = undefined;
  PrimaryTrafficCount?: string = undefined;
  PrimaryTrafficCountDate?: string = undefined;
  PrimaryFrontage?: string = undefined;
  SecondaryStreet?: string = undefined;
  SecondaryAccess?: string = undefined;
  SecondaryTrafficCount?: string = undefined;
  SecondaryFrontage?: string = undefined;
  CityID?: number = undefined;
  StateID?: number = undefined;
  CountyID?: number = undefined;
  CountryID?: number = undefined;
  EastWestStreet?: string = undefined;
  NorthSouthStreet?: string = undefined;
  Quadrant?: AddressDTO.QuadrantEnum = undefined;
  PartOfComplex?: AddressDTO.PartOfComplexEnum = undefined;
  SecTrafficCntDate?: string = undefined;
  AddressType?: boolean = false;

  constructor(init?: Partial<AddressDTO>) {
    Object.assign(this, init);
  }
}