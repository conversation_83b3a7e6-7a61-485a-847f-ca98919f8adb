import { ChildFreeHoldsAdditionalUsesDTO, PropertyMasterRollupObject } from "../api-client";
export class RollupObject implements PropertyMasterRollupObject {
  DockHigh: number = 0;
  Truckwell: number = 0;
  ParkingSpaces: number = 0;
  PassengerElevators: number = 0;
  ParkingElevators: number = 0;
  RetailFrontage: number = 0;
  FreighElevators: number = 0;
  ParkingRatio: number = 0;
  AwningsCount: number = 0;
  TrafficCount: number = 0;
  RailServed: boolean = false;
  HasPortAccess: boolean = false;
  IsOwnerOccupied: boolean = false;
  HasResCoveredParking: boolean = false;
  IsVented: boolean = false;
  IsADAAccessible: boolean = false;
  HasYardFenced: boolean = false;
  HasYardUnfenced: boolean = false;
  YardPaved: boolean = false;
  HasYard: boolean = false;
  HasSolar: boolean = false;
  IncludeInAnalytics: boolean = false;
  Mezzanine: boolean = false;
  Awnings: boolean = false;
  Lifts: boolean = false;
  SpecificUseName: string = '';
  TenancyTypeID: string = '';
  ClassTypeID: string = '';
  ConstructionTypeID: string = '';
  ConstructionStatusID: string = '';
  Volts: string = '';
  Amps: string = '';
  SprinklerTypeID: string = '';
  HvacTypeID: string = '';
  GovernmentInterestID: string = '';
  BuildingWebsite: string = '';
  ZoningCode: string = '';
  PowerType: string = '';
  SizeSourceID: string = '';
  LotSizeSourceID: string = '';
  ContributedGBASizeSourceID: string = '';
  YearBuilt: string = '';
  YearRenovated: string = '';
  WaterStarRatingID: string = '';
  EnergyStarRatingID: string = '';
  GreenStarRatingID: string = '';
  SmallestFloor: string = '';
  LargestFloor: string = '';
  TypicalFloorSize: string = '';
  TiAllowance: string = '';
  ClearHeightMin: string = '';
  ClearHeightMax: string = '';
  Vacancy: string = '';
  BookValue: string = '';
  BookValueDate: string = '';
  HardstandArea: string = '';
  ConstructionStartDate: string = '';
  ActualCompletion: string = '';
  TitleReferenceDate: string = '';
  PropertyUse: string = '';
  UseTypeID: number = 0;
  MainPhotoUrl: string = '';
  ChildFreeHoldsAdditionalUses: ChildFreeHoldsAdditionalUsesDTO[] = [];
  BuildSpecStatusID: string = '';
  Floors: string = '';
  RoofTypeID: string = '';
  CurrentTitle: string = '';
  HasReservedParkingSpaces: boolean = false;
  HasSprinkler: boolean = false;
  LiftsCount: number = 0;
  Phase: string = '';
  HardstandAreaSourceID: string = '';
  NoOfUnits: number = 0;
  BuildingSF: number = 0;
  BuildingSizeSM: number = 0;
  GradeLevelDriveIn: number = 0;
  Anchors: number = 0;
  NoOfAnchor: number = 0;
  TotalAnchorSF: number = 0;
  RetailFrontageM: number = 0;
  ReservedParkingSpaces: number = 0;
  UnreservedParkingSpaces: number = 0;
  HasUnreservedParkingSpaces: boolean = false;
  HVAC: boolean = false;
  AmenitiesType: string = '';
  FeatureIDs: string = '';
  GLASizeSourceID: string = '';
  GLARSizeSourceID: string = '';
  OfficeHVAC: string = '';
  SmallestFloorSM: string = '';
  LargestFloorSM: string = '';
  NoOfOfficeFloor: string = '';
  OfficeSF: string = '';
  OfficeSM: string = '';
  TypicalFloorSizeSM: string = '';
  ClearHeightMinM: string = '';
  ClearHeightMaxM: string = '';
  ReservedParkingSpacesRatePerMonth: string = '';
  UnreservedParkingSpacesRatePerMonth: string = '';
  ContributedGBA_SF: string = '';
  ContributedGBA_SM: string = '';
  GRESBScoreMin: string = '';
  GRESBScoreMax: string = '';
  Mezzanine_Size_SF: string = '';
  Mezzanine_Size_SM: string = '';
  GLA_SF: string = '';
  GLA_SM: string = '';
  GLAR_SF: string = '';
  GLAR_SM: string = '';
  Awnings_Size_SF: string = '';
  Awnings_Size_SM: string = '';
  EstCompletionDate: string = '';
  ClassTypeName: string = '';
  TenancyName: string = '';
  CondoTypeName: string = '';
  BldgSizeSourceName: string = '';
  EnergyStarRatingName: string = '';
  WaterStarRatingName: string = '';
  GreenStarRatingName: string = '';
  ConstructionTypeName: string = '';
  ConstructionStatusName: string = '';
  HVACTypeName: string = '';
  SprinklerTypeName: string = '';
  BuildSpecStatusName: string = '';
  RoofTypeName: string = '';
  IsCraneServed: boolean = false;
  GovernmentInterestName: string = '';
  PowerTypeName: string = '';
  TypicalFloorSizeSourceID: string = '';
  HardstandAreaSourceName: string = '';
  NRASizeSourceID: string = '';
  NRASizeSourceName: string = '';
  ContributedGBASizeSourceName: string = '';
  GLASizeSourceName: string = '';
  GLARSizeSourceName: string = '';

  constructor(init?: Partial<PropertyMasterRollupObject>) {
    Object.assign(this, init);
  }
}
