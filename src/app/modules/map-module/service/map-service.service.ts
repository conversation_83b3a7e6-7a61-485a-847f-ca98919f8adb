import { Injectable, NgZone } from '@angular/core';

//Imports from deck.gl
import { BitmapLayer } from '@deck.gl/layers';
import { MVTLayer, TileLayer } from '@deck.gl/geo-layers';

import { MapOptions } from '../models/MapOptions';
import * as MapEnum from '../models/MapEnum';
import { LatLng } from '../models/LatLng';
import { FormattedAddress } from '../models/FormattedAddress';
import { MapBound } from '../models/mapBound';
import { BuildingKeyNameMapping, LayerInfoCardColor, LayerInfoCardHeading, Overlays, ParcelKeyNameMapping, ZoningColorData, ZoomLevels, TileLayerColorCodes, BuildingTileKeys } from '../../../enumerations/overlays';
import { environment } from '../../../../environments/environment';
import { wktToGeoJSON } from "@terraformer/wkt";
import { SharedDataService } from '../../../services/shareddata.service';
import { NotificationService } from '../../notification/service/notification.service';
import { azureMap } from '../../../constants';
import { MarkerClusterer } from "@googlemaps/markerclusterer";

declare var google: any;

@Injectable()
export class MapService {
  private _zone: NgZone;
  azureMapSatteliteTile: any = {}
  buildingsMVTTiles: any = {};
  zoningMVTTiles: any = {};
  postalCodeMVTTiles: any = {};
  notStrataparcelsMVTTiles: any = {};
  strataParcelsMVTTiles: any = {};
  selectedLayers: any = [];
  layers: any = [];
  postal_code: string = '';

  constructor(zone: NgZone, private _sharedDataService: SharedDataService, private _notificationService: NotificationService) {
    this._zone = zone;

  }
  // #Region Private functions

  private getPositionValue(position: MapEnum.GoogleMapControlPosition): any {
    let googlePosition = google.maps.ControlPosition.TOP_RIGHT;
    switch (position) {
      case MapEnum.GoogleMapControlPosition.Top_Center:
        googlePosition = google.maps.ControlPosition.TOP_CENTER;
        break;
      case MapEnum.GoogleMapControlPosition.Top_Left:
        googlePosition = google.maps.ControlPosition.TOP_LEFT;
        break
      case MapEnum.GoogleMapControlPosition.Top_Right:
        googlePosition = google.maps.ControlPosition.TOP_RIGHT;
        break;
      case MapEnum.GoogleMapControlPosition.LEFT_Top:
        googlePosition = google.maps.ControlPosition.LEFT_TOP;
        break;
      case MapEnum.GoogleMapControlPosition.Right_Top:
        googlePosition = google.maps.ControlPosition.RIGHT_TOP;
        break;
      case MapEnum.GoogleMapControlPosition.Left_Center:
        googlePosition = google.maps.ControlPosition.LEFT_CENTER;
        break;
      case MapEnum.GoogleMapControlPosition.Right_Center:
        googlePosition = google.maps.ControlPosition.RIGHT_CENTER;
        break;
      case MapEnum.GoogleMapControlPosition.Left_Bottom:
        googlePosition = google.maps.ControlPosition.LEFT_BOTTOM;
        break;
      case MapEnum.GoogleMapControlPosition.Right_Bottom:
        googlePosition = google.maps.ControlPosition.RIGHT_BOTTOM;
        break;
      case MapEnum.GoogleMapControlPosition.Bottom_Center:
        googlePosition = google.maps.ControlPosition.BOTTOM_CENTER;
        break;
      case MapEnum.GoogleMapControlPosition.Bottom_Left:
        googlePosition = google.maps.ControlPosition.BOTTOM_LEFT;
        break;
      case MapEnum.GoogleMapControlPosition.Bottom_Right:
        googlePosition = google.maps.ControlPosition.BOTTOM_RIGHT;
        break;
      default:
        googlePosition = google.maps.ControlPosition.TOP_RIGHT;
        break;
    }
    return googlePosition;
  }

  private getFeatureText(feature: MapEnum.MapFeatures) {
    let featureText = '';
    switch (feature) {
      case MapEnum.MapFeatures.Administrative_Country:
        featureText = 'administrative.country';
        break;
      case MapEnum.MapFeatures.Administrative_LandParcel:
        featureText = 'administrative.land_parcel';
        break;
      case MapEnum.MapFeatures.Administrative_Locality:
        featureText = 'administrative.locality';
        break;
      case MapEnum.MapFeatures.Administrative_Neighborhood:
        featureText = 'administrative.neighborhood';
        break;
      case MapEnum.MapFeatures.Administrative_Province:
        featureText = 'administrative.province';
        break;
      case MapEnum.MapFeatures.ManMadeLandscape:
        featureText = 'administrative.province';
        break;
      case MapEnum.MapFeatures.NaturalLandscape_Landcover:
        featureText = 'landscape.natural.landcover';
        break;
      case MapEnum.MapFeatures.NaturalLandscape_Terrain:
        featureText = 'landscape.natural.terrain';
        break;
      case MapEnum.MapFeatures.AttractionPin:
        featureText = 'poi.attraction';
        break;
      case MapEnum.MapFeatures.BusinessPin:
        featureText = 'poi.business';
        break;
      case MapEnum.MapFeatures.GovernmentPin:
        featureText = 'poi.government';
        break;
      case MapEnum.MapFeatures.MedicalInstitutionPin:
        featureText = 'poi.medical';
        break;
      case MapEnum.MapFeatures.ParkPin:
        featureText = 'poi.park';
        break;
      case MapEnum.MapFeatures.PlaceOfWorkshipPin:
        featureText = 'poi.place_of_worship';
        break;
      case MapEnum.MapFeatures.ScoolPin:
        featureText = 'poi.school';
        break;
      case MapEnum.MapFeatures.SportsComplexPin:
        featureText = 'poi.sports_complex';
        break;
      case MapEnum.MapFeatures.ArterialRoad:
        featureText = 'road.arterial';
        break;
      case MapEnum.MapFeatures.HighwayRoad:
        featureText = 'road.highway';
        break;
      case MapEnum.MapFeatures.ControlledAccessHighwayRoad:
        featureText = 'road.highway.controlled_access';
        break;
      case MapEnum.MapFeatures.LocalRoad:
        featureText = 'road.local';
        break;
      case MapEnum.MapFeatures.LineTransit:
        featureText = 'transit.line';
        break;
      case MapEnum.MapFeatures.AirportStation:
        featureText = 'transit.station.airport';
        break;
      case MapEnum.MapFeatures.BusStation:
        featureText = 'transit.station.bus';
        break;
      case MapEnum.MapFeatures.RailwayStation:
        featureText = 'transit.station.rail';
        break;
      case MapEnum.MapFeatures.Water:
        featureText = 'water';
        break;
    }
    return featureText;
  };

  private displayInfoWindow(map: any, marker: any, event: any, content: any, isAutoPan: boolean = false,
    previousMarker, previousHoverMarker, ourOverlay) {
    const infowindow = new google.maps.InfoWindow({ content: content, disableAutoPan: !isAutoPan });
    const bounds = new google.maps.LatLngBounds();
    if (!!marker) {
      const lat = marker.data.Latitude;
      const lng = marker.data.Longitude;
      infowindow.setOptions({ pixelOffset: this.getInfowindowOffset(map, marker, lat, lng, ourOverlay) });
    }
    infowindow.open(map, marker);

    infowindow.open(map, marker);
    google.maps.event.addListener(infowindow, 'closeclick', (eventClose) => {
      let markerIcon;
    });
    return infowindow;
  }
  getInfowindowOffset = function (map, marker, lat, lng, ourOverlay) {
    // Settings
    const iwWidth = 350; // InfoWindow width
    let iwHeight = 125; // InfoWindow Height
    let xOffset = 0;
    let yOffset = 0;
    const location = ourOverlay.getProjection().fromLatLngToContainerPixel(marker.position);
    // Get Edges of map in pixels: Sout West corner and North East corner
    const swp = ourOverlay.getProjection().fromLatLngToContainerPixel(map.getBounds().getSouthWest());
    const nep = ourOverlay.getProjection().fromLatLngToContainerPixel(map.getBounds().getNorthEast());

    // Horizontal Adjustment
    if (location.x < iwWidth / 2) {
      xOffset = iwWidth / 2 - location.x;
    } else if (location.x > nep.x - iwWidth / 2) {
      xOffset = (nep.x - iwWidth / 2) - location.x;
    }

    // Vertical Adjustment
    if (location.y < iwHeight) {
      yOffset = location.y + iwHeight - (location.y - nep.y);
    } else {
      iwHeight = 10;
      yOffset = location.y + iwHeight - (location.y - nep.y);
    }
    return new google.maps.Size(xOffset, yOffset);
  };

  private setDisplay(map, place) {
    if (!map)
      return;
    let bounds = new google.maps.LatLngBounds();
    if (place.geometry.viewport) {
      bounds.union(place.geometry.viewport);
    } else {
      bounds.extend(place.geometry.location);
    }
    map.fitBounds(bounds);
  };

  private getBoundPropertyObject(bounds: any): MapBound {
    var boundProperties: MapBound = new MapBound();
    if (!!bounds) {
      boundProperties.Center = bounds.getCenter();
      boundProperties.SouthWest = this.getLatLngObject(bounds.getSouthWest());
      boundProperties.NorthEast = this.getLatLngObject(bounds.getNorthEast());
    }
    return boundProperties;
  };

  getLatLngObject(latlng): LatLng {
    let obj = new LatLng();
    obj.Latitude = latlng.lat();
    obj.Longitude = latlng.lng();
    return obj;
  };

  private getStyleObj(feature: string) {
    return ({
      featureType: feature,
      elementType: "labels",
      stylers: [{ visibility: "off" }]
    });
  }

  private getMapFeatures(features: MapEnum.MapFeatures[]) {
    let styles = [];

    for (const feature of features) {
      styles.push(this.getStyleObj(this.getFeatureText(feature)));
    }
    return styles;
  }

  private generateMapOptionsForGoogle(mapOptions: MapOptions): any {
    let result: any = {};
    result.center = this.GetLatLng(mapOptions.CenterLat, mapOptions.CenterLng);
    result.mapId = mapOptions.MapId;
    result.draggable = mapOptions.IsDraggable;
    if (mapOptions.FullscreenControl != null) {
      result.fullscreenControl = mapOptions.FullscreenControl;
    }
    result.mapTypeId = this.GetMapTypeId(mapOptions.MapType);
    result.zoom = mapOptions.ZoomLevel;
    result.maxZoom = mapOptions.MaxZoom;
    result.minZoom = mapOptions.MinZoom;
    if (!mapOptions.RequireCtrlToZoom) {
      result.gestureHandling = 'greedy';
    } else {
      result.gestureHandling = 'cooperative';
    }
    return result;
  }

  private getAzureMapTileType(map:any) {
    const currentMapType = map.getMapTypeId();
    if (currentMapType === google.maps.MapTypeId.SATELLITE) {
      return 'a'; // 'a' for aerial
    } else if(currentMapType === google.maps.MapTypeId.HYBRID) {
      return 'h'; // 'h' for hybrid
    } else if(currentMapType === google.maps.MapTypeId.ROADMAP){
      return 'r'; // 'r' for road
    } else {
      return 't'; // 't' for terrain
    }
  }
  
  private getAzureMapOverlay(map:any, layerType: 'a' | 'h' | 'r' | 't' = 'h'){
    switch (layerType){
      case 'a':
        return new google.maps.ImageMapType({
          getTileUrl: (coord, zoom) => {
            const tileUrl = zoom === azureMap.MaxZoom.Satellite ? environment.AzureMapProxyURL : this._sharedDataService.AzureMapURL
            return `${tileUrl}?api-version=${environment.AzureMapApiVersion}&tilesetId=${azureMap.TilesetIDs.AzureSatelliteMap}&zoom=${zoom}&x=${coord.x}&y=${coord.y}&subscription-key=${environment.AzureMapApiKey}`
          },
          isPng: true,
          alt: azureMap.TilesName.AzureSatelliteMap,
          name: azureMap.TilesName.AzureSatelliteMap,
          maxZoom: map.maxZoom,
          minZoom: map.minZoom
        });
      case 'h':
        const featureOpts = [
          {
            elementType: 'geometry',
            stylers: [{ visibility: 'off' }] // Hide all map features (background, land, etc.)
          },
          {
            featureType: 'all',
            elementType: 'labels.text.fill',
            stylers: [{ color: '#ffffff' }] // Set label text color to white for all labels
          },
          {
            featureType: 'all',
            elementType: 'labels.text.stroke',
            stylers: [{ color: '#000000' }] // Set label text stroke to black for better visibility
          },
          {
            featureType: 'road', // Hide road labels
            elementType: 'labels',
            stylers: [{ visibility: 'off' }] // Turn off road labels
          },
          {
            featureType: 'poi', // Hide points of interest (e.g., parks, schools, etc.)
            elementType: 'labels',
            stylers: [{ visibility: 'off' }] // Turn off labels for points of interest
          },
          {
            featureType: 'transit', // Hide transit labels
            elementType: 'labels',
            stylers: [{ visibility: 'off' }] // Turn off labels for transit
          },
          {
            featureType: 'transit.line', // Show transit lines (e.g., ferries, trains) 
            elementType: 'geometry',
            stylers: [{ visibility: 'on' }] // Make transit lines visible
          },
        ];
        
        return new google.maps.StyledMapType(featureOpts, {
          name: azureMap.TilesName.AzureHybridMapLayer
        });
      case 'r':
        return new google.maps.ImageMapType({
          getTileUrl: (coord, zoom) => {
      
            return `${this._sharedDataService.AzureMapURL}?api-version=${environment.AzureMapApiVersion}&tilesetId=${azureMap.TilesetIDs.AzureRoadMap}&zoom=${zoom}&x=${coord.x}&y=${coord.y}&subscription-key=${environment.AzureMapApiKey}&tileSize=512`
          },
          isPng: true,
          alt: azureMap.TilesName.AzureRoadMap,
          name: azureMap.TilesName.AzureRoadMap,
          maxZoom: map.maxZoom,
          minZoom: map.minZoom
        });
      case 't':
        return new google.maps.ImageMapType({
          getTileUrl: (coord, zoom) => {
      
            return `${this._sharedDataService.AzureMapURL}?api-version=${environment.AzureMapApiVersion}&tilesetId=${azureMap.TilesetIDs.AzureTerrainMapLayer}&zoom=${zoom}&x=${coord.x}&y=${coord.y}&subscription-key=${environment.AzureMapApiKey}&tileSize=512`
          },
          isPng: true,
          alt: azureMap.TilesName.AzureTerrainMapLayer,
          name: azureMap.TilesName.AzureTerrainMapLayer,
          maxZoom: map.maxZoom,
          minZoom: map.minZoom
        });
    }
  }

  private setMaxZoomLabel(map:any, maxZoom:number | null){
    if(maxZoom && this.GetMapZoomLevel(map) > maxZoom) {
      this._notificationService.ShowWarningMessage(`Azure Map supports a maximum zoom level of ${maxZoom}`);
    }
    map.setOptions({maxZoom:maxZoom})
  }

  private getMapOverlayIndex(map: any, overLayName: string) {
    const overlayMapArray = map.overlayMapTypes.getArray();
    return overlayMapArray.findIndex((ele) => ele.name === overLayName);
  }

  private getAzureMapDeckLayer(map:any) {
    return new TileLayer({
      id: Overlays.AzureSatelliteMap,
      data: `${environment.AzureMapProxyURL}?api-version=${environment.AzureMapApiVersion}&tilesetId=${azureMap.TilesetIDs.AzureSatelliteMap}&zoom={z}&x={x}&y={y}&subscription-key=${environment.AzureMapApiKey}`,
      maxZoom: 19,
      renderSubLayers: props => {
        const {boundingBox} = props.tile;
        return new BitmapLayer(props, {
          data: null,
          image: props.data,
          bounds: [boundingBox[0][0], boundingBox[0][1], boundingBox[1][0], boundingBox[1][1]]
        });
      },
      pickable: true,
      onTileError: (error:any) => {
        this.handleTileLoadError(error,map);
      },
    })
  }

  private handleTileLoadError(error:any, map:any) {
    if (error.message && error.message.includes("Failed to fetch")) {
      // Handle the specific error here
      this._sharedDataService.currentZoomLevel = this.GetMapZoomLevel(map);
      this.SetMapZoomLevel(map,azureMap.MaxZoom.Satellite);
      this._notificationService.ShowInfoMessage(`Fetching tiles at zoom level ${azureMap.MaxZoom.Satellite}`);
    } else {
      // Handle other types of errors if necessary
      this._notificationService.ShowWarningMessage("An error occurred while loading tiles. Please try to zoom out and try again.");
    }
  }

  // #endRegion Private Functions

  addAzureMapOverlay(map:any, checkedList?:string[], deckOverlay?:any){
    // Add Azure Maps overlay
    const tileType = this.getAzureMapTileType(map);
    this.setAzureMapOverlay(map, tileType, checkedList, deckOverlay);
  }

  setAzureMapOverlay(map:any, layerType: 'a' | 'h' | 'r' | 't' = 'h', checkedList?:string[], deckOverlay?:any){
    switch(layerType){
      case 'h':
        if(!map.IsHybridMapLayer || map.IsAzureMapDeckLayer) {
          this.setAzureMapOverlay(map, 'a', checkedList, deckOverlay)
          if(this.GetMapZoomLevel(map) <= azureMap.MaxZoom.Satellite){
            map.overlayMapTypes.insertAt(1, this.getAzureMapOverlay(map,'h'));
            map.IsHybridMapLayer = true;
          }
        }
        break;
      case 'a':
        this.removeAzureMapOverlayIfExists(map,'r') // Remove RoadMap View if Exists
          if(this.GetMapZoomLevel(map) > azureMap.MaxZoom.Satellite) {
            this.addAzureMapDeckLayer(map,checkedList, deckOverlay);
          } else {
            if(!map.IsAzureMap){
              map.overlayMapTypes.insertAt(0, this.getAzureMapOverlay(map,'a'));
              map.IsAzureMap = true;
            }
            setTimeout(()=>{
              this.removeAzureMapDeckLayer(map,'a', checkedList, deckOverlay);
            },300)
          }
        this.removeAzureMapOverlayIfExists(map, 'h'); // Remove Hybrid Layer if Exists
        break;
      case 'r':
        if(map.IsAzureMapDeckLayer){
          this.removeAzureMapDeckLayer(map,'a', checkedList, deckOverlay);
        } else {
          this.removeAzureMapOverlayIfExists(map, 'a', checkedList, deckOverlay); // Remove Arial View if Exists
        }
        if(!map.IsAzureRoadMap) {
          this._sharedDataService.maxZoom = map.maxZoom;
          this.setMaxZoomLabel(map, azureMap.MaxZoom.RoadMap);
          map.overlayMapTypes.insertAt(0, this.getAzureMapOverlay(map,'r'));
          map.IsAzureRoadMap = true;
        }
        this.removeAzureMapOverlayIfExists(map, 't'); // Remove Terrain View if Exists
        break;
      case 't':
        if(!map.IsAzureTerrainMapLayer){
          this.setAzureMapOverlay(map, 'r');
          map.overlayMapTypes.insertAt(1, this.getAzureMapOverlay(map,'t'));
          map.IsAzureTerrainMapLayer = true;
        }
        break;
    }
  }

  clearAzureMapOverlay(map:any, checkedList?:string[], deckOverlay?:any){
    ['a', 'h', 'r', 't'].forEach((layerType : 'a' | 'h' | 'r' | 't') => {
      if(map.IsAzureMapDeckLayer) {
        this.removeAzureMapDeckLayer(map,layerType, checkedList, deckOverlay);
      } else {
        this.removeAzureMapOverlayIfExists(map, layerType)
      }
      
    })
  }

  removeAzureMapOverlayIfExists(map:any, layerType: 'a' | 'h' | 'r' | 't'  = 'a', checkedList?:string[], deckOverlay?:any) {
    if(map.overlayMapTypes.getLength() > 0){
      switch(layerType){
        case 'a':
          // Remove the first overlay map type (Azure Maps)
          this.removeAzureMapOverlayIfExists(map, 'h');
          if (map.IsAzureMap || map.IsAzureMapDeckLayer) {
            const indexOfAzureMap = this.getMapOverlayIndex(map, azureMap.TilesName.AzureSatelliteMap)
            if(indexOfAzureMap !== -1) {
              map.overlayMapTypes.removeAt(indexOfAzureMap);
              map.IsAzureMap = false;
            }
          }
          break;
        case 'h':
          if (map.IsHybridMapLayer) {
            const indexOfAzureMap = this.getMapOverlayIndex(map, azureMap.TilesName.AzureHybridMapLayer)
            if(indexOfAzureMap !== -1) {
              map.overlayMapTypes.removeAt(indexOfAzureMap);
            }
            map.IsHybridMapLayer = false;
          }
          break
        case 'r':
          this.removeAzureMapOverlayIfExists(map, 't');
          if (map.IsAzureRoadMap) {
            const indexOfAzureMap = this.getMapOverlayIndex(map, azureMap.TilesName.AzureRoadMap);
            if(indexOfAzureMap !== -1) {
              const googleMapMaxZoom = this._sharedDataService.maxZoom
              this.setMaxZoomLabel(map,googleMapMaxZoom);
              this._sharedDataService.maxZoom = null;
              map.overlayMapTypes.removeAt(indexOfAzureMap);
            }
            map.IsAzureRoadMap = false;
          }
          break;
        case 't':
          if (map.IsAzureTerrainMapLayer) {
            const indexOfAzureMap = this.getMapOverlayIndex(map, azureMap.TilesName.AzureTerrainMapLayer);
            if(indexOfAzureMap !== -1) {
              map.overlayMapTypes.removeAt(indexOfAzureMap);
            }
            map.IsAzureTerrainMapLayer = false;
          }
          break;
      }

    }
  }

  toggleAzureMapOverlay(map:any, checkedList?:string[], deckOverlay?:any) {
    if(this._sharedDataService.IsAzureMapOn){
      const layerType = this.getAzureMapTileType(map);
      switch(layerType){
        case 'a':
          this.removeAzureMapOverlayIfExists(map, 'h');
          this.removeAzureMapOverlayIfExists(map, 'r');
          break;
        case 'h':
          break;
        case 'r':
          this.removeAzureMapOverlayIfExists(map, 't');
          this.removeAzureMapOverlayIfExists(map, 'a', checkedList, deckOverlay);
          break;
        case 't':
          break;
      }
      this.addAzureMapOverlay(map, checkedList, deckOverlay); // Add Azure Map overlay based on Map Type
    }
  }

  addAzureMapDeckLayer(map:any, checkedList:string[], deckOverlay:any) {
    const tileType = this.getAzureMapTileType(map);
    switch (tileType) {
      case 'a':
      case 'h':
        if(!map.IsAzureMapDeckLayer) {
          if(!checkedList.includes(Overlays.AzureSatelliteMap)){
            checkedList.push(Overlays.AzureSatelliteMap);
          }
          this.addOverlay(checkedList, map, deckOverlay, 'AzureMapSatellite');
          map.IsAzureMapDeckLayer = true;
        }
        setTimeout(()=>{
          this.removeAzureMapOverlayIfExists(map, 'a', checkedList, deckOverlay);
        },500);
    }
  }

  removeAzureMapDeckLayer(map:any,layerType: 'a' | 'h' | 'r' | 't'  = 'a', checkedList:string[], deckOverlay:any){
    switch(layerType) {
      case 'a':
      case 'h':
      case 'r':
      case 't':
        if(map.IsAzureMapDeckLayer) {
          const indexOfAzureMap = checkedList.findIndex((id:string) => id === Overlays.AzureSatelliteMap)
          if(indexOfAzureMap !== -1) {
            checkedList.splice(indexOfAzureMap,1)
            this.addOverlay(checkedList, map, deckOverlay, 'AzureMapSatelliteMap');
            map.IsAzureMapDeckLayer = false;
          }
        }
    }
  }



  CreateMap(mapOptions: MapOptions): any {
    let mapOpts = this.generateMapOptionsForGoogle(mapOptions);
    mapOpts.styles = this.getMapFeatures(mapOptions.FeaturesToHide);
    mapOpts.mapTypeId = google.maps.MapTypeId.HYBRID;
    let map = new google.maps.Map(document.getElementById(mapOpts.mapId), mapOpts);
    map.styles = mapOpts.styles;
    this.TiltMap(map, 0);
    return map;
  };

  OnMapZoomChange(map, callback: (boundProperties: MapBound) => any) {
    google.maps.event.addListener(map, 'zoom_changed', () => {
      let bounds = map.getBounds();
      let boundProps = this.getBoundPropertyObject(bounds);
      if (!!callback)
        this._zone.run(() => callback(boundProps));
    });
  };

  OnMapViewPortChangedOnce(map: any, callback: (boundProperties: MapBound) => any): void {
    google.maps.event.addListenerOnce(map, 'idle', () => {
      var bounds = map.getBounds();
      var boundProps = this.getBoundPropertyObject(bounds);
      if (!!callback)
        this._zone.run(() => callback(boundProps));
    });
  };

  GetMapZoomLevel(map: any): number {
    if(!!map)
    return map.getZoom();
  }

  SetMapZoomLevel(map: any, zoomLevel: number): any {
    map.setZoom(zoomLevel);
    return map;
  }

  ClearViewPortChangeListener(map: any): void {
    google.maps.event.clearListeners(map, 'idle');
  };

  TiltMap(map: any, degree: number = 0): void {
    map.setTilt(degree);
  };

  MapRefresh(map: any) {
    setTimeout(() => {
      google.maps.event.trigger(map, "resize");
    }, 1000);
  };

  MapReload(map: any) {
    google.maps.event.trigger(map, "idle");
  };


  SetCenter(map: any, latitude: number, longitude: number) {
    setTimeout(() => {
      map.setCenter(this.GetLatLng(latitude, longitude));
    }, 1000);
  };

  SetIDCenter(map: any, latitude: number, longitude: number) {
    map.setCenter(this.GetLatLng(latitude, longitude));
  };

  MoveMapCenter(map, latitude, longitude, requireAnimation = false) {
    if (requireAnimation)
      this.animatedMapMovement(map, latitude, longitude, 10, 0);
    else
      map.panTo(this.GetLatLng(latitude, longitude));
  };

  GetLatLng(latitude: number, longitude: number): any {
    return new google.maps.LatLng(latitude, longitude);
  };

  GetCenter(map) {
    if (!map)
      return null;
    return this.getLatLngObject(map.getCenter());
  };

  OnMapClick(map, callback: (latlng: LatLng) => any) {
    google.maps.event.addListener(map, 'click', (event) => {
      this._zone.run(() => callback(this.getLatLngObject(event.latLng)));
    });
  }

  PlaceMarker(map: any, latitude: number, longitude: number, isDraggable: boolean = false, icon: string = ''): any {
    const pin = new google.maps.marker.PinElement({
      scale: 1,
    });
    const markerIcon = document.createElement('img');
    markerIcon.src = icon;
    let markerIconTodisplay;
    if (icon) {
      markerIconTodisplay = markerIcon;
    } else {
      markerIconTodisplay = pin.element;

    }

    let marker = new google.maps.marker.AdvancedMarkerElement({
      position: this.GetLatLng(latitude, longitude),
      map: map,
      gmpDraggable: isDraggable,
      content: markerIconTodisplay
    });
    return marker;
  }

  ClearMarkers(markers: Array<any>) {
    if (!markers)
      markers = [];
    for (var index in markers) {
      markers[index].map=null;
      markers[index] = null;
    }
    markers = [];
    return markers;
  };

  ClearSingleMarker(marker: any) {
    if (!!marker) {
      marker.map=null;
    }
  }

  SetMarkerAnimation(marker: any, animation: MapEnum.Animation) {
    switch (animation) {
      case MapEnum.Animation.Bounce:
        if (marker.content.classList.contains("drop")) {
          marker.content.classList.replace("drop", "bounce");

        } else {
          marker.content.classList.add("bounce");

        }
        break;
      case MapEnum.Animation.Drop:
        if (marker.content.classList.contains("bounce")) {
          marker.content.classList.replace("bounce", "drop");

        } else {
          marker.content.classList.add("drop")

        }
        break;
    }
    return marker;
  };

  ClearMarkerAnimation(marker) {
    if (!!marker.content.classList.length) {
      marker.content.classList.forEach(element => {
        marker.content.classList.remove(element);
      });
    };
  };

  OnMarkerDragBegin(marker: any, callback: () => any) {
    google.maps.event.addListener(marker, 'dragstart', (event) => {
      callback();
    });
  };

  OnMarkerDragEnd(marker, callback: (latlng: LatLng) => any) {
    google.maps.event.addListener(marker, 'dragend', (event) => {
      callback(this.getLatLngObject(event.latLng));
    });
  };

  MoveMarker(marker, latitude, longitude) {
    var latlng = this.GetLatLng(latitude, longitude);
    marker.position = latlng;
  };

  GetClusterImg({ fillColor, label }) {
    const width = this.GetWidthOfCluster(+label);
    const fontSize = this.GetFontSizeOfCluster(+label);
    const svgStr = `<svg width="${width}" height="${width}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-100 -100 200 200">
                      <defs>
                          <g id="a" transform="rotate(45)">
                              <path d="M0 47A47 47 0 0 0 47 0L62 0A62 62 0 0 1 0 62Z" fill-opacity="0.7"/>
                              <path d="M0 67A67 67 0 0 0 67 0L81 0A81 81 0 0 1 0 81Z" fill-opacity="0.4"/>
                              <path d="M0 86A86 86 0 0 0 86 0L100 0A100 100 0 0 1 0 100Z" fill-opacity="0.1"/>
                          </g>
                      </defs>
                      <g fill="${fillColor}">
                          <circle r="42"/>
                          <g>
                              <use xlink:href="#a"/>
                          </g>
                          <g transform="rotate(120)">
                              <use xlink:href="#a"/>
                          </g>
                          <g transform="rotate(240)">
                              <use xlink:href="#a"/>
                          </g>
                      </g>

                      <text x="0" y="12" text-anchor="middle" fill="black" font-size="${fontSize}" font-weight="bold" font-family=" Arial, Helvetica, sans-serif">${label}</text>
                  </svg>`
    
    // Get svg as an HTML element
    const div = document.createElement('div');
    div.innerHTML = svgStr;
    const svgElement = div.firstChild as Element;

    return svgElement
  }

  GetWidthOfCluster(count) {
    if (count <= 100) {
      return 60
    } else if (count <= 1000) {
      return 70
    } else {
      return 80
    }
  }

  GetFontSizeOfCluster(count) {
    if (count <= 100) {
      return 40
    } else if (count <= 1000) {
      return 30
    } else {
      return 27
    }
  }
  
  GetColorOfCluster(count) {
    if (count <= 100) {
      return '#f8c126'
    } else if (count <= 1000) {
      return '#f4271c'
    } else if (count <= 2000) {
      return '#f721ec'
    } else {
      return '#b50cfd'
    }
  }
  createCluster(map, markers, maxZoom, minimumClusterSize) {
    let markerCluster = new MarkerClusterer({map, markers, renderer: {
      render: ({ count, position }) => {
        const fillColor = this.GetColorOfCluster(count);
        const label = count + ''
    const content = this.GetClusterImg({ fillColor, label })
    
        return new google.maps.marker.AdvancedMarkerElement({
          position,
          content,
          zIndex: 1000000 + count,
        })
      }
    }})
    return markerCluster;
  }

  clearCluster(cluster) {
    if (cluster != null && cluster.map != null)
      cluster.clearMarkers();
    return cluster;
  }

  onClusterClick(markerCluster, callback: (cluster: any) => any) {
    let isClusterClick: boolean = false;
    google.maps.event.addListener(markerCluster, 'clusterclick', (cluster) => {
      callback(cluster);
    });
  }


  ShowInfoWindow(map: any, marker: any, event: any, infoWindowContent: any, isAutoPan: boolean = false,
    previousMarker = null, previousHoverMarker = null, ourOverlay = null) {
    const infowindow = this.displayInfoWindow(map, marker, event, infoWindowContent,
      isAutoPan, previousMarker, previousHoverMarker, ourOverlay);
    return infowindow;
  };

  ClearInfoWindow(infoWindow: any) {
    infoWindow.close();
  };

  OnMarkerClick(marker, callback: (event: any, marker: any, latlng: LatLng) => any) {
    google.maps.event.addListener(marker, 'click', (event) => {
      if (!!callback) {
        this._zone.run(() => callback(event, marker, this.getLatLngObject(event.latLng)));
      }
    });
  };

  OnMarkerHover(marker: any, callback: (event: any, marker: any, latlng: LatLng) => any) {
    marker.content.addEventListener('mouseover', (event) => {
      const latlLng = {
        lat: () => marker.position.lat,
        lng: () => marker.position.lng
      };

      this._zone.run(() => callback(event, marker,this.getLatLngObject(latlLng)));
    });
  };

  OnMarkerMouseOut(marker: any, callback: (event: any, marker: any) => any) {
    google.maps.event.addListener(marker, 'mouseout', (event) => {
      this._zone.run(() => callback(event, marker));
    });
  };

  GetMapTypeId(mapType: MapEnum.MapType) {
    switch (mapType) {
      case MapEnum.MapType.Hybrid:
        return google.maps.MapTypeId.HYBRID;
      case MapEnum.MapType.Roadmap:
        return google.maps.MapTypeId.ROADMAP;
      case MapEnum.MapType.Satellite:
        return google.maps.MapTypeId.SATELLITE;
      case MapEnum.MapType.Terrain:
        return google.maps.MapTypeId.TERRAIN;
      default:
        return google.maps.MapTypeId.ROADMAP;
    }
  };

  AddController(map: any, controllerId: string, position: MapEnum.GoogleMapControlPosition): any {
    let googlePosition = this.getPositionValue(position);
    var control = document.getElementById(controllerId);
    map.controls[googlePosition].push(control);
    return map;
  };

  AddSearchBox(map: any, searchBoxId: string, position: MapEnum.GoogleMapControlPosition, addController: boolean = true, onPlaceChanged: (place: any) => any) {
    let input = document.getElementById(searchBoxId);
    if (addController)
      this.AddController(map, searchBoxId, position);
    let autocompleteOptions = {};
    let autocomplete = new google.maps.places.Autocomplete(input, autocompleteOptions);
    autocomplete.bindTo('bounds', map);

    google.maps.event.addListener(autocomplete, 'place_changed', () => {
      var place = autocomplete.getPlace();
      this.setDisplay(map, place);
      if (!!onPlaceChanged)
        this._zone.run(() => onPlaceChanged(place));
    });

  }

  GetLocationDetailsFromLatLng(latitude, longitude, callback: (places: any) => any) {
    var latlng = this.GetLatLng(latitude, longitude);
    var geocoder = new google.maps.Geocoder();
    geocoder.geocode({ 'latLng': latlng }, (results, status) => {
      if (status == google.maps.GeocoderStatus.OK) {
        if (!!callback) {
          this._zone.run(() => callback(results));
        }
      }
    });
  };

  GetFormattedObjectFromGooglePlaceAddressComponent(addressComponents): FormattedAddress {
    let resultantData: FormattedAddress = new FormattedAddress();
    for (const eachAddress of addressComponents) {
      if (!!eachAddress.types && eachAddress.types.length > 0) {
        if (eachAddress.types[0] == 'street_number') {
          if (!resultantData.MinimumStreetNumber) {
            resultantData.StreetNumber = eachAddress.long_name;
            if (eachAddress.long_name.indexOf('-') > -1) {
              resultantData.MinimumStreetNumber = eachAddress.long_name.split('-')[0];
              resultantData.MaximumStreetNumber = eachAddress.long_name.split('-')[1];
            }
            else {
              resultantData.MinimumStreetNumber = resultantData.MaximumStreetNumber = eachAddress.long_name;
            }
          }
        }
        else if (eachAddress.types[0] == 'route') {
          if (!resultantData.StreetNameShort) {
            resultantData.StreetNameLong = eachAddress.long_name;
            resultantData.StreetNameShort = eachAddress.short_name;
          }
        }
        else if (eachAddress.types[0] == 'locality') {
          if (!resultantData.City)
            resultantData.City = eachAddress.long_name;
        }
        else if (eachAddress.types[0] == 'administrative_area_level_2') {
          if (!resultantData.CountyName)
            resultantData.CountyName = eachAddress.long_name;
        }
        else if (eachAddress.types[0] == 'administrative_area_level_1') {
          if (!resultantData.StateCode) {
            resultantData.StateName = eachAddress.long_name;
            resultantData.StateCode = eachAddress.short_name;
          }
        }
        else if (eachAddress.types[0] == 'country') {
          if (!resultantData.CountryCode) {
            resultantData.CountryName = eachAddress.long_name;
            resultantData.CountryCode = eachAddress.short_name;
          }
        }
        else if (eachAddress.types[0] == 'postal_code') {
          if (!resultantData.ZipCode)
            resultantData.ZipCode = eachAddress.long_name;
        }
      }
    }
    return resultantData;
  };

  // Map Drawing Management Begins

  AddDrawMenu(map: any, position: MapEnum.GoogleMapControlPosition, ...drawingModes: MapEnum.DrawMode[]): any {
    let drawModes = new Set<string>();
    for (let mode of drawingModes) {
      switch (mode) {
        case MapEnum.DrawMode.Circle:
          drawModes.add('circle');
          break;
        case MapEnum.DrawMode.Polygon:
          drawModes.add('polygon');
          break;
        case MapEnum.DrawMode.Polyline:
          drawModes.add('polyline');
          break;
        case MapEnum.DrawMode.Rectangle:
          drawModes.add('rectangle');
          break;
      }
    }
    var drawingManager = new google.maps.drawing.DrawingManager({
      drawingControl: true,
      drawingControlOptions: {
        position: this.getPositionValue(position),
        drawingModes: Array.from(drawModes.values())
      },
      circleOptions: {
        fillOpacity: 0.2,
        strokeWeight: 5,
        clickable: false,
        editable: true,
        zIndex: 1
      }
    });
    drawingManager.setMap(map);
    return drawingManager;
  };

  OnMapOverlayComplete(drawingManager: any, mode: MapEnum.DrawMode, callback: (event: any) => any) {
    google.maps.event.addListener(drawingManager, 'overlaycomplete', (event) => {
      switch (mode) {
        case MapEnum.DrawMode.Circle:
          if (event.type == 'circle') {
            callback(event.overlay);
          }
          break;
        case MapEnum.DrawMode.Polygon:
          if (event.type == 'polygon') {
            callback(event.overlay);
          }
          break;
        case MapEnum.DrawMode.Polyline:
          if (event.type == 'polyline') {
            callback(event.overlay);
          }
          break;
        case MapEnum.DrawMode.Rectangle:
          if (event.type == 'rectangle') {
            callback(event.overlay);
          }
          break;
      }
    });
  }

  FitMapToPolygon(map: any, polygon: any) {
    var bounds = new google.maps.LatLngBounds();
    for (let latLng of polygon.latLngs.getArray()[0].getArray()) {
      var ll = this.GetLatLng(latLng.lat(), latLng.lng());
      bounds.extend(ll);
    }
    map.fitBounds(bounds);
  };

  OnDrawingModeChange(drawingManager: any, callback: () => any) {
    google.maps.event.addListener(drawingManager, "drawingmode_changed", callback);
  }

  DrawShapeOnMap(map: any, latLngList: Array<LatLng>, polyLines: any, isFitMap: boolean = true, color: string = '#FF3333',
    isEditable = false, saveArea, savePolygon) {
    if (latLngList == null) {
      return null;
    }
    var bounds = new google.maps.LatLngBounds();
    if (!polyLines) {
      polyLines = [];
    }
    var poly = [];
    latLngList.forEach((LatLng) => {
      var ll = this.GetLatLng(LatLng.Latitude, LatLng.Longitude);
      poly.push(ll);
      bounds.extend(ll);
    });
    var polyObj = new google.maps.Polygon({
      path: poly,
      strokeColor: color,
      strokeOpacity: 1.0,
      strokeWeight: 3,
      editable: isEditable,
      suppressUndo: true,
      fillColor: 'transparent',
      draggable: isEditable,
    });
    polyObj.setMap(map);
    polyLines.push(polyObj);
    var originalPath = [];
    for (var i = 0; i < polyObj.getPath().getArray().length; i++) {
      originalPath.push(polyObj.getPath().getArray()[i]);
    }

    if (isEditable) {
      var newLatLng;
      var area = undefined;

      google.maps.event.addListener(polyObj.getPath(), 'set_at', () => {
        area = google.maps.geometry.spherical.computeArea(polyObj.getPath());
        saveArea(area);
        var latlongs = [];
        newLatLng = undefined;
        for (var i = 0; i < polyObj.getPath().getLength(); i++) {
          var latLng = polyObj.getPath().getAt(i);
          latlongs.push(`${latLng.lng()} ${latLng.lat()}`);
        }
        latlongs.push(latlongs[0]);
        newLatLng = latlongs.join(',').replace("'", '');
        savePolygon(newLatLng);
      });

      google.maps.event.addListener(polyObj.getPath(), 'insert_at', () => {
        area = google.maps.geometry.spherical.computeArea(polyObj.getPath());
        saveArea(area);
        var latlongs = [];
        newLatLng = undefined;
        for (var i = 0; i < polyObj.getPath().getLength(); i++) {
          var latLng = polyObj.getPath().getAt(i);
          latlongs.push(`${latLng.lng()} ${latLng.lat()}`);
        }
        newLatLng = latlongs.join(',').replace("'", '');
        savePolygon(newLatLng);
      });
    }

    if (isFitMap) {
      map.fitBounds(bounds);
    }

    return polyLines;
  };

  DrawPolygonOnMap = function (map: any, latLngList: Array<LatLng>, polyLines: any, isFitMap: boolean = true, color: string = '#FF3333', zIndex = 1000) {
    if (latLngList == null)
      return null;
    var bounds = new google.maps.LatLngBounds();
    if (!polyLines)
      polyLines = [];

    var poly = [];
    latLngList.forEach((LatLng) => {
      var ll = this.GetLatLng(LatLng.Latitude, LatLng.Longitude);
      poly.push(ll);
      bounds.extend(ll);
    });
    var polyObj = new google.maps.Polyline({
      path: poly,
      strokeColor: color,
      strokeOpacity: 1.0,
      strokeWeight: 3
    });
    polyObj.setMap(map);
    polyLines.push(polyObj);


    if (isFitMap)
      map.fitBounds(bounds);

    return polyLines;
  };

  ClearPolygons(draws) {
    if (draws != null) {
      draws.forEach(shape => {
        this.ClearPolygon(shape);
      });
    }
    draws = [];
    return draws;
  };

  ClearPolygon(polygon) {
    if (polygon != null)
      polygon.setMap(null);
  };

  // Map Drawing Management Ends

  // Fusion table layer addition begin

  // LoadFusionTableLayer(map: any, fusionTableId: string, geometryFieldName: string): any {
  //   var layer = new google.maps.FusionTablesLayer({
  //     query: {
  //       select: geometryFieldName,
  //       from: fusionTableId
  //     }
  //   });
  //   layer.setMap(map);
  //   return layer;
  // }

  // StyleFusionLayer(layer: any, polygonOptions: PolygonStyleOption, polylineOptions: PolygonStyleOption): any {
  //   let style: Array<any> = new Array<any>();
  //   let styleObj: any = {};
  //   if (polygonOptions) {
  //     styleObj.polygonOptions = polygonOptions;
  //   }
  //   if (polylineOptions) {
  //     styleObj.polylineOptions = polylineOptions;
  //   }
  //   style.push(styleObj);
  //   layer.set('styles', style);
  //   return layer;
  // }

  // ShowFusionTableLayer(map: any, layer: any): any {
  //   layer.setMap(map);
  //   return layer;
  // }

  // HideFusionTableLayer(map: any, layer: any): any {
  //   layer.setMap(null);
  //   return layer;
  // }

  // Fusion table layer addition end

  private animatedMapMovement(map, latitude, longitude, totalSteps, currentStep) {
    if (totalSteps == currentStep)
      return false;
    var center = this.GetCenter(map);
    var LatDiff = latitude - center.Latitude;
    var LngDiff = longitude - center.Longitude;
    LatDiff = LatDiff / totalSteps;
    LngDiff = LngDiff / totalSteps;
    center.Latitude += LatDiff;
    center.Longitude += LngDiff;
    setTimeout(() => {
      map.panTo(this.GetLatLng(center.Latitude, center.Longitude));
      currentStep++;
      this.animatedMapMovement(map, latitude, longitude, totalSteps, currentStep);
    }, 60);
  };

  LeftClick(map: any) {
    google.maps.event.trigger(map, 'leftclick');
  }

  DrawPolygon(map, drawingMode, position = undefined, color = '#D94825') {
    var drawingManager = new google.maps.drawing.DrawingManager({
      drawingControl: false,
      drawingControlOptions: {
        position: position || google.maps.ControlPosition.TOP_CENTER,
        drawingModes: [drawingMode]
      },

      polygonOptions: {
        fillOpacity: 0.2,
        strokeColor: color,
        strokeWeight: 5,
        zIndex: 1
      },
      circleOptions: {
        fillOpacity: 0.2,
        strokeWeight: 5,
        strokeColor: color,
        clickable: false,
        editable: true,
        zIndex: 1
      },
      polylineOptions: {
        fillOpacity: 0.2,
        strokeColor: color,
        strokeWeight: 5,
        zIndex: 1
      }
      
    });
  
    drawingManager.setMap(map);
    if (drawingMode == 'polygon') {
      drawingManager.setDrawingMode(null);
    }
    else if (drawingMode == 'polyline') {
      drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYLINE);
    }
    else if (drawingMode == 'circle')
      drawingManager.setDrawingMode(google.maps.drawing.OverlayType.CIRCLE);
    return drawingManager;


  }
  StreetView(latlng: any, mapId: any): any {
    const currentLatLng = { lat: latlng.Latitude, lng: latlng.Longitude };
    const sv = new google.maps.StreetViewService();
    sv.getPanoramaByLocation(currentLatLng, 50, function (data, status) {
      if (status == 'OK') {
        //google has a streetview image for this location, so attach it to the streetview div
        var panoramaOptions = {
          position:  {
            lat : latlng.Latitude,
            lng : latlng.Longitude
          },
          pano: data.location.pano,
      //    addressControl: true,
          navigationControl: true,
          navigationControlOptions: {
            style: google.maps.NavigationControlStyle.SMALL
          },
          enableCloseButton: true,
        };
        var myLatlng = new google.maps.LatLng(latlng.Latitude, latlng.Longitude);
        // Set the initial Street View camera to the center of the map
        sv.getPanorama({
          location: myLatlng
          , radius: 50
          , source: google.maps.StreetViewSource.OUTDOOR
        },function(data, status){
          if (status === google.maps.StreetViewStatus.OK) {
            var panorama = new google.maps.StreetViewPanorama(document.getElementById(mapId), panoramaOptions);

      //    function processSVData({ data }: any, status) {
          const location = data.location!;
          panorama.setPano(location.pano as string);
          panorama.setPov({
            heading: !!panorama.getPhotographerPov() ? panorama.getPhotographerPov().heading : 270,
            pitch: !!panorama.getPhotographerPov() ? panorama.getPhotographerPov().pitch : 0,
          });
          panorama.setVisible(true);
     //   }
          } else {
          
          }
      });
      }
      else {

      }
    });
    // var panorama = new google.maps.StreetViewPanorama(
    //   document.getElementById(mapId) as HTMLElement
    // );  
    // // Set the initial Street View camera to the center of the map
    // sv.getPanorama({ location: currentLatLng
    //                 , radius: 50 
    //                 ,  source: google.maps.StreetViewSource.OUTDOOR}).then(function processSVData({ data }: any) {
    //   const location = data.location!;    
    //   panorama.setPano(location.pano as string);
    //   panorama.setPov({
    //     heading: panorama.getPhotographerPov().heading,
    //     pitch: panorama.getPhotographerPov().pitch,
    //   });
    //   panorama.setVisible(true);
    // });

  }


  getTooltipContent = (properties, title, keyNameMapping,color) => {
    const displayKeys = Object.keys(keyNameMapping);
    const propertyStrings = displayKeys.map((key) => {
      const displayName = keyNameMapping[key];
      let value = properties[key];
      if (!isNaN(value) && typeof value === 'number') {
        if (value % 1 !== 0) {
          value = value.toFixed(3);
        }
      }
      if (value) {
        return `<div style="display:flex; white-space: wrap;width:150px;">${displayName}: ${value}</div>`;
      }
    });
    return `<div style="display:flex;background-color:white;flex-direction:column"><div style="display:flex;background-color:${color}; color: white;font-size:15px; padding: 5px;align-items:center;justify-content:center;text-align:center">${title}</div>
    <div style="display:flex;flex-direction:column;gap:2px;padding:5px;font-size:13px">${propertyStrings.join('')}</div></div>`;
  }
  
  getZoningFillColor = (zone: string) => {
    const zoneItem = ZoningColorData.find(zoneType => zoneType.zone === zone);
    return zoneItem ? zoneItem.rgb : [0, 0, 0, 0];
  }

  handleToolTipContent = (object, tooltipId) => {
    const tooltip = document.getElementById(tooltipId);
    if (tooltip) {
      let content = `<div></div>`;
      if (object && object.properties && object.properties.Parcel_No) {
        content = this.getTooltipContent(object.properties, LayerInfoCardHeading.Parcel, ParcelKeyNameMapping, LayerInfoCardColor.Parcel);
      }
      if (object && object.properties && BuildingTileKeys.BuildingFootPrintID in object.properties) {
        content = this.getTooltipContent(object.properties, LayerInfoCardHeading.Building, BuildingKeyNameMapping, LayerInfoCardColor.Building);
      }
      tooltip.innerHTML = content; // Update the content of picked tile Object
    }
  }

  addOverlay(checkedLayerListIds: any[], map, deckOverlay, tooltipId, inEditMode: boolean = false, saveParcelInfo: any = () => undefined): void {
    deckOverlay && deckOverlay.finalize();
    this.azureMapSatteliteTile = this.getAzureMapDeckLayer(map);
    this.postal_code = this._sharedDataService.searchPostalCode;
    this.zoningMVTTiles = new MVTLayer({
      id: Overlays.Zoning,
      data: `${environment.tilesBaseUrl}/zoning/{z}/{x}/{y}.pbf`,
      maxZoom: 19,
      getFillColor: (feature) => this.getZoningFillColor(feature.properties.General_Us),
      getLineColor: TileLayerColorCodes.zoning.lineColor,
      getLineWidth: TileLayerColorCodes.zoning.lineWidth,
    });
    this.strataParcelsMVTTiles = new MVTLayer({
      id: Overlays.Strata,
      data: `${environment.tilesBaseUrl}/strataParcels/{z}/{x}/{y}.pbf`,
      maxZoom: 19,
      getLineColor: TileLayerColorCodes.strata.lineColor,
      getFillColor: TileLayerColorCodes.strata.fillColor,
      getLineWidth: TileLayerColorCodes.strata.lineWidth,
      pickable: true,
      autoHighlight: !inEditMode,
      highlightColor: TileLayerColorCodes.strata.highlightColor,
      uniqueIdProperty: 'Parcel_No',
    });
    this.notStrataparcelsMVTTiles = new MVTLayer({
      id: Overlays.NotStrata,
      data: `${environment.tilesBaseUrl}/notStrataParcels/{z}/{x}/{y}.pbf`,
      maxZoom: 19,
      getLineColor: TileLayerColorCodes.notStrata.lineColor,
      getFillColor: TileLayerColorCodes.notStrata.fillColor,
      getLineWidth: TileLayerColorCodes.notStrata.lineWidth,
      pickable: true,
      autoHighlight: !inEditMode,
      highlightColor: TileLayerColorCodes.notStrata.highlightColor,
      uniqueIdProperty: 'Parcel_No',
    });
    this.buildingsMVTTiles = new MVTLayer({
      id: Overlays.Building,
      data: `${environment.tilesBaseUrl}/buildings/{z}/{x}/{y}.pbf`,
      maxZoom: 19,
      getLineColor: TileLayerColorCodes.building.lineColor,
      getFillColor: TileLayerColorCodes.building.fillColor,
      getLineWidth: TileLayerColorCodes.building.lineWidth,
      pickable: true,
    });
    this.postalCodeMVTTiles = new MVTLayer({
      id: Overlays.PostalCode,
      data: `${environment.tilesBaseUrl}/postalcode/{z}/{x}/{y}.pbf`,
      maxZoom: 17,
      getLineColor: feature => feature.properties.POSTALCODE === this.postal_code ? TileLayerColorCodes.postalCode.highlightLineColor : TileLayerColorCodes.postalCode.lineColor,
      getFillColor: feature => feature.properties.POSTALCODE === this.postal_code ? TileLayerColorCodes.postalCode.highlightColor : TileLayerColorCodes.postalCode.fillColor,
      getLineWidth: TileLayerColorCodes.postalCode.lineWidth,
      pickable: true,
    })
    this.layers = [this.azureMapSatteliteTile, this.zoningMVTTiles, this.notStrataparcelsMVTTiles, this.strataParcelsMVTTiles, this.buildingsMVTTiles, this.postalCodeMVTTiles];
    const isParcelLayerIncluded = checkedLayerListIds.includes(Overlays.Parcel);
    this.selectedLayers = this.layers.filter(layer => {
      const isLayerIncluded = checkedLayerListIds.includes(layer.id);
      return isLayerIncluded || (isParcelLayerIncluded && (layer.id === Overlays.Strata || layer.id === Overlays.NotStrata));
    });
    deckOverlay.setProps({
      layers: this.selectedLayers,
      pickable: true,
      onClick: ({ x, y, object }) => {
        if (map.getZoom() >= ZoomLevels.ZoomSeventeen && inEditMode) {
          this.handleToolTipContent(object, tooltipId);
        }
        const pickedLayerInfo = deckOverlay.pickMultipleObjects({
          x: x,
          y: y,
          radius: 1,
          layerIds: [Overlays.NotStrata, Overlays.Strata],
          depth: 1000,
        });
        let parcelInfo = pickedLayerInfo.filter(pickedObject => {
          let parcelObjectProperties = pickedObject.object.properties;
          return parcelObjectProperties && parcelObjectProperties.Parcel_No;
        });
        saveParcelInfo(pickedLayerInfo);
        this._sharedDataService.parcelInfoPickedFromTileLayer = parcelInfo;
      },

      onHover: ({ x, y, object }) => {
        if (map.getZoom() >= ZoomLevels.ZoomSeventeen && !inEditMode) {
          this.handleToolTipContent(object, tooltipId);
        }
      },
    });
    deckOverlay.setMap(map);
  }
  getLatLngListFromPolygon(polygonText) {
    const isMulitpolygon = polygonText.includes('MULTIPOLYGON');
    const geojson = wktToGeoJSON(polygonText);
    let latlngList: Array<LatLng> = new Array<LatLng>();
    const coordinates = isMulitpolygon ? geojson.coordinates.flat(2) : geojson.coordinates.flat(1)
    coordinates.forEach(ll => {
      let latlng = new LatLng();
      latlng.Latitude = parseFloat(ll[1]);
      latlng.Longitude = parseFloat(ll[0]);
      latlngList.push(latlng);
    });
    return latlngList;
  }

  splitMultiPolygonToPolygons(multiPolygonStr) {
    // Remove the "MULTIPOLYGON" prefix and extra parentheses
    const cleanStr = multiPolygonStr
      .replace("MULTIPOLYGON", "")
      .trim()
      .replace(/^\(\(\(/, "")
      .replace(/\)\)\)$/, "");

    // Split into individual polygon coordinate sets
    const polygons = cleanStr.split(")),((").map(polygonStr => {
      // Extract coordinates for the current polygon
      const coordinates = polygonStr.split(",").map(coordStr => {
        const [lng, lat] = coordStr.trim().split(" ").map(Number);
        return [lng, lat];
      });

      // Format the coordinates into a POLYGON string
      return `POLYGON((${coordinates.map(([lng, lat]) => `${lng} ${lat}`).join(",")}))`;
    });

    return polygons;
  }

  checkPinLocationWithFootprint(polygon: any, pinLocation: any) {
    if (polygon.includes('MULTIPOLYGON')) {
      const polygons = this.splitMultiPolygonToPolygons(polygon);
      return polygons.some((poly) => this.checkPinLocationWithFootprint(poly, pinLocation));
    } else {
      const shape = polygon.includes('POLYGON') ? polygon : `POLYGON((${polygon}))`;
      const latlLngs = this.getLatLngListFromPolygon(shape);
      const polyEvent = this.getPolygon(latlLngs);
      //Checks if the given pin location is inside the polygon
      return google.maps.geometry.poly.containsLocation(pinLocation, polyEvent);
    }
    
  }

  getPolygon(latLngList: Array<LatLng>) {
    var poly = [];
    latLngList.forEach((LatLng) => {
      var ll = this.GetLatLng(LatLng.Latitude, LatLng.Longitude);
      poly.push(ll);
    });
    var polyObj = new google.maps.Polygon({
      path: poly,
    });
    return polyObj;
  }

  DrawPolygonsOnMap(map: any, latLngList: Array<LatLng>, polyLines: any, isFitMap: boolean = true, color: string = '#FF3333',
    isEditable = false, saveUpdatedPolygons: any, clickable = false) {
    if (latLngList == null) {
      return null;
    }
    var bounds = new google.maps.LatLngBounds();
    if (!polyLines) {
      polyLines = [];
    }
    var poly = [];
    latLngList.forEach((LatLng) => {
      var ll = this.GetLatLng(LatLng.Latitude, LatLng.Longitude);
      poly.push(ll);
      bounds.extend(ll);
    });
    var polyObj = new google.maps.Polygon({
      path: poly,
      strokeColor: color,
      strokeOpacity: 1.0,
      strokeWeight: 3,
      editable: isEditable,
      suppressUndo: true,
      fillColor: 'transparent',
      draggable: isEditable,
      clickable
    });
    polyObj.setMap(map);
    polyLines.push(polyObj);
    var originalPath = [];
    for (var i = 0; i < polyObj.getPath().getArray().length; i++) {
      originalPath.push(polyObj.getPath().getArray()[i]);
    }

    if (isEditable) {
      google.maps.event.addListener(polyObj.getPath(), 'set_at', () => {
        saveUpdatedPolygons(polyObj)
      });
      google.maps.event.addListener(polyObj.getPath(), 'insert_at', () => {
        saveUpdatedPolygons(polyObj)
      });
    }
    if (isFitMap) {
      map.fitBounds(bounds);
    }
    return polyLines;
  };
}
