import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalComponent } from './component/modal.component';
import { AdditionalAddressModalComponent } from './modals/additional-address-modal/additional-address-modal.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../../modules/shared/shared.module';
import { FileUploadModalComponent } from './modals/file-upload-modal/file-upload-modal.component';
import { ImageViewerComponent } from './modals/image-viewer-modal/image-viewer-modal.component';
import { ImageViewerModule } from "ngx-image-viewer";
import { PropertyParcelModalComponent } from './modals/property-parcel-modal/property-parcel-modal.component';
import { AddNoteModalComponent } from './modals/add-note-modal/add-note-modal.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { BuildingSizeUtilityModalComponent } from './modals/building-size-utility-modal/building-size-utility-modal.component';
import { ExpressViewImageUploadModalComponent } from './modals/image-upload-modal/image-upload-modal.component';
import { ChangelogModalComponent } from './modals/changelog-modal/changelog-modal.component';
import { NgxCurrencyModule } from 'ngx-currency';
import { PropertyMapUtilityModalComponent } from './modals/property-map-utility-modal/property-map-utility-modal.component';
import { MapSwitchModule } from '../../pages/map-switch/map-switch.module';
import { MultiParcelConfirmationModalComponent } from './modals/multi-parcel-confirmation-modal/multi-parcel-confirmation-modal.component';
import { PropertyNameAndAddressComponent } from './modals/property-name-and-address/property-name-and-address.component';
import { FootprintPreviewModalComponent } from './modals/footprint-preview-modal/footprint-preview-modal.component';
import { WhatsNewModalComponent } from './modals/whats-new-modal/whats-new-modal.component';
import { ManualFootprintAddComponent } from './modals/manual-footprint-add/manual-footprint-add.component';
import { ContributedFieldsComponent } from '../../../app/components/common/contributed-fields/contributed-fields.component';

@NgModule({
  imports: [CommonModule, FormsModule, ReactiveFormsModule, SharedModule,ImageViewerModule, NgSelectModule, NgxCurrencyModule, MapSwitchModule],
  declarations: [
    ModalComponent,
    AdditionalAddressModalComponent,
    FileUploadModalComponent,
    ImageViewerComponent,
    PropertyParcelModalComponent,
    AddNoteModalComponent,
    BuildingSizeUtilityModalComponent,
    PropertyMapUtilityModalComponent,
    ExpressViewImageUploadModalComponent,
    ChangelogModalComponent,
    MultiParcelConfirmationModalComponent,
    PropertyNameAndAddressComponent,
    FootprintPreviewModalComponent,
    WhatsNewModalComponent,
    ManualFootprintAddComponent,
    ContributedFieldsComponent
  ],
  providers: [],
  exports: [
    ModalComponent,
    AdditionalAddressModalComponent,
    FileUploadModalComponent,
    ImageViewerComponent,
    PropertyParcelModalComponent,
    AddNoteModalComponent,
    BuildingSizeUtilityModalComponent,
    ExpressViewImageUploadModalComponent,
    ChangelogModalComponent,
    PropertyMapUtilityModalComponent,
    MultiParcelConfirmationModalComponent,
    PropertyNameAndAddressComponent,
    WhatsNewModalComponent,
    ManualFootprintAddComponent,
    ContributedFieldsComponent
  ]
})
export class ModalModule { }
