import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { NotesService } from '../../../../services/notes.service';
import { SharedDataService } from '../../../../services/shareddata.service';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
import { mapEditPropertyDTO } from '../../../../DTO/mapEditPropertyDTO';
import { CommonStrings } from '../../../../constants';
import { NotesRequestDTO, NotesResponseDTO } from '../../../../api-client';

@Component({
  selector: 'app-add-note-modal',
  templateUrl: './add-note-modal.component.html',
  styleUrls: ['./add-note-modal.component.scss']
})
export class AddNoteModalComponent implements OnInit {

  @Input() selectedNote: NotesResponseDTO;
  @Input() initialDetails: mapEditPropertyDTO;
  @Output() onClose = new EventEmitter();
  noteForm: FormGroup;
  notetypes: any;

  constructor(private _notesService: NotesService,
    private notificationService: NotificationService,
    private sharedService: SharedDataService) {}

  ngOnInit() {
    this.createForm();
    const lookup = this.sharedService.getLookupDropdowns();
    this.notetypes = lookup ?lookup['NoteTypeID'] : [];

  }

  createForm() {
    this.noteForm = new FormGroup({
      'NoteType': new FormControl(''),
      'Title': new FormControl(''),
      'Description': new FormControl('')
    });
  }

  saveNote() {
    if(this.noteForm.valid) {
      const reqBody: NotesRequestDTO = {
        NoteTypeId: this.selectedNote.NoteTypeId,
        NoteTitle: this.selectedNote.NoteTitle,
        NoteDescription: this.selectedNote.NoteDescription,
        ParentTableId: this.selectedNote.ParentTableId,
        ParentId: this.selectedNote.ParentId
      }
      const response_notes = this._notesService.SaveNote(reqBody, this.selectedNote.NoteId);
      response_notes.subscribe(result => {
        if (!result.error) {
          this.onClose.emit();
        } else {
          this.notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        const message = this.selectedNote.NoteId ? CommonStrings.ErrorMessages.FailedToUpdateNote : CommonStrings.ErrorMessages.FailedToSaveNote;
        this.notificationService.ShowErrorMessage(error?.error?.message ?? message);
      });
    }    
  }

  closeNote() {
    this.onClose.emit();
  }

}
