<div class="col-md-12">
  <form [formGroup]="propertyForm" class="form-align">
    <div class="form-group row">
      <div class="form-group row">
        <label class="col-md-4 form-control-label">Address Type
          <span class="mandatory">*</span>
        </label>
        <div class="col-md-8">
          <label class="radio-inline">
            <input type="radio" [value]="addressTypeValues.Address" formControlName="AddressType" name="AddressType" [(ngModel)]="additionalAddress.AddressType" data-testId="additional-address-address"> Address</label>
          <label class="radio-inline">
            <input type="radio" [value]="addressTypeValues.Intersection" formControlName="AddressType" name="AddressType" [(ngModel)]="additionalAddress.AddressType" data-testId="additional-address-intersection"> Intersection </label>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Street Number
            <span *ngIf="!propertyForm.controls['StreetNumberMin'].valid && !propertyForm.controls['StreetNumberMax'].valid " style="color:red;"> * </span>
          </label>
          <div class="col-sm-6">
            <div class="row">
              <label class="form-control-label" style="padding-left: 15px;">Min</label>
              <div class="col-md-12">
                <input type="text" formControlName="StreetNumberMin" class="form-control" [(ngModel)]="additionalAddress.StreetNumberMin" [ngClass]="{'error-field':(!propertyForm.controls['StreetNumberMin'].valid)}" maxLength="6"
                data-testId="additional-address-street-min">
              </div>
            </div>
          </div>
          <div class="col-sm-6">
            <div class="row">
              <label class="form-control-label" style="padding-left: 15px;">Max</label>
              <div class="col-md-12">
                <input type="text" formControlName="StreetNumberMax" class="form-control" [(ngModel)]="additionalAddress.StreetNumberMax" maxLength="6" data-testId="additional-address-street-max">
              </div>
            </div>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Direction</label>
          <div class="col-md-12">
            <span class="help-block">
              <ng-select formControlName="Direction" [items]="streetPrefixes" [virtualScroll]="true" bindLabel="PrefixName" bindValue="PrefixEnum" placeholder="--Select--" [(ngModel)]="additionalAddress.StreetPrefix1"
              data-testId="additional-adddress-direction"></ng-select>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">
            Street Name
            <span *ngIf="!propertyForm.controls['AddressStreetName'].valid" style="color:red;"> * </span>
          </label>
          <div class="col-md-12">
            <span class="help-block">
              <input type="text" class="form-control" formControlName="AddressStreetName" [(ngModel)]="additionalAddress.AddressStreetName" [ngClass]="{'error-field':(!propertyForm.controls['AddressStreetName'].valid )}" data-testId="additional-address-street-name">
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Suffix 1/ Suffix 2</label>
          <div class="col-md-6">
            <span class="help-block">
              <ng-select formControlName="streetSuffix1" [items]="streetSufixes" [virtualScroll]="true" bindLabel="Suffix" bindValue="StreetSuffix1" placeholder="--Select--" [(ngModel)]="additionalAddress.StreetSuffix1" data-testId="additional-address-suffix1"></ng-select>
            </span>
          </div>
          <div class="col-md-6">
            <span class="help-block">
              <ng-select formControlName="streetSuffix2" [items]="streetSufixes" [virtualScroll]="true" bindLabel="Suffix" bindValue="StreetSuffix1" placeholder="--Select--" [(ngModel)]="additionalAddress.StreetSuffix2" data-testId="additional-address-suffix2"></ng-select>
            </span>
          </div>
        </div>
      </div>
      <div class="col-md-4 address">
        <div class="heightAdjust">
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">City
            <span style="color:red;">*</span>
          </label>
          <div class="col-md-12">
            <span class="help-block">
              <ng-select formControlName="city" [items]="cities" [virtualScroll]="true" bindLabel="CityName" bindValue="CityID" placeholder="--Select--" [(ngModel)]="additionalAddress.CityID" [ngClass]="{'error-field':(!propertyForm.controls['city'].valid && !additionalAddress.CityID )}" data-testId="additional-address-city"></ng-select>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input"> Council </label>
          <div class="col-sm-12">
            <span class="help-block">
              <ng-select formControlName="county" [items]="counties" [virtualScroll]="true" bindLabel="CountyName" bindValue="CouncilID" placeholder="--Select--" [(ngModel)]="additionalAddress.CountyID" data-testId="additional-address-council"></ng-select>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Building #
            <span *ngIf="!propertyForm.controls['BuildingNumber'].valid" style="color:red;"> * </span>
          </label>
          <div class="col-sm-12">
            <input type="text" maxlength="45" class="form-control" formControlName="BuildingNumber" [(ngModel)]="additionalAddress.BuildingNumber" [ngClass]="{'error-field':(!propertyForm.controls['BuildingNumber'].valid )}" data-testId="additional-address-building-comments">
          </div>
          <div class="col-md-12"></div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">State
            <span style="color:red;">*</span>
          </label>
          <div class="col-sm-12">
            <span class="help-block">
              <ng-select formControlName="state" [items]="states" [virtualScroll]="true" bindLabel="StateName" bindValue="StateID" placeholder="--Select--" [(ngModel)]="additionalAddress.StateID" [ngClass]="{'error-field':(!propertyForm.controls['state'].valid )}" data-testId="additional-address-state"></ng-select>
            </span>
          </div>
        </div>
      </div>
      <!--right column -->
      <div class="col-md-4 address">
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Quadrant
            <span *ngIf="!propertyForm.controls['quadrant'].valid" style="color:red;"> * </span>
          </label>
          <div class="col-md-12">
            <ng-select formControlName="quadrant" [items]="quadrants" [virtualScroll]="true" bindLabel="QuadrantName" bindValue="QuadrantEnum" placeholder="--Select--" [(ngModel)]="additionalAddress.Quadrant" [ngClass]="{'error-field':(!propertyForm.controls['quadrant'].valid )}" data-testId="additional-address-quadrant"></ng-select>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">N/S Street
            <span *ngIf="!propertyForm.controls['NorthSouthStreet'].valid" style="color:red;"> * </span>
          </label>
          <div class="col-md-12">
            <input type="text" maxLength="100" class="form-control" formControlName="NorthSouthStreet" [(ngModel)]="additionalAddress.NorthSouthStreet" [ngClass]="{'error-field':(!propertyForm.controls['NorthSouthStreet'].valid )}" data-testId="additional-address-street-ns">
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">E/W Street
            <span *ngIf="!propertyForm.controls['EastWestStreet'].valid" style="color:red;"> * </span>
          </label>
          <div class="col-md-12">
            <input type="text" maxLength="100" class="form-control" formControlName="EastWestStreet" [(ngModel)]="additionalAddress.EastWestStreet" [ngClass]="{'error-field':(!propertyForm.controls['EastWestStreet'].valid )}" data-testId="additional-address-street-ew">
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-12 form-control-label" for="text-input">Postal Code
            <span style="color:red;">*</span>
          </label>
          <div class="col-sm-12">
            <input type="number" [disabled] formControlName="zipcode" class="form-control" [(ngModel)]="additionalAddress.ZipCode" value="{{additionalAddress.ZipCode}}" [ngClass]="{'error-field':(!propertyForm.controls['zipcode'].valid )}" data-testId="additional-address-postal-code">
          </div>
        </div>
      </div>
    </div>
    <button type="submit" class="btn btn-success" (click)="saveAdditionalAddress()" data-testId="additional-address-save">Save</button>
  </form>
</div>
