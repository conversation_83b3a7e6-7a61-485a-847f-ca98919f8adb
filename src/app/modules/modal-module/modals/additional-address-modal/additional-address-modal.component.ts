import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { AddressService } from '../../../../services/address.service';
import { AdditionalAddressDTO, AdditionalAddressRequestDTO, ApiResponseAdditionalAddressDTO, PropertyDetailsDTO } from '../../../../../app/api-client';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
import { CommonStrings } from '../../../../constants';

@Component({
  selector: 'app-additional-address-modal',
  templateUrl: './additional-address-modal.component.html',
  styleUrls: ['./additional-address-modal.component.scss']
})
export class AdditionalAddressModalComponent implements OnInit {
  propertyForm: FormGroup;
  streetPrefixes: any;
  streetSufixes: any;
  quadrants: any;
  states: any;
  CountryId: number;
  counties: any;
  cities: any;
  @Input() editAdditionalAddress: AdditionalAddressDTO;
  @Input() property: PropertyDetailsDTO;
  @Input() propertyLookup: any;
  additionalAddress: AdditionalAddressDTO;
  currentAddressID: number;
  @Output() onSaveAdditionalAddress: EventEmitter<AdditionalAddressDTO> = new EventEmitter<AdditionalAddressDTO>();
  @Output() onClose = new EventEmitter();
  selectedAdditionalAddressCopy: AdditionalAddressDTO;
  public addressTypeValues: any = { Address: false, Intersection: true };

  constructor(private addressService: AddressService, private notificationService: NotificationService) {}

  ngOnInit() {
    this.additionalAddress = {};
    this.CountryId = this.property.Address.CountryID;
    this.additionalAddress = this.editAdditionalAddress;
    this.selectedAdditionalAddressCopy = JSON.parse(JSON.stringify(this.editAdditionalAddress));
    this.createForm();
    this.populateDropDowns(() => {
    });

    this.loadStateList(this.property.Address.CountryID, () => {
      this.GetCity();
      this.GetCounty();
    });

    setTimeout(() => {
      this.prePopulateData();
    }, 400);

  }

  createForm() {
    this.propertyForm = new FormGroup({
      'StreetNumberMin': new FormControl('', Validators.required),
      'StreetNumberMax': new FormControl(''),
      'Direction': new FormControl(''),
      'AddressStreetName': new FormControl('', Validators.required),
      'streetSuffix1': new FormControl(''),
      'streetSuffix2': new FormControl(''),
      'zipcode': new FormControl(''),
      'state': new FormControl(''),
      'city': new FormControl(''),
      'county': new FormControl(''),
      'quadrant': new FormControl(''),
      'NorthSouthStreet': new FormControl(''),
      'EastWestStreet': new FormControl(''),
      'Latitude': new FormControl(''),
      'Longitude': new FormControl(''),
      'BuildingNumber': new FormControl(''),
      'AddressType': new FormControl('', Validators.required)
    });

    this.propertyForm?.get('AddressType')?.valueChanges?.subscribe(
      (AddressType) => {
        if (AddressType === this.addressTypeValues.Address) {
          this.propertyForm?.get('quadrant')?.clearValidators();
          this.propertyForm?.get('EastWestStreet')?.clearValidators();
          this.propertyForm?.get('NorthSouthStreet')?.clearValidators();

          this.propertyForm?.get('StreetNumberMin')?.setValidators([Validators.required]);
          this.propertyForm?.get('AddressStreetName')?.setValidators([Validators.required]);

        } else {
          this.propertyForm?.get('StreetNumberMin')?.clearValidators();
          this.propertyForm?.get('AddressStreetName')?.clearValidators();

          this.propertyForm?.get('quadrant')?.setValidators([Validators.required]);
          this.propertyForm?.get('EastWestStreet')?.setValidators([Validators.required]);
          this.propertyForm?.get('NorthSouthStreet')?.setValidators([Validators.required]);
        }
        this.propertyForm?.get('StreetNumberMin')?.updateValueAndValidity();
        this.propertyForm?.get('AddressStreetName')?.updateValueAndValidity();
        this.propertyForm?.get('quadrant')?.updateValueAndValidity();
        this.propertyForm?.get('EastWestStreet')?.updateValueAndValidity();
        this.propertyForm?.get('NorthSouthStreet')?.updateValueAndValidity();
      }
    )
  }

  private populateDropDowns(onDropdownsPopulated: () => void) {
    this.streetPrefixes = this.propertyLookup['PrefixID'];
    this.streetSufixes = this.propertyLookup['StreetSuffix1'];
    this.quadrants = this.propertyLookup['QuadrantID'];
    onDropdownsPopulated();
  }

  private loadStateList(countryId, onLoadingState: () => void) {
    this.states = (this.propertyLookup['StateID'] || []).filter(state => state.CountryID === countryId);
    onLoadingState();
  }

  GetCity() {
    this.cities = (this.propertyLookup['CityID'] || []).filter(city => city.StateID === this.property.Address.StateID);
  }

  GetCounty() {
    this.counties = (this.propertyLookup['CouncilID'] || []).filter(county => county.StateID === this.property.Address.StateID);
  }
  prePopulateData() {
    if (!this.additionalAddress?.AddressId) {
      const address = this.property?.Address || {};
      this.additionalAddress = {
        ...this.additionalAddress,
        CountryID: address.CountryID,
        StateID: address.StateID,
        CountyID: address.CountyID ,
        CityID: address.CityID,
        ZipCode: address.ZipCode,
        ParentId: this.property?.PropertyID,
        AddressType: false
      };
    }
  }


  saveAdditionalAddress() {
    if (this.propertyForm.valid)
    {
      const { AddressStreetName, AddressType, BuildingNumber, CityID, CountryID, CountyID, EastWestStreet, NorthSouthStreet, StreetPrefix1,
        StreetPrefix2, Quadrant, StateID, StreetNumberMax, StreetNumberMin, StreetSuffix1, StreetSuffix2, ZipCode, ParentId
      } = this.additionalAddress;
      const addressReq: AdditionalAddressRequestDTO = {
        AddressStreetName: AddressStreetName,
        AddressType: AddressType,
        BuildingNumber: BuildingNumber,
        CityId: CityID,
        CountryId: CountryID,
        CountyId: CountyID,
        EastWestSt: EastWestStreet,
        IsActive: true,
        NorthSouthSt: NorthSouthStreet,
        StreetPrefix1: StreetPrefix1,
        StreetPrefix2: StreetPrefix2,
        ParentId: ParentId,
        QuadrantId: Quadrant,
        StateId: StateID,
        StreetNumberMin: Number(StreetNumberMin),
        StreetNumberMax: Number(StreetNumberMax),
        StreetSuffix1: StreetSuffix1,
        StreetSuffix2: StreetSuffix2,
        ZipCode: Number(ZipCode),
      }
      let response_location;
      if (this.additionalAddress.AddressId) {
        response_location = this.addressService.updateAdditionalAddress(this.additionalAddress.AddressId, addressReq);
      } else {
        response_location = this.addressService.saveAdditionalAddress(addressReq);
      }
      response_location.subscribe((result: ApiResponseAdditionalAddressDTO) => {
        if (!result.error) {
          const AddressID = result.responseData.AddressId;
          this.additionalAddress.AddressId = AddressID;
          this.onSaveAdditionalAddress.emit(this.additionalAddress);
          this.onClose.emit();
        } else {
          this.notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.AdditionalAddressSaveOrUpdateFailed);
      });
    } 
  }
}  
