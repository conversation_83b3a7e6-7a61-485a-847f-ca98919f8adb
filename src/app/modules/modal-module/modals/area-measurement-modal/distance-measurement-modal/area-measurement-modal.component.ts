import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

import { MapService } from '../../../../map-module/service/map-service.service';
import { MapOptions } from '../../../../map-module/models/MapOptions';
import * as MapEnum from '../../../../map-module/models/MapEnum';

declare var google: any;

@Component({
  selector: 'app-area-measurement-modal',
  templateUrl: './area-measurement-modal.component.html',
  styleUrls: ['./area-measurement-modal.component.scss']
})
export class AreaMeasurementModalComponent implements OnInit {

  @Output() onClose = new EventEmitter();
  @Input() latLng: { lat: number, lng: number };
  @Output() onAreaSave: EventEmitter<any> = new EventEmitter<any>();
  @Input() areaPolygon: { area: number, polygon: any };
  public mapOptions: MapOptions;
  public map: any;
  private _mapService: MapService;
  drawingManager: any;
  public area: number;
  polygon: any;

  constructor(
    mapService: MapService
  ) {
    this._mapService = mapService;
  }

  ngAfterViewInit() {
  }

  ngOnInit() {
    this.initMap();
  }

  DrawShape(shape) {
    let instance = this;
    this.drawingManager = this._mapService.DrawPolygon(this.map, shape);
    this.drawingManager.setOptions({ drawingControl: true });
    if (shape == "polygon") {
      this._mapService.OnMapOverlayComplete(this.drawingManager, MapEnum.DrawMode.Polygon, (event) => {
        event.setOptions({ editable: true, draggable: true });
        this.drawingManager.setDrawingMode(null);
        let latlngArray = event.latLngs.getArray()[0].getArray();
        var area = google.maps.geometry.spherical.computeArea(latlngArray);
        this.map.setOptions({ draggable: true });
        if (this.polygon) {
          this.clearPolygon();
        }
        this.area = parseFloat(area.toFixed(2));
        this.polygon = event;

        google.maps.event.addListener(event.getPath(), 'set_at', () => {
          instance.preparedPloygonsData(event);
        });

        // Add event listener for new vertex insertion
        google.maps.event.addListener(event.getPath(), 'insert_at', () => {
          instance.preparedPloygonsData(event);
        });
      });
    }
  }

  preparedPloygonsData(event) {
    var newArea = google.maps.geometry.spherical.computeArea(event.getPath());
    newArea = parseFloat(newArea.toFixed(2));
    this.area = newArea;
  }

  clearPolygon() {
    if (this.polygon) {
      this._mapService.ClearPolygon(this.polygon);
      this.area = null;
    }
  }

  onSave() {
    const latlng = this.polygon.getPath().getArray().map(element => ({
      Latitude: element.lat(),
      Longitude: element.lng(),
    }));
    this.onAreaSave.emit({ area: this.area, polygon: latlng });
    this.onClose.emit(false);
  }

  private initMap() {
    this.mapOptions = new MapOptions('map-property-space');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 20, 7, null, this.latLng.lat, this.latLng.lng);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.FullscreenControl = false;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    this._mapService.AddController(this.map, "clearShapesInBM", MapEnum.GoogleMapControlPosition.Top_Left);
    this.DrawShape("polygon");
    if (this.areaPolygon) {
      this.area = this.areaPolygon.area;
      this.polygon = this.areaPolygon.polygon;
      const polygon = this._mapService.DrawShapeOnMap(this.map, this.areaPolygon.polygon, null, false, 'blue', true, (area) => { this.area = parseFloat(area.toFixed(2)) }, (poly) => { })
      this.polygon = polygon[0];
      this.polygon.setMap(this.map);
    }
  }

  closeParkingSpace() {
    this.onClose.emit();
  }

}
