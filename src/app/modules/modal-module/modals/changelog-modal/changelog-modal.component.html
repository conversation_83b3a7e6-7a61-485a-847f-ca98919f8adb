<div class="change-log-wrapper ">
<div class="row mt-2">
  <div class="col-md-3 mt-2">
    <div><b><span *ngIf="!changeLogDetails?.length && !IsLoader">No</span><span
          *ngIf="changeLogDetails?.length && !IsLoader">{{changeLogDetails?.length}}</span><span style="margin-right:5px;"
          *ngIf="IsLoader"><i class="fa fa-spinner fa-spin showspin"></i></span> ChangeLog(s) Found!</b></div>
  </div>
  <div class="col-md-2">
    <select class="form-control" [(ngModel)]="pageSize" (change)="filter(pager.currentPage)">
      <option value="100">100</option>
      <option value="50">50</option>
      <option value="20">20</option>
      <option value="10">10</option>
    </select>
  </div>
  <div class="col-md-4">
    <ul class="pagination mb-0" *ngIf="pager?.pages && pager?.pages?.length">
      <li [ngClass]="{disabled:pager?.currentPage === 1}">
        <a (click)="setPage(1)">««</a>
      </li>
      <li [ngClass]="{disabled:pager?.currentPage === 1}">
        <a (click)="setPage(pager?.currentPage - 1)">«</a>
      </li>
      <li *ngFor="let page of pager?.pages" [ngClass]="{active:pager?.currentPage === page}">
        <a (click)="setPage(page)">{{page}}</a>
      </li>
      <li [ngClass]="{disabled:pager?.currentPage === pager?.totalPages}">
        <a (click)="setPage(pager?.currentPage + 1)">»</a>
      </li>
      <li [ngClass]="{disabled:pager?.currentPage === pager?.totalPages}">
        <a (click)="setPage(pager?.totalPages)">»»</a>
      </li>
    </ul>
  </div>
  <div class="col-md-3">
    <div class="input-with-icon">
      <i class="fa fa-times" title="clear" (click)="clearSearchFilter()" *ngIf="filterInput"></i>
      <i class="fa fa-filter" *ngIf="!filterInput"></i>
      <input type="text" class="form-control" placeholder="Filter..." [(ngModel)]="filterInput"
        (ngModelChange)="filterItem(filterInput)">
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 mt-2">
    <div class="table-responsive">
      <table class="table table-hover table-inside">
        <thead>
          <tr>
            <th scope="col">Field Name</th>
            <th scope="col" class="max-width-250">Old Value </th>
            <th scope="col" class="max-width-250">New Value</th>
            <th scope="col">Changed By</th>
            <th scope="col">Application</th>
            <th scope="col">Action</th>
            <th scope="col">Changed Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let log of pagedchangeLogDetails">
            <td>{{log.DisplayText}}
              <button *ngIf="isPolygonField(log.FieldName)" class="btn btn-primary preview-btn" (click)="showBuildingFootprintPreview(log)">Show footprint preview</button>
            </td>
            <td class="max-width-250" *ngIf="!isPolygonField(log.FieldName)">{{log.OldValue}}</td>
            <td class="max-width-250" *ngIf="isPolygonField(log.FieldName)">
              <div class="trimmed-text" [title]="log.OldValue">
                {{ log.OldValue }}
              </div>
              <i class="fas fa-info-circle" title="{{log.OldValue}}"></i>
            </td>
            <td class="max-width-250" *ngIf="!isPolygonField(log.FieldName)">{{log.NewValue}}</td>
            <td class="max-width-250" *ngIf="isPolygonField(log.FieldName)">
              <div class="trimmed-text" title="{{ log.NewValue }}">
                {{ log.NewValue }}
              </div>
              <i class="fas fa-info-circle info-icon" title="{{ log.NewValue }}"></i>
            </td>
            <td>{{log.ChangedBy}}</td>
            <td>{{log.ApplicationDescription}}</td>
            <td>{{log.Action}}</td>
            <td>{{log.ChangedDate | date :dateFormat : "+0000"}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
</div>

<div *ngIf="showBuildingFootprintPreviewModal">
  <imperium-modal [width]="'xl-medium'" [height]="'large'" [(visible)]="showBuildingFootprintPreviewModal" [title]="'Building Footprint Preview'"
    [bodyTemplate]="footprintModal" (visibleChange)="onClosePreviewModal()">
    <ng-template #footprintModal>
      <app-footprint-preview-modal [location]="location" [selectedFootprintLog]="selectedFootprintLog"></app-footprint-preview-modal>
    </ng-template>
  </imperium-modal>
</div>
