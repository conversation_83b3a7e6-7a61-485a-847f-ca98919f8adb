// Angular core imports
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { DatePipe } from '@angular/common';
// Services
import { PropertyService } from '../../../../services/api-property.service';
import { PagerService } from '../../../../services/pager.service';
import { LoginService } from '../../../../services/login.service';
import { CommunicationService } from '../../../../services/communication.service';
import { AuditService } from '../../../../services/audit.service';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
// RxJS
import { Subscription } from 'rxjs';
// Models
import { ApiResponseListMapStringObject, AuditLogEntityTypes, LocationDTO } from '../../../../api-client';
// Utilities
import { cloneDeep } from 'lodash';
// Constants
import { CommonStrings, PolygonFields } from '../../../../../app/constants';

@Component({
  selector: 'app-changelog-modal',
  templateUrl: './changelog-modal.component.html',
  styleUrls: ['./changelog-modal.component.scss']
})
export class ChangelogModalComponent implements OnInit {

  @Input() parentId: any;
  @Input() changelogType: any;
  @Input() location: LocationDTO;
  @Output() onClose = new EventEmitter();
  @Input() fetchNewChangeLog = true;
  @Input() fetchOldChangeLog = true;
  changeLogDetails: Array<any> = new Array<any>();
  changeLogDetailsCopy: Array<any> = new Array<any>();
  polygonFields = PolygonFields;
  pagedchangeLogDetails: Array<any> = new Array<any>();
  IsLoader: boolean = false;
  pager: any = {};
  pageSize: any = 100;
  dateFormat: string;
  filterInput: string = '';
  IsApplication: boolean = true;
  showBuildingFootprintPreviewModal: boolean = false;
  selectedFootprintLog: any;
  isResearchStatusHistory: boolean = false;
  propertySaveOrNewPropertyFetchListener:Subscription;
  researchStatusHistoryListener: Subscription;
  constructor(private _propertyService: PropertyService, private pagerService: PagerService, private _loginService: LoginService, private _datePipe: DatePipe,private communicationService: CommunicationService,
  private auditService: AuditService, private notificationService: NotificationService) {
    this.dateFormat = this._loginService.UserInfo.DateFormat;
    this.propertySaveOrNewPropertyFetchListener = this.communicationService.subscribe('fetchChangeLog').subscribe(result => {
      this.parentId = result?.data;
      this.changeLogDetails = [];
      this.changeLogDetailsCopy = [];
      if (this.fetchOldChangeLog) {
        this.fetchChangeLogData();
      } else if (this.fetchNewChangeLog) {
        this.fetchNewChangeLogData();
      }
    });
  }

  ngOnDestroy(){
    this.propertySaveOrNewPropertyFetchListener?.unsubscribe();
  }

  ngOnInit() {
    if (this.fetchOldChangeLog) {
      this.fetchChangeLogData();
    } else if(this.fetchNewChangeLog) {
      this.fetchNewChangeLogData();
    }
  }

  fetchChangeLogData() {
    this.IsLoader = true;
    const response_location = this._propertyService.GetChangeLog(this.changelogType, this.parentId);
    response_location.subscribe(result => {
      if (!result.error) {
        this.changeLogDetails = result?.responseData;
        if (this.changeLogDetails?.length > 0) {
          this.changeLogDetails?.forEach(element => {
            if (element.DataTypeID == 3) {
              try {
                element.OldValue = this._datePipe?.transform(element.OldValue, this.dateFormat);
                element.NewValue = this._datePipe?.transform(element.NewValue, this.dateFormat);
              } catch (error) {
              }
            }
          });
          this.changeLogDetailsCopy = this.changeLogDetails;
          if(!this.fetchNewChangeLog){
            this.setPage(1);
          }
        }
        if(this.fetchNewChangeLog){
          this.fetchNewChangeLogData();
        }else{
        this.IsLoader = false;
        }
      }else{
        this.IsLoader = false;
        this.notificationService.ShowErrorMessage(result.message ?? CommonStrings.ErrorMessages.FailedToFetchChangeLogDetails);
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchChangeLogDetails);
      this.IsLoader = false;
    });
  }

  fetchNewChangeLogData() {
    let changeLogType = this.changelogType;
    if (this.changelogType === AuditLogEntityTypes.AdditionalAddress) {
      changeLogType = AuditLogEntityTypes.Address;
    }
    this.auditService.getAuditLogsByEntityTypeAndEntityId(this.changelogType, this.parentId).subscribe(
      (result: ApiResponseListMapStringObject) => {
        if (!result.error) {
          const newChangeLogDetails = result.responseData;
          this.IsLoader = false;
  
          if (newChangeLogDetails?.length > 0) {
            newChangeLogDetails.forEach(element => {
              if (element?.DataTypeID  && Number(element?.DataTypeID ) === 3) {
                try {
                  // @ts-ignore
                  element.OldValue = this._datePipe?.transform(element.OldValue, this.dateFormat) as unknown as object;
                  // @ts-ignore
                  element.NewValue = this._datePipe?.transform(element.NewValue, this.dateFormat) as unknown as object;
                } catch (error) {
                }
              }
            });
            this.changeLogDetails = (newChangeLogDetails || []).concat(this.changeLogDetails || []);
            this.changeLogDetailsCopy = cloneDeep(this.changeLogDetails); // Create a copy of the combined changelogs
          }
          this.setPage(1);
        }
      },
      error => {
        this.IsLoader = false;
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchChangeLogDetails);
      }
    );
    }
      
  cancel() {
    this.onClose.emit();
  }
  setPage(page: number) {
    this.filter(page);
    if (page < 1 || page > this.pager.totalPages || page > this.pager.totalPages) {
      return;
    }
  }
  filter(page: number) {
    if (this.changeLogDetails.length < parseInt(this.pageSize)) {
      this.pager.currentPage = 1;
      page = 1;
    }
    page = page || this.pager.currentPage;
    this.pager = this.pagerService.getPager(this.changeLogDetails.length, page, parseInt(this.pageSize));
    this.pagedchangeLogDetails = this.changeLogDetails.slice(this.pager.startIndex, this.pager.endIndex + 1);
    // window.scrollTo(0,0);
  }
  clearSearchFilter() {
    this.filterInput = '';
    this.clearFun();
  }

  clearFun() {
    this.changeLogDetails = this.changeLogDetailsCopy;
    this.pagedchangeLogDetails = this.changeLogDetailsCopy.slice(0, this.pageSize);
    this.setPage(1);
  }
  filterItem(value) {
    if (value.length >= 3) {
      this.changeLogDetails = Object.assign([], this.changeLogDetailsCopy).filter(item => !!item.DisplayText && item.DisplayText.toLowerCase().indexOf(value.toLowerCase()) > -1);
      this.pagedchangeLogDetails = this.changeLogDetails.slice(0, this.pageSize);
    }
    else {
      this.clearFun();
    }
    this.setPage(1);
  }

  showBuildingFootprintPreview(log) {
    this.showBuildingFootprintPreviewModal = true;
    this.selectedFootprintLog = log;
  }

  onClosePreviewModal(){
    this.showBuildingFootprintPreviewModal = false;
    this.selectedFootprintLog = undefined;
  }

  isPolygonField(fieldName: string): boolean {
    return this.polygonFields.includes(fieldName);
  }
}
