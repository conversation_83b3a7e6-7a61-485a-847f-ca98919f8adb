<div class="col-md-12">
    <div [ngClass]="{'file-row': true, row: true, odd: oddRow}">
        <form [formGroup]="mediaForm" class="form-align">
        <div class="row">
            <div class="col-md-6">
                <div class="row">
                        <div class="col-md-12 data-wrapper">
                            <label>File Name</label>
                            <input type="text" class="form-control" 
                            formControlName="MediaName" [(ngModel)]="media.MediaName" 
                            [ngClass]="{'error-field':(!mediaForm.controls['MediaName'].valid)}">
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label>Media Type</label>
                            <ng-select formControlName="MediaType" [items]="mediaTypes"
                            [virtualScroll]="true"
                            bindLabel="MediaTypeName"
                            bindValue="MediaTypeEnum"
                             placeholder="--Select--"
                             [(ngModel)]="media.MediaTypeID" 
                             [ngClass]="{'error-field':(!mediaForm.controls['MediaType'].valid)}">
                    </ng-select>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label> Media SubType</label>
                            <ng-select formControlName="MediaSubType" [items]="mediaSubTypes"
                            [virtualScroll]="true"
                            bindLabel="MediaSubTypeName"
                            bindValue="MediaSubTypeEnum"
                             placeholder="--Select--"
                             [(ngModel)]="media.MediaSubTypeID"></ng-select>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label>Media Description</label>
                            <textarea rows="1" class="form-control" formControlName="Description" [(ngModel)]="media.Description"></textarea>
                        </div>
                        <div class="col-lg-12 mb-3">
                            <label class="set-as"><input type="checkbox" formControlName="IsDefault" data-testId="media-isdefault"
                                    name="media.IsDefault" [(ngModel)]="media.IsDefault"/>
                                Set as Default <img src="assets/images/favourte-icon.png" width="20"></label>
                        </div>
                    
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-lg-12 mb-3">
                        <img [src]="media.URL" width="360" height="260">
                    </div>
                    
                    <div class="col-lg-12">
                        <ul class="size-info">
                            <li>
                                <i class="fa fa-file"></i>
                                <h3>{{fileSize}}</h3>
                                <p>size</p>
                            </li>
                            <li>
                                <i class="fa fa-arrows-v"></i>
                                <h3>{{media.Height}}</h3>
                                <p>Height</p>
                            </li>
                            <li>
                                <i class="fa fa-exchange"></i>
                                <h3>{{media.Width}}</h3>
                                <p>Width</p>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-12 text-right mb-3">
                    </div>
                </div>
            </div>
        </div>
                    <div class="col-md-12 no-padding" *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                        <div class="row">
                            <div class="col-md-6">
                                <label>Did you take this photo ? <span class="mandatory">*</span></label>
                                <ng-select formControlName="IsOwnMedia" [items]="yesNoList"
                                    labelForId="IsOwnMedia" [virtualScroll]="true" bindLabel="Item" bindValue="ID"
                                    placeholder="--Select--" [(ngModel)]="media.IsOwnMedia"
                                    (change)="mediaSourceChange(media.IsOwnMedia)" data-testId="media-ownership"
                                    [ngClass]="{'error-field':(!mediaForm.controls['IsOwnMedia'].valid && mediaForm.controls['IsOwnMedia'].touched)}">
                                </ng-select>
                            </div>
                            <div class="col-md-6" *ngIf="!media.IsOwnMedia">
                                <label>Media Source <span class="mandatory">*</span></label>
                                <ng-select formControlName="MediaSourceID" [items]="mediaSource" [virtualScroll]="true"
                                    labelForId="MediaSourceID" bindLabel="MediaSourceName" bindValue="MediaSourceEnum"
                                    placeholder="--Select--" [(ngModel)]="media.MediaSourceID" data-testId="media-media-source"
                                    [ngClass]="{'error-field':(!mediaForm.controls['MediaSourceID'].valid && mediaForm.controls['MediaSourceID'].touched)}">
                                </ng-select>
                            </div>
                        </div>
                    </div>
                    
                    <ng-container *ngIf="!IsAgentAdd && !IsBranchAdd && !IsCompanyAdd">
                        
                        <div class="col-md-12 mt-3 no-padding" *ngIf="!media.IsOwnMedia">
                            <div class="row">
                                <div class="col-md-12">
                                    <label>Source Comments<span *ngIf="mediaWEBsource" class="mandatory">*</span></label>
                                    <textarea rows="3" formControlName="SourceComments" [(ngModel)]="media.SourceComments"
                                        class="form-control" maxlength="1000"
                                        [ngClass]="{'error-field':(!mediaForm.controls['SourceComments'].valid) && mediaForm.controls['SourceComments'].touched}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mt-3 no-padding" *ngIf="!media.IsOwnMedia">
                            <div class="row">
                                <div class="col-md-12">
                                    <label style="color:#20a8d8;" *ngIf="media.MediaID > 0 && fileArray.length">
                                        Source Materials
                                    </label>
                                    <label for="inputFiles" class="file-input-btn" *ngIf="!media.MediaID">
                                        <span style="color:#20a8d8;">
                                            <i class="fa fa-paperclip" aria-hidden="true"></i>
                                            <span>Attach confirmation source materials <span class="mandatory">*</span></span>
                                            <input id="inputFiles" name="files[]" multiple="" type="file" accept="image/*"
                                                (change)="fileUploadEvent($event)">
                                        </span>
                                    </label>
                                </div>
                                <div class="col-md-6">
                                    <div class="row m-0">
                                        <ng-container *ngFor="let file of fileArray; let i = index;">
                                            <div class="col-md-12 dottedline pr-0 pl-0">
                                                <span class="labelText" *ngIf="!media.MediaID">{{file.MediaName}}</span>
        
                                                <span class="deleteRow" *ngIf="!media.MediaID && !file.IsUploaded"
                                                    (click)="deleteAddedFiles(i)">
                                                    <i class="fa fa-times"></i>
                                                </span>
                                                <span class="statusRow" *ngIf="!media.MediaID && file.IsUploaded">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                <span class="deleteRow mr-2">
                                                    {{file.Size}}
                                                </span>
                                                <a class="labelText" *ngIf="media.MediaID > 0" target="_blank" download
                                                    href="{{file.OrginalURL}}">
                                                    {{file.MediaName}}.{{file.Ext}}
                                                </a>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                        </div>
                   
                    </ng-container>
                </form>
                    <div class='col-md-12'>
                        <div class='row'>
                            <div class='col-md-12'>
                    <div *ngIf="!isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <button class="btn btn-primary start" (click)="upload()"[disabled]="!mediaForm.valid" data-testId="media-upload">
                            <i class="fa fa-upload"></i>
                            <strong>Upload</strong>
                        </button>
                        <button class="btn btn-success cancel" (click)="close()">
                            <i class="fa fa-cancel"></i>
                            <strong>Cancel</strong>
                        </button>
                    </div>
                    <div *ngIf="isEdit" class="text-right">
                        <img *ngIf="showLoader" src="assets/images/ajax-loader.gif" width="20">
                        <button class="btn btn-primary start" (click)="save()" data-testId="media-save">
                             <i class="fa fa-upload"></i>
                             <strong>Save</strong>
                         </button>
                        <button class="btn btn-success cancel" (click)="close()">
                            <i class="fa fa-cancel"></i>
                            <strong>Cancel</strong>
                        </button>
                    </div>
                    </div>
                </div>
            </div>
    </div>
</div>
