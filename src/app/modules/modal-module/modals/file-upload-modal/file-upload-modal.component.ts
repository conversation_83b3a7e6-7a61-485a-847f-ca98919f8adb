import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FileObject, FileObjectStatus } from '../../../../modules/aws/types/types';
import { MediaService } from '../../../../services/media.service'
import { mapEditPropertyDTO } from '../../../../DTO/mapEditPropertyDTO';
import { Subscription } from 'rxjs';
import { MediaType, MediaSubType, MediaSource } from '../../../../models/MediaType';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { LoginService } from '../../../../services/login.service';
import { SharedDataService } from '../../../../services/shareddata.service';
import { environment } from '../../../../../environments/environment';
import { FileTypes } from '../../../aws/types/types';
import { NotificationService } from '../../../notification/service/notification.service';
import {ContactMedia} from '../../../../models/ContactMedia';
import { MediaInfoDTO } from '../../../../models/media-dto';
import { ApiResponseImageUploadDTO, ApiResponseMediaDTO, ImageUploadRequestDTO, MediaDTO, MediaRequestDTO } from '../../../../api-client';
import { CommonStrings } from '../../../../constants';
@Component({
  selector: 'app-file-upload-modal',
  templateUrl: './file-upload-modal.component.html',
  styleUrls: ['./file-upload-modal.component.scss']
})
export class FileUploadModalComponent implements OnInit {

  @Input() media: MediaInfoDTO;
  @Input() oddRow: boolean;
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() IsAgentAdd = false;
  @Input() IsBranchAdd = false;
  @Input() IsCompanyAdd = false;
  @Output() onUploadEvent = new EventEmitter();
  @Output() onSourceChange = new EventEmitter<boolean>();
  @Output() onSave = new EventEmitter<string>();
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  FileObjectStatus = FileObjectStatus;
  progress = 0;
  speed = 0;
  uploadError: string;
  containerEventSubscription: Subscription;
  uploadHandle: any;
  isDefault: any = 0;
  mediaTypes: Array<MediaType>;
  mediaSubTypes: Array<MediaSubType>;
  mediaForm: FormGroup;
  fileArray: Array<any> = [];
  files: MediaInfoDTO[] = [];
  ImageFiles = [];
  mediaSource: Array<MediaSource>;
  fileObject: FileObject;
  fileSize: string;
  isEdit: boolean = false;
  mediaWEBsource = false;
  showLoader: boolean = false;
  DataArray: Array<any> = [];
  public mediaCopy: MediaInfoDTO;

  IsAttachment = false;
  fileObjectAttach: FileObject[] = [];
  count = 0;
  attachfileSize: string;
  initialAttachmentDetails: ContactMedia;
  yesNoList;
  lookupData: any;

  constructor(private mediaService: MediaService,
    private _loginService: LoginService,
    private _notificationService: NotificationService,
    private _sharedDataService: SharedDataService ) {
      this.yesNoList = this._sharedDataService.yesNoList;
  }

  ngOnInit() {
    this.createForm();
    this.media.ModifiedBy = this._loginService.UserInfo.EntityID;
    this.media.MediaRelationTypeID = MediaDTO.MediaRelationTypeIDEnum.Property;
    this.media.RelationID = this.initialDetails.propertyId;
    if (this.media.MediaID) {
      this.isEdit = true;
      this.getAttachmentMedia();
      this.fileSize = this.formatBytes(this.media.Size);
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    }
    else {
      this.fileObject = new FileObject(this.media.File);
      this.media.Ext = this.media.File.name.split('.').pop();
      this.media.MediaName = this.media.File.name.split('.')[0];
      this.media.Size = this.media.File.size;
      this.fileSize = this.formatBytes(this.fileObject.file.size);
      this.media.MediaTypeID = MediaDTO.MediaTypeIDEnum.AerialImagery;
      this.media.CreatedBy = this._loginService.UserInfo.EntityID;
      this.media.IsDefault = true;
      this.media.MediaID = 0;
    }

    this.lookupData = this._sharedDataService.getLookupDropdowns();
    this.mediaTypes = this.lookupData['MediaTypeID'] ?? [];
    this.mediaSubTypes = this.lookupData['MediaSubTypeID'] ?? [];
    this.mediaSource = this.lookupData['MediaSourceID'] ?? [];
  }

  upload() {   
    this.showLoader = true;
    if (this.mediaForm.valid) {
      if (!this.media.IsOwnMedia && !this.files.length) {
        this.showLoader = false;
        this._notificationService.ShowErrorMessage('Please upload source materials');
        return;
      }
      this.fileObject.status = FileObjectStatus.Uploading;
      this.uploadError = undefined;
      this.progress = 0;
      const imageUploadRequest: ImageUploadRequestDTO = {
        fileName: this.fileObject.fileName,
        base64: this.media.URL,
        isMarketBrief: false
      }
      const uploadHandle = this.mediaService.uploadToS3(imageUploadRequest);
      uploadHandle.subscribe(result => {
        if (!result.error) {
          this.media.Path = this.fileObject.fileName;
          this.saveMedia();
        } else {
          this.showLoader = false;
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToUploadImageToS3);
      });
    }
  }

  save() {
    this.showLoader = true;
    this.saveMedia();
  }

  cancel() {  
    if (this.fileObject && this.fileObject.status === FileObjectStatus.Uploading) {
      this.fileObject.status = FileObjectStatus.Canceled;
    }
  }

  close() {
    this.onClose.emit();
  }

  clear() {
    if (this.fileObject && this.fileObject.status !== FileObjectStatus.Uploading) {
      this.fileObject.status = FileObjectStatus.Deleted;
    }
  }

  ngOnDestroy() {
    // prevent memory leak when component destroyed
    if(!!this.containerEventSubscription)
      this.containerEventSubscription.unsubscribe();
    this.clear();
  }

  createForm() {
    this.mediaForm = new FormGroup({
      'MediaName': new FormControl('', Validators.required),
      'MediaType': new FormControl(''),
      'MediaSubType': new FormControl(''),
      'Description': new FormControl(''),
      'IsDefault': new FormControl(''),
      'IsOwnMedia': new FormControl(''),
      'MediaSourceID': new FormControl(''),
      'SourceComments': new FormControl('')
    });
    if (!this.IsAgentAdd && !this.IsBranchAdd && !this.IsCompanyAdd) {
      if (this.media.MediaID > 0) {
        this.mediaForm.controls['IsOwnMedia'].disable();
        this.mediaForm.controls['MediaSourceID'].disable();
        this.mediaForm.controls['SourceComments'].disable();
        this.mediaForm.controls['IsOwnMedia'].markAsUntouched();
        this.mediaForm.controls['MediaSourceID'].markAsUntouched();
        this.mediaForm.controls['SourceComments'].markAsUntouched();
      } else {
        this.mediaForm.controls['IsOwnMedia'].enable();
        this.mediaForm.controls['MediaSourceID'].enable();
        this.mediaForm.controls['SourceComments'].enable();
        this.mediaForm.controls['IsOwnMedia'].markAsTouched();
        this.mediaForm.controls['MediaSourceID'].markAsTouched();
        this.mediaForm.controls['SourceComments'].markAsTouched();
        this.mediaForm.get('IsOwnMedia').setValidators([Validators.required]);
        this.mediaForm.get('IsOwnMedia').valueChanges.subscribe(
          (IsOwnMedia) => {
            if (!IsOwnMedia) {
              this.mediaForm.get('MediaSourceID').setValidators([Validators.required]);
            } else {
              this.mediaForm.get('MediaSourceID').clearValidators();
            }
            this.mediaForm.get('MediaSourceID').updateValueAndValidity();
          });
        this.mediaForm.get('MediaSourceID').valueChanges.subscribe(
          (MediaSourceID) => {
            if (MediaSourceID === MediaDTO.MediaSourceIDEnum.OwnerWebsite || MediaSourceID === MediaDTO.MediaSourceIDEnum.BuildingWebsite ||
              MediaSourceID === MediaDTO.MediaSourceIDEnum.BrokersWebsite || MediaSourceID === MediaDTO.MediaSourceIDEnum.ThirdPartyWebsite) {
              this.mediaWEBsource = true;
              this.mediaForm.get('SourceComments').setValidators([Validators.required]);
            } else {
              this.mediaWEBsource = false;
              this.mediaForm.get('SourceComments').clearValidators();
            }
            this.mediaForm.get('SourceComments').updateValueAndValidity();
          });
      }
    }
  }

  saveMedia() {
    if (this.mediaForm.valid) {
    if (this.media.IsOwnMedia != null) {
      this.media.IsOwnMedia = this.media.IsOwnMedia;
    }
    if (this.IsAgentAdd) {
      this.media.IsDefault = true;
    }
    this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    const medias = this.mediaService.saveOrUpdateMedia(this.media, this.media.MediaID);
    medias.subscribe((result: ApiResponseMediaDTO) => {
      if (!result.error) {
        this.clear();
        const dataResult = result.responseData;
        this.showLoader = false;
        if(this.fileObject){
        this.fileObject.mediaId = 0;
        }
        if (!this.media.IsOwnMedia) {
          if (this.files.length) {
            this.files.forEach((x, index) => {
              this.uploadAttachmentMedia(dataResult[0].MediaID, index);
            })
          }
        }
        if (this.fileObject) {
          this.fileObject.mediaId = 0;
        }
        this._notificationService.ShowSuccessMessage(CommonStrings.SuccessMessages.MediaSavedSuccessfully);
        this.onUploadEvent.emit("true");  
      } else {
        this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveMedia);
    });
  } else {
    this.showLoader = false;
  }
 }
 uploadAttachmentMedia(mediaId: number, i: number) {
  this.showLoader = true;
  this.IsAttachment = true;
  this.files[i].ModifiedBy = this._loginService.UserInfo.EntityID;
  this.files[i].MediaRelationTypeID = MediaDTO.MediaRelationTypeIDEnum.MediaSupportDocs;
  this.files[i].RelationID = mediaId;
  this.mediaAttachment(i);
  const imageUploadRequest: ImageUploadRequestDTO = {
     fileName: this.fileObjectAttach[i].fileName,
     base64: this.files[i].UploadPathURL
  }
  const uploadHandleAttach = this.mediaService.uploadToS3(imageUploadRequest);
  uploadHandleAttach.subscribe((result: ApiResponseImageUploadDTO) => {
    if (!result.error) {
      this.fileArray[i].IsUploaded = true;
      this.count++;
      this.saveAttachmentMedia(i);
    } else {
      this._notificationService.ShowErrorMessage(result.message);
    }
  }, error => {
    this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToUploadImageToS3);
  });
}

mediaAttachment(i) {
  this.fileObjectAttach[i] = new FileObject(this.files[i].File);
  this.files[i].Ext = this.files[i].File.name.split('.').pop();
  this.files[i].MediaName = this.files[i].File.name.split('.')[0];
  this.files[i].Size = this.files[i].File.size;
  this.attachfileSize = this.formatBytes(this.fileObjectAttach[i].file.size);
  this.files[i].MediaTypeID = MediaDTO.MediaTypeIDEnum.OtherMedia;
  this.files[i].MediaSubTypeID = undefined;
  this.files[i].CreatedBy = this._loginService.UserInfo.EntityID;
  this.files[i].IsDefault = false;
  this.files[i].MediaID = 0;
  this.fileExtentions(this.files[i])
}
saveAttachmentMedia(i: number) {
  this.files[i].Path = this.fileObjectAttach[i].fileName;
  const medias = this.mediaService.saveOrUpdateMedia(this.files[i]);
  medias.subscribe(result => {
    if (!result.error) {
      if (this.fileObjectAttach[i]) {
        this.fileObjectAttach[i].mediaId = 0;
      }
      if (this.files.length === this.count) {
        this.showLoader = false;
        this.onSave.emit(this.media.Path);
      }
    } else {
      this._notificationService.ShowErrorMessage(result.message);
    }
  }, error => {
    this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveMedia);
  });
}
getAttachmentMedia() {
  this.fileArray = [];
  if (this.media.MediaID) {
    const medias = this.mediaService.getAllMedias(this.media.MediaID);
    medias.subscribe(result => {
      if (!result.error) {
        this.fileArray = result.responseData;
        this.fileArray.forEach(x => {
          x.Size = this.formatBytes(x.Size);
          x.OrginalURL = environment.MediaS3Base + environment.MediaS3Path + '/' + x.Path;
        });
        this.fileArray = this.fileArray.sort((a, b) => b.CreatedDate.localeCompare(a.CreatedDate));
      } else {
        this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => this._notificationService.ShowErrorMessage(error?.error?.message));
  }
}

deleteAddedFiles(i: number, dynamicMedia = null, dynamicIndex = null) {
  if (!!dynamicMedia) {
    this.ImageFiles[dynamicIndex].fileArray.splice(i, 1);
    this.ImageFiles[dynamicIndex].uploadingFileArray.splice(i, 1);
  } else {
    this.fileArray.splice(i, 1);
    this.files.splice(i, 1);
  }
}

 mediaSourceChange(value: boolean) {
  if (value) {
    this.onSourceChange.emit(false);
  } else {
    this.onSourceChange.emit(true);
  }
}

  fileUploadEvent(fileInput: any, dynamicMedia = null, index = null) {
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const media = {} as MediaInfoDTO;
        media.File = fileInput.target.files[i];
        media.Ext = media.File.name.split('.').pop();
        if (this.mediaService.mediaCheck(media) === false) {
          this._notificationService.ShowErrorMessage('File not Supported');
          return;
        }
        if (fileInput.target.files && fileInput.target.files[i]) {
          const reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            media.UploadPathURL = event.target.result;
            const img = new Image;
            img.onload = function () {
              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
            this.fileExtentions(media)
          }
          reader.readAsDataURL(fileInput.target.files[i]);
        }
        if (!!dynamicMedia) {
          this.ImageFiles[index].uploadingFileArray.push(media);
        } else {
          this.files.push(media);
        }
      }
    }
    fileInput.target.value = null;
    if (!!dynamicMedia) {
      this.ImageFiles[index].fileArray = [];
      this.ImageFiles[index].uploadingFileArray.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.ImageFiles[index].fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    } else {
      this.fileArray = [];
      this.files.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    }
  }
  fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PNG || media.Ext.toLowerCase() === FileTypes.JPG) {
      media.URL = environment.MediaS3Base + environment.MediaS3Path + environment.MediaS3ThumbnailPath +
        '/' + environment.MediaS3ThumbResolution + '/' + media.Path;
    } else {
      this.mediaService.fileExtentions(media);
    }
  }

  formatBytes(bytes) {
    if (bytes < 1024) return bytes + " Bytes";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(3) + " KB";
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(3) + " MB";
    else return (bytes / 1073741824).toFixed(3) + " GB";
  };

}
