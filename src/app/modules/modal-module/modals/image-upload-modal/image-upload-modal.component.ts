import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FileObject, FileObjectStatus } from '../../../aws/types/types';
import { Subscription } from 'rxjs';
import { MediaService } from '../../../../services/media.service'
import { ContactMedia } from '../../../../models/ContactMedia';
import { MediaInfoDTO } from '../../../../models/media-dto';
import { MediaType, MediaSubType, MediaSource } from '../../../../models/MediaType';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';

import { LoginService } from '../../../../services/login.service';
import { FileTypes } from '../../../aws/types/types';
import { environment } from '../../../../../environments/environment';

import { SharedDataService } from '../../../../services/shareddata.service';
import { NotificationService } from '../../../notification/service/notification.service';

// Import IndexedDB Class
import { IndexedDBService } from '../../../../services/indexeddb.service'
import { StagingIndexedDBService } from '../../../../services/indexeddb-staging.service';
import { CommonStrings } from '../../../../constants';
import { ApiResponseMediaDTO, ImageUploadRequestDTO, MediaDTO } from '../../../../api-client';

@Component({
  selector: 'image-upload-modal',
  templateUrl: './image-upload-modal.component.html',
  styleUrls: ['./image-upload-modal.component.scss']
})
export class ExpressViewImageUploadModalComponent implements OnInit {

  @Input() media: MediaInfoDTO;
  @Input() oddRow: boolean;
  @Input() IsAgentAdd = false;
  @Input() IsBranchAdd = false;
  @Input() IsCompanyAdd = false;
  @Input() initialDetails: ContactMedia;
  @Input() IsProperty = false;
  @Input() MinifiedMedia = false;
  @Input() HideDefault = false;
  @Output() onSave = new EventEmitter<string>();
  @Output() onClose = new EventEmitter();
  @Output() onRetake = new EventEmitter();
  @Output() onSetDefault = new EventEmitter<string>();
  @Output() onSourceChange = new EventEmitter<boolean>();
  @Input() IsPropertyBasicInfo = false;
  @Input() uploadFiles = new Array<MediaInfoDTO>();
  @Input() hideRetakeBtn = false;
  uploadFilesCopy: Array<MediaInfoDTO> = new Array<MediaInfoDTO>();
  files: MediaInfoDTO[] = [];
  attachMedia: MediaInfoDTO[] = [];
  FileObjectStatus = FileObjectStatus;
  FileObjectStatusAttach: FileObjectStatus[] = [];
  progress = 0;
  speed = 0;
  uploadError: string;
  containerEventSubscription: Subscription;
  uploadHandle: any;
  isDefault: any = 0;
  mediaForm: FormGroup;
  fileObject: FileObject;
  fileObjectAttach: FileObject[] = [];
  fileSize: string;
  isEdit = false;
  showLoader = false;
  mediaTypes: Array<MediaType>;
  mediaSource: Array<MediaSource>;
  mediaSubTypes: Array<MediaSubType>;
  URL: any;
  IsAttachment = false;
  fileArray: Array<any> = [];
  initialAttachmentDetails: ContactMedia;
  attachfileSize: string;
  count = 0;
  mediaWEBsource = false;
  public mediaCopy: MediaInfoDTO;
  DataArray: Array<any> = [];
  mediaView = 'singleMedia';
  NonImageFiles = [];
  ImageFiles = [];
  extendHeight = '40vh';
  dataCount: number = null;
  multipleArrayCount = 0;
  IndexedDBService: IndexedDBService;
  StagingIndexedDBService: StagingIndexedDBService
  defaultImage : any;
  galleryImages: any;
  isNoDefaultImage: boolean = false;
  isReplaceDefaultImage: boolean = false;
  isDefaultImageConflict: boolean = false;
  title: string;
  yesNoList;
  lookupData: any;

  constructor(private mediaService: MediaService,
    private _loginService: LoginService,
    private sharedDataService: SharedDataService,
    private _notificationService: NotificationService, private fb: FormBuilder) {
    this.IndexedDBService = new IndexedDBService();
    this.StagingIndexedDBService = new StagingIndexedDBService();
    this.yesNoList = this.sharedDataService.yesNoList;
  }

  ngOnInit() {
    this.mediaView = 'singleMedia'
    this.createForm();

    this.media.ModifiedBy = this._loginService.UserInfo.EntityID;
    this.media.MediaRelationTypeID = this.initialDetails.RelationshipTypeID;
    this.media.RelationID = this.initialDetails.RelationID;
    this.media.RelationID = this.initialDetails.PropertyID;
    if (this.media.MediaID > 0) {
      this.isEdit = true;
      this.fileSize = this.formatBytes(this.media.Size);
      this.getAttachmentMedia();
      this.mediaCopy = {} as MediaInfoDTO;
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    } else {
      this.fileObject = new FileObject(this.media.File);
      this.media.Ext = this.media.Ext || this.media.File.name.split('.').pop();
      this.media.MediaName = this.media.File.name.split('.')[0];
      this.media.Size = this.media.File.size;
      this.fileSize = this.formatBytes(this.fileObject.file.size);
      this.media.MediaTypeID = this.media.MediaTypeID || MediaDTO.MediaTypeIDEnum.AerialImagery;
      this.media.MediaSubTypeID = this.media.MediaSubTypeID || undefined;
      this.media.CreatedBy = this._loginService.UserInfo.EntityID;
      this.media.IsDefault = this.getIsDefault(this.media.MediaTypeID, this.media.MediaSubTypeID);
      this.media.MediaID = 0;
      this.media.MediaSourceID = this.media.MediaSourceID;
      this.media.IsOwnMedia = this.media.IsOwnMedia;
      if (this.IsPropertyBasicInfo === true) {
        this.media.MediaTypeID = this.media.MediaTypeID || MediaDTO.MediaTypeIDEnum.BuildingImage;
        this.media.MediaSubTypeID = this.media.MediaSubTypeID || MediaDTO.MediaSubTypeIDEnum.MainPhoto;
        this.media.IsDefault = this.media.IsDefault || true;
      }
    }
    this.fileExtentions(this.media);
    this.lookupData = this.sharedDataService.getLookupDropdowns();

    this.mediaTypes = this.lookupData['MediaTypeID'] ?? [];
    this.media.MediaName = this.getMediaFileNameFromMediaTypeID(this.media.MediaTypeID);
    this.mediaSubTypes = this.lookupData['MediaSubTypeID'] ?? [];
    this.mediaSource = this.lookupData['MediaSourceID'] ?? [];
  }

  ngAfterViewInit() {
    if (this.initialDetails.PropertyID) {
      const medias = this.mediaService.getAllMedias(this.initialDetails.PropertyID);
      medias.subscribe(result => {
        if (!result.error) {
          this.galleryImages = result.responseData;
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => this._notificationService.ShowErrorMessage(error?.error?.message));
    }
  }

  fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PNG || media.Ext.toLowerCase() === FileTypes.JPG) {
      media.URL = media.URL || environment.MediaS3Base + environment.MediaS3Path + environment.MediaS3ThumbnailPath +
        '/' + environment.MediaS3ThumbResolution + '/' + media.Path;
    } else {
      this.mediaService.fileExtentions(media);
    }
  }

  checkUpload() {
    if (this.galleryImages && this.galleryImages.length > 0) {
      this.defaultImage = this.galleryImages.find(image => image.IsDefault == 1)
      if (!this.media.IsDefault && this.defaultImage === undefined) {
        this.isNoDefaultImage = true;
        this.title = CommonStrings.DialogConfigurations.Messages.NoDefaultImageMessage;
      } else if (this.media.IsDefault && this.defaultImage) {
        if (this.defaultImage.MediaSourceID == 1) {
          this.isDefaultImageConflict = true;
          this.title = CommonStrings.DialogConfigurations.Messages.DefaultImageConflictMessage;
        }
        else {
          this.isReplaceDefaultImage = true;
          this.title = CommonStrings.DialogConfigurations.Messages.ReplaceDefaultImageMessage
        }
      } else {
        this.upload();
      }
    } else {
      this.upload();
    }
  }
  

  setNewImageAsDefaultImage() {
    this.media.IsDefault = true;
    this.isNoDefaultImage = false;
    this.upload();
  }

  swapDefaultImage() {
    this.media.IsDefault = true;
    this.defaultImage.isDefault = 0;
    this.isReplaceDefaultImage = false;
    this.upload();
  }

  noDefaultImages() {
    this.isNoDefaultImage = false;
    this.upload();
  }

  imageConflictClose() {
    this.media.IsDefault = false;
    this.isDefaultImageConflict = false;
    this.upload();
  }

  noSwapDefaultImage() {
    this.media.IsDefault = false;
    this.isReplaceDefaultImage = false;
    this.upload();
  }

  upload() {
    if (this.mediaForm.valid) {
      this.showLoader = true;
      if (environment.EnableBackgroundMediaUpload) {
        this.backgroundSaveMedia();
      } else {
        const imageUploadRequest: ImageUploadRequestDTO = {
          fileName: this.fileObject.fileName,
          base64: this.media.UploadPathURL,
        }
        const uploadHandle = this.mediaService.uploadToS3(imageUploadRequest);
        uploadHandle.subscribe(result => {
          if (!result.error) {
            this.media.Path = this.fileObject.fileName;
            this.saveMedia();
          } else {
            this._notificationService.ShowErrorMessage(result.message);
          }
        }, error => {
          this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToUploadImageToS3);
        });
      }
    }
  }

  save() {
    this.showLoader = true;
    this.saveMedia();
  }

  close() {
    this.onClose.emit();
  }

  retake() {
    this.onRetake.emit();
  }

  createForm() {
    this.mediaForm = new FormGroup({
      'MediaName': new FormControl('', Validators.required),
      'MediaTypeID': new FormControl(''),
      'MediaSubTypeID': new FormControl(''),
      'Description': new FormControl(''),
      'IsDefault': new FormControl(''),
      'IsOwnMedia': new FormControl(''),
      'MediaSourceID': new FormControl(''),
      'SourceComments': new FormControl('')
    });
    if (!this.IsAgentAdd && !this.IsBranchAdd && !this.IsCompanyAdd) {
      if (this.media.MediaID > 0) {
        this.mediaForm.controls['IsOwnMedia'].disable();
        this.mediaForm.controls['MediaSourceID'].disable();
        this.mediaForm.controls['SourceComments'].disable();
        this.mediaForm.controls['IsOwnMedia'].markAsUntouched();
        this.mediaForm.controls['MediaSourceID'].markAsUntouched();
        this.mediaForm.controls['SourceComments'].markAsUntouched();
      } else {
        this.mediaForm.controls['IsOwnMedia'].enable();
        this.mediaForm.controls['MediaSourceID'].enable();
        this.mediaForm.controls['SourceComments'].enable();
        this.mediaForm.controls['IsOwnMedia'].markAsTouched();
        this.mediaForm.controls['MediaSourceID'].markAsTouched();
        this.mediaForm.controls['SourceComments'].markAsTouched();
        this.mediaForm.get('IsOwnMedia').setValidators([Validators.required]);
        this.mediaForm.get('IsOwnMedia').valueChanges.subscribe(
          (IsOwnMedia) => {
            if (!IsOwnMedia) {
              this.mediaForm.get('MediaSourceID').setValidators([Validators.required]);
            } else {
              this.mediaForm.get('MediaSourceID').clearValidators();
            }
            this.mediaForm.get('MediaSourceID').updateValueAndValidity();
          });
        this.mediaForm.get('MediaSourceID').valueChanges.subscribe(
          (MediaSourceID) => {
            if (MediaSourceID === MediaDTO.MediaSourceIDEnum.OwnerWebsite || MediaSourceID === MediaDTO.MediaSourceIDEnum.BuildingWebsite ||
              MediaSourceID === MediaDTO.MediaSourceIDEnum.BrokersWebsite || MediaSourceID === MediaDTO.MediaSourceIDEnum.ThirdPartyWebsite) {
              this.mediaWEBsource = true;
              this.mediaForm.get('SourceComments').setValidators([Validators.required]);
            } else {
              this.mediaWEBsource = false;
              this.mediaForm.get('SourceComments').clearValidators();
            }
            this.mediaForm.get('SourceComments').updateValueAndValidity();
          });
      }
    }
  }

  backgroundSaveMedia() {
    if (this.media.IsOwnMedia != null) {
      this.media.IsOwnMedia = this.media.IsOwnMedia;
    }
    if (this.IsAgentAdd) {
      this.media.IsDefault = true;
    }
    this.mediaCopy = JSON.parse(JSON.stringify(this.media));
    const status = this.media.RelationID ? this.IndexedDBService.getIndexedDBInitializationStatus() : this.StagingIndexedDBService.getIndexedDBInitializationStatus()
    if (status) {
      (async () => {
        const attachments: { fileName: string, media: MediaInfoDTO }[] = [];
        // Preparing supporting attachments to be uploaded
        this.files.forEach((x, index) => {
          const attachment = this.prepareAttachmentMediaUpload(index);
          attachments.push(attachment);
        })
        //storing in staging as property ID is not assigned yet.
        if (!this.media.RelationID) {
          await this.StagingIndexedDBService.saveStagingMedia({
            mediaIdentifier: 'staging-' + this.fileObject.fileName,
            mediaInformation: {
              fileObject: this.fileObject,
              media: this.media,
              fileName: this.fileObject.fileName,
              attachments: attachments
            }
          });
        }
        else{
        // Uploading to IndexedDB
          await this.IndexedDBService.saveMedia({
            mediaIdentifier: this.media.RelationID + '-' + this.fileObject?.fileName,
            mediaInformation: {
              fileObject: this.fileObject,
              media: this.media,
              fileName: this.fileObject?.fileName,
              attachments: attachments
            }
          });
        }
        this.showLoader = false;
        this.close();
      })();
    }
  }
  saveMedia() {
    if (this.mediaForm.valid) {
      if (this.media.IsOwnMedia != null) {
        this.media.IsOwnMedia = this.media.IsOwnMedia;
      }
      if (this.IsAgentAdd) {
        this.media.IsDefault = true;
      }
      this.mediaCopy = JSON.parse(JSON.stringify(this.media));
      const medias = this.mediaService.saveOrUpdateMedia(this.media);
      medias.subscribe((result: ApiResponseMediaDTO) => {
        if (!result.error) {
          const dataResult = result.responseData;
          if (this.media.IsDefault) {
            this.onSetDefault.emit(this.media.Path);  
          }
          if (!this.media.IsOwnMedia) {
            if (this.files.length) {
              this.files.forEach((x, index) => {
                this.uploadAttachmentMedia(dataResult[0].MediaID, index);
              })
            }
          }
          if (this.fileObject) {
            this.fileObject.mediaId = 0;
          }
          if (!this.files.length) {
            this.showLoader = false;
            this.onSave.emit(this.media.Path);
          }
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveMedia);
      });
    }
  }

  formatBytes(bytes) {
    if (bytes < 1024) {
      return bytes + ' Bytes';
    } else if (bytes < 1048576) {
      return (bytes / 1024).toFixed(3) + ' KB';
    } else if (bytes < 1073741824) {
      return (bytes / 1048576).toFixed(3) + ' MB';
    } else { return (bytes / 1073741824).toFixed(3) + ' GB' };
  };

  // .................... Change Log ........................
  getIsDefault=(mediaTypeID: MediaDTO.MediaTypeIDEnum, mediaSubTypeID?: MediaDTO.MediaSubTypeIDEnum) => mediaTypeID === MediaDTO.MediaTypeIDEnum.BuildingImage && mediaSubTypeID === MediaDTO.MediaSubTypeIDEnum.MainPhoto

  getFileNameFromMediaTypeName = (mediaTypeName:string): string => mediaTypeName + (mediaTypeName?.toLowerCase().endsWith('image') ? '' : ' Image')

  getMediaFileNameFromMediaTypeID = (mediaTypeID: MediaDTO.MediaTypeIDEnum):string => {
    const mediaTypeName = this.mediaTypes.find((media)=> media.MediaTypeEnum === mediaTypeID)?.MediaTypeName
    return this.getFileNameFromMediaTypeName(mediaTypeName)
  }
  // .............................Attachment Source ...................

  fileUploadEvent(fileInput: any, dynamicMedia = null, index = null) {
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const fileObject = new FileObject(fileInput.target.files[i]);
        const media = {} as MediaInfoDTO;
        media.File = fileInput.target.files[i];
        media.Ext = media.File.name.split('.').pop();
        if (this.mediaService.mediaCheck(media) === false) {
          this._notificationService.ShowErrorMessage('File not Supported');
          return;
        }
        if (fileInput.target.files && fileInput.target.files[i]) {
          const reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            media.UploadPathURL = event.target.result;
            const img = new Image;
            img.onload = function () {
              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
            this.fileExtentions(media)
          }
          reader.readAsDataURL(fileInput.target.files[i]);
        }
        if (!!dynamicMedia) {
          this.ImageFiles[index].uploadingFileArray.push(media);
        } else {
          this.files.push(media);
        }
      }
    }
    fileInput.target.value = null;
    if (!!dynamicMedia) {
      this.ImageFiles[index].fileArray = [];
      this.ImageFiles[index].uploadingFileArray.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.ImageFiles[index].fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    } else {
      this.fileArray = [];
      this.files.forEach(x => {
        const fileSize = this.formatBytes(x.File.size);
        this.fileArray.push({ 'MediaName': x.File.name, 'Size': fileSize, 'IsUploaded': false });
      });
    }
  }

  mediaAttachment(i) {
    this.fileObjectAttach[i] = new FileObject(this.files[i].File);
    this.files[i].Ext = this.media.Ext || this.files[i].File.name.split('.').pop();
    this.files[i].MediaName = this.files[i].File.name.split('.')[0];
    this.files[i].Size = this.files[i].File.size;
    this.attachfileSize = this.formatBytes(this.fileObjectAttach[i].file.size);
    this.files[i].MediaTypeID =  MediaDTO.MediaTypeIDEnum.OtherMedia;
    this.files[i].MediaSubTypeID = undefined;
    this.files[i].CreatedBy = this._loginService.UserInfo.EntityID;
    this.files[i].IsDefault = false;
    this.files[i].MediaID = 0;
    this.fileExtentions(this.files[i])
  }

  uploadAttachmentMedia(mediaId: number, i: number) {
    this.showLoader = true;
    this.IsAttachment = true;
    this.files[i].ModifiedBy = this._loginService.UserInfo.EntityID;
    this.files[i].MediaRelationTypeID = MediaDTO.MediaRelationTypeIDEnum.MediaSupportDocs;
    this.files[i].RelationID = mediaId;
    this.mediaAttachment(i);
    const imageUploadRequest: ImageUploadRequestDTO = {
      fileName: this.fileObjectAttach[i].fileName,
      base64: this.files[i].UploadPathURL,
    }
    const uploadHandleAttach = this.mediaService.uploadToS3(imageUploadRequest);
    uploadHandleAttach.subscribe(result => {
      if (!result.error) {
        this.fileArray[i].IsUploaded = true;
        this.count++;
        this.saveAttachmentMedia(i);
      } else {
        this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToUploadImageToS3);
    });
  }

  /**
    * Method for Preparing supporting attachment to be uploaded
    * @param { number } index Attachment File Index
  */
  prepareAttachmentMediaUpload = (index: number) => {
    this.files[index].ModifiedBy = this._loginService.UserInfo.EntityID;
    this.files[index].MediaRelationTypeID = MediaDTO.MediaRelationTypeIDEnum.MediaSupportDocs;
    this.files[index].RelationID = this.initialDetails.PropertyID;
    this.mediaAttachment(index);
    return { fileName: this.fileObjectAttach[index].fileName, media: this.files[index] };
  }

  saveAttachmentMedia(i: number) {
    this.files[i].Path = this.fileObjectAttach[i].fileName;
    const medias = this.mediaService.saveOrUpdateMedia(this.files[i]);
    medias.subscribe(result => {
      if (!result.error) {
        if (this.fileObjectAttach[i]) {
          this.fileObjectAttach[i].mediaId = 0;
        }
        if (this.files.length === this.count) {
          this.showLoader = false;
          this.onSave.emit(this.media.Path);
        }
      } else {
        this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveMedia);
    });
  }

  getAttachmentMedia() {
    this.fileArray = [];
    if (this.media.MediaID) {
      const medias = this.mediaService.getAllMedias(this.media.MediaID);
      medias.subscribe(result => {
        if (!result.error) {
          this.fileArray = result.responseData;
          this.fileArray.forEach(x => {
            x.Size = this.formatBytes(x.Size);
            x.OrginalURL = environment.MediaS3Base + environment.MediaS3Path + '/' + x.Path;
          });
          this.fileArray = this.fileArray.sort((a, b) => b.CreatedDate.localeCompare(a.CreatedDate));
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchMedia));
    }
  }

  mediaSourceChange(value: boolean) {
    if (value) {
      this.onSourceChange.emit(false);
    } else {
      this.onSourceChange.emit(true);
    }
  }

  deleteAddedFiles(i: number, dynamicMedia = null, dynamicIndex = null) {
    if (!!dynamicMedia) {
      this.ImageFiles[dynamicIndex].fileArray.splice(i, 1);
      this.ImageFiles[dynamicIndex].uploadingFileArray.splice(i, 1);
    } else {
      this.fileArray.splice(i, 1);
      this.files.splice(i, 1);
    }
  }

}
