import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { PropertyDetailsDTO } from '../../../../../app/api-client';
import { RollupObject } from '../../../../models/rollupObject';

@Component({
  selector: 'app-manual-footprint-add',
  templateUrl: './manual-footprint-add.component.html',
  styleUrls: ['./manual-footprint-add.component.css']
})
export class ManualFootprintAddComponent implements OnInit {
  @Output() close :EventEmitter<any> = new EventEmitter<any>();
  @Output() save:EventEmitter<any> = new EventEmitter<any>();
  @Input() property: PropertyDetailsDTO;
  form: FormGroup;
  @Input() condo: any;
  @Input() rollupMasterFreeholdFieldsObject: RollupObject;
  @Input() lookupDropdowns: any;
  @Input() dataArray: any[];
  @Input() propertyCopy: PropertyDetailsDTO;

  constructor() { }

  ngOnInit(): void {
    this.form = new FormGroup({
      'ContributedSourceComments': new FormControl(''),
      'ContributedGBA_SF': new FormControl(''),
      'ContributedGBASource': new FormControl(''),
      'GLA_SF': new FormControl(''),
      'IndustrialGLASource': new FormControl(''),
      'OfficeSF': new FormControl(''),
      'OfficeNLASource': new FormControl(''),
      'GLAR_SF': new FormControl(''),
      'RetailGLARSource': new FormControl(''),
    });
  }

  onSave() {
    this.save.emit(this.form);
  }

  isFormValid() {
    return Object.keys(this.form.controls)
      .filter(key => this.form.controls[key].enabled) // Only check enabled controls
      .every(key => this.form.controls[key].valid);
  }
}
