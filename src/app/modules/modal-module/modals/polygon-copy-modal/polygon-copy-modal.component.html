<div class="col">
  <div class="col">
    <h4 class="title">Copy From</h4>
    <div class="floor-data">
      <div class="specific-use-wrapper">
        <div id="propertyUse" class="row">
          <div class="no-padding">
            <div class="form-group">
              <label class="form-control-label default-property-use" for="text-input">Property Use
              </label>
              <div class="default-property-use">
                <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                  <input type="radio" id="toCopy_{{i}}_{{1}}" [value]="useType.UseTypeID" [(ngModel)]="originalPolygon.specificUse" [ngModelOptions]="{ standalone : true }" [disabled]="true" *ngIf="useType.UseTypeID != propertyUseTypes.Land">
                  <label for="toCopy_{{i}}_{{1}}" title="{{useType.UseTypeName}}" *ngIf="useType.UseTypeID != propertyUseTypes.Land" class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer" data-testId="copy-polygon-from-property-use"
                    [ngClass]="{'blue' : useType.UseTypeID == propertyUseTypes.Office,
                    'red' : useType.UseTypeID == propertyUseTypes.Industrial,
                    'orange' : useType.UseTypeID == propertyUseTypes.Retail,
                    'brown' : useType.UseTypeID == propertyUseTypes.Apartments,
                    'magenta' : useType.UseTypeID == propertyUseTypes.SpecialUse,
                    'green': useType.UseTypeID == propertyUseTypes.Land,
                    'blueCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.Office && useType.UseTypeID == propertyUseTypes.Office) ,
                    'redCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.Industrial && useType.UseTypeID == propertyUseTypes.Industrial),
                    'orangeCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.Retail && useType.UseTypeID == propertyUseTypes.Retail),
                    'brownCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.Apartments && useType.UseTypeID == propertyUseTypes.Apartments), 
                    'magentaCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.SpecialUse && useType.UseTypeID == propertyUseTypes.SpecialUse),
                    'greenCheckedLabel': (originalPolygon.specificUse == propertyUseTypes.Land && useType.UseTypeID == propertyUseTypes.Land)
                    }">
                    {{useType.UseTypeLabel}}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <label class="form-control-label no-padding" style="width: 115px;">Additional Use</label>
        <ng-select [items]="additionalUseTypes" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="UseTypeLabel" [disabled]="true" bindValue="UseTypeID" placeholder="" [ngModelOptions]="{ standalone: true }" [(ngModel)]="originalPolygon.additionalUse">
        </ng-select>
      </div>
      <div class="d-flex justify-content-between" style="gap: 1rem">
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Min </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px;" bindLabel="key" bindValue="value" placeholder="" [ngModelOptions]="{ standalone: true }" [disabled]="true" [(ngModel)]="originalPolygon.minFloor">
          </ng-select>

        </div>
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Max </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="key" bindValue="value" placeholder="" [ngModelOptions]="{ standalone: true }" [disabled]="true" [(ngModel)]="originalPolygon.maxFloor">
          </ng-select>
        </div>
      </div>
      <div>
        <div>
          <label class="form-control-label" for="text-input">Floor Size
          </label>
          <div class="position-relative">
            <input type="number" [(ngModel)]="originalPolygon.floorSize" [ngModelOptions]="{standalone: true}" class="form-control maxnumbervalidation floorsizeinput" [disabled]="true">
          </div>
        </div>
      </div>
      <div class=" description form-group">
        <label class="form-control-label no-padding" for="text-input"> Description </label>
        <input type="text" class="form-control description" [(ngModel)]="originalPolygon.description" [ngModelOptions]="{ standalone: true}" [disabled]="true">
      </div>
      <div class="floorcount form-group">
        <label class="form-control-label" for="text-input"> Floor Count </label>
        <input type="number" class="form-control floor-count" [(ngModel)]="originalPolygon.floorCount" [ngModelOptions]="{standalone: true}" [disabled]="true">
      </div>
    </div>
    <h4 class="title">Copy To</h4>
    <div class="floor-data">
      <div class="specific-use-wrapper">
        <div id="propertyUse" class="row">
          <div class="no-padding">
            <div class="form-group">
              <label class="form-control-label default-property-use" for="text-input">Property Use
              </label>
              <div class="default-property-use">
                <div class="radio-toolbar" *ngFor="let useType of propertyTypes;let i = index">
                  <input type="radio" id="toAdd_{{i}}_{{1}}" [value]="useType.UseTypeID" (change)="onSpecificUseChange(useType.UseTypeID)" [(ngModel)]="polygonCopy.specificUse" [ngModelOptions]="{ standalone : true }" *ngIf="useType.UseTypeID != 7" [disabled]="useType.UseTypeID == originalPolygon.specificUse">
                  <label for="toAdd_{{i}}_{{1}}" title="{{useType.UseTypeName}}" *ngIf="useType.UseTypeID != 7" class="center-aligned-radio-button btn type-btn Q-blue-outline-button cursor-pointer" [attr.data-testid]="'copy-polygon-to-property-use-' + useType.UseTypeID" [ngClass]="{'blue' : (useType.UseTypeID == propertyUseTypes.Office && useType.UseTypeID != originalPolygon.specificUse),
                    'red' : (useType.UseTypeID == propertyUseTypes.Industrial && useType.UseTypeID != originalPolygon.specificUse),
                    'orange' : (useType.UseTypeID == propertyUseTypes.Retail && useType.UseTypeID != originalPolygon.specificUse),
                    'brown' : (useType.UseTypeID == propertyUseTypes.Apartments && useType.UseTypeID != originalPolygon.specificUse),
                    'magenta' : (useType.UseTypeID == propertyUseTypes.SpecialUse && useType.UseTypeID != originalPolygon.specificUse),
                    'green': (useType.UseTypeID == propertyUseTypes.Land && useType.UseTypeID != originalPolygon.specificUse),
                    'blueCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.Office && useType.UseTypeID == propertyUseTypes.Office) ,
                    'redCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.Industrial && useType.UseTypeID == propertyUseTypes.Industrial),
                    'orangeCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.Retail && useType.UseTypeID == propertyUseTypes.Retail),
                    'brownCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.Apartments && useType.UseTypeID == propertyUseTypes.Apartments),
                    'magentaCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.SpecialUse && useType.UseTypeID == propertyUseTypes.SpecialUse),
                    'greenCheckedLabel': (polygonCopy.specificUse == propertyUseTypes.Land && useType.UseTypeID == propertyUseTypes.Land),
                    'radio-button-disabled': (useType.UseTypeID == originalPolygon.specificUse)
                    }">
                    {{useType.UseTypeLabel}}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <label class="form-control-label" style="width: 115px;">Additional Use</label>
        <ng-select [items]="additionalUseTypes" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="UseTypeLabel" bindValue="UseTypeID" placeholder="" [ngModelOptions]="{ standalone: true }" [(ngModel)]="polygonCopy.additionalUse" data-testId="copy-poygon-to-additional-use">
        </ng-select>
      </div>
      <div class="d-flex justify-content-between" style="gap: 1rem">
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Min </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px;" bindLabel="key" bindValue="value" placeholder="" [ngModelOptions]="{ standalone: true }" [(ngModel)]="polygonCopy.minFloor" (change)="onMinMaxChange()" data-testId="copy-polygon-to-min-floor">
          </ng-select>

        </div>
        <div style="display: flex; flex-direction: column;">
          <label class="form-control-label"> Max </label>
          <ng-select [items]="floorOptions" [virtualScroll]="true" style="width: 80px; height:39px" bindLabel="key" bindValue="value" placeholder="" [ngModelOptions]="{ standalone: true }" [(ngModel)]="polygonCopy.maxFloor" (change)="onMinMaxChange()" data-testId="copy-polygon-to-max-floor">
          </ng-select>
        </div>
      </div>
      <div>
        <div>
          <label class="form-control-label" for="text-input">Floor Size
          </label>
          <div class="position-relative">
            <input type="number" [(ngModel)]="polygonCopy.floorSize" [ngModelOptions]="{standalone: true}" class="form-control maxnumbervalidation floorsizeinput" [disabled]="true" data-testId="copy-polygon-floor-size">
          </div>
        </div>
      </div>
      <div class=" description form-group">
        <label class="form-control-label no-padding" for="text-input"> Description </label>
        <input type="text" class="form-control description" [(ngModel)]="polygonCopy.description" [ngModelOptions]="{ standalone: true}" data-testId="copy-polygon-to-description">
      </div>
      <div class="floorcount form-group">
        <label class="form-control-label" for="text-input"> Floor Count </label>
        <input type="number" class="form-control floor-count" [(ngModel)]="polygonCopy.floorCount" [ngModelOptions]="{standalone: true}" [disabled]="true" data-testId="copy-polygon-floor-count">
      </div>
    </div>
    <div class="d-flex justify-content-end w-100">
      <button (click)="onAddPolygon()" class="btn btn-sm btn-primary save-polygon" [disabled]="!isNewPolygonValid()" data-testId="copy-polygon-add">
        Copy Polygon
      </button>
    </div>
  </div>
</div>
