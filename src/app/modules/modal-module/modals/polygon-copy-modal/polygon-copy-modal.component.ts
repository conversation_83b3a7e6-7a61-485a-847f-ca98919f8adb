import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
import { MultiPolygon } from '../../../../models/Common';
import { UseTypes } from '../../../../enumerations/useTypes';

@Component({
  selector: 'app-polygon-copy-modal',
  templateUrl: './polygon-copy-modal.component.html',
  styleUrls: ['./polygon-copy-modal.component.scss']
})
export class PolygonCopyModalComponent implements OnInit {

  @Output() onClose = new EventEmitter();
  @Input() additionalUseTypes: any[];
  @Input() propertyTypes: any[];
  @Output() onPolygonSave: EventEmitter<any> = new EventEmitter<any>();
  @Input() originalPolygon: MultiPolygon;
  @Input() floorOptions: any[];
  public polygonCopy: MultiPolygon;
  private _notificationService: NotificationService;
   propertyUseTypes = UseTypes;

  constructor(
    notificationService: NotificationService
  ) {
    this._notificationService = notificationService;
  }

  ngAfterViewInit() {
  }

  ngOnInit() {
    this.polygonCopy = {
      ...this.originalPolygon,
      specificUse: undefined,
      minFloor: undefined,
      maxFloor: undefined,
      floorCount: undefined,
      localBuildingFootPrintID: uuidv4(),
      description: undefined,
      BuildingFootPrintID: undefined,
      additionalUse: undefined
    }
  }

  onMinMaxChange() {
    if (this.polygonCopy.minFloor && this.polygonCopy.maxFloor && this.polygonCopy.minFloor > this.polygonCopy.maxFloor) {
      this._notificationService.ShowErrorMessage('Min or Max Floor connot be more than property floors');
      this.polygonCopy.minFloor = null;
      this.polygonCopy.maxFloor = null;
      this.polygonCopy.floorCount = null;
    } else if (this.polygonCopy.minFloor && this.polygonCopy.maxFloor) {
      if (this.polygonCopy.minFloor == this.polygonCopy.maxFloor) {
        this.polygonCopy.floorCount = 1;
      } else {
        this.polygonCopy.floorCount = Number(this.polygonCopy.maxFloor) - Number(this.polygonCopy.minFloor) + 1;
      }
    } else {
      this.polygonCopy.floorCount = null;
    }
  }

  onSpecificUseChange(useType) {
    this.polygonCopy.specificUse  = useType;
  }

  isNewPolygonValid() {
    if (this.polygonCopy.floorCount && this.polygonCopy.specificUse && this.polygonCopy.minFloor && this.polygonCopy.maxFloor && this.polygonCopy.floorSize) {
      return true;
    }
    return false;
  }

  onAddPolygon() {
    this.onPolygonSave.emit(this.polygonCopy);
  }

}
