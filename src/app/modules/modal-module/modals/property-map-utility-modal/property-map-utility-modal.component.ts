import { Component, OnInit, Input} from '@angular/core';
import { GoogleMapsOverlay } from '@deck.gl/google-maps';
import { MapOptions } from '../../../map-module/models/MapOptions';
import { MapService } from '../../../map-module/service/map-service.service';
import * as MapEnum from '../../../map-module/models/MapEnum';
import { PropertySearchList } from '../../../../models/PropertySearch';
import { MapHelperService } from '../../../../services/map-helper.service';
import { environment } from '../../../../../environments/environment';
import { Overlays } from '../../../../enumerations/overlays';
import { MapDefaultOptions } from '../../../../enumerations/mapDefaultOptions';

declare var google: any;
@Component({
  selector: 'app-property-map-utility-modal',
  templateUrl: './property-map-utility-modal.component.html',
  styleUrls: ['./property-map-utility-modal.component.scss']
})
export class PropertyMapUtilityModalComponent implements OnInit {

  @Input() propertyList: Array<PropertySearchList>;
  selectedPropertyList: PropertySearchList[];
  public mapOptions: MapOptions;
  public map: any;
  polylines: any;
  public showAddEditPropertyForm: boolean;
  Latitude: number;
  Longitude: number;
  private propertyMarkers: Array<any>;
  private infoWindows: Array<any>;
  previousHoverMarker: any = null;
  previousMarker: any = null;
  ourOverlay = null;
  list: any[] = [
    { checked: false, id: Overlays.Zoning },
    { checked: false, id: Overlays.Parcel },
    { checked: false, id: Overlays.Building },
  ];
  checkedList = [];
  deckOverlay: any;
  isMapLoaded: boolean = false;

  constructor(
    private _mapService: MapService,
    private _mapHelperService: MapHelperService) {
  }

  ngOnInit() {
    if (this.propertyList) {
      this.selectedPropertyList = this.propertyList.filter(property => property.IsSelected === true)
      if (this.selectedPropertyList) {
        this.Latitude = this.selectedPropertyList[0]?.Location?.Latitude;
        this.Longitude = this.selectedPropertyList[0]?.Location?.Longitude;
      }
    } else {
      this.Latitude = MapDefaultOptions.Latitude;
      this.Longitude = MapDefaultOptions.Longitude;
    }
    this.initMap();
  }

  private initMap() {
    this.mapOptions = new MapOptions('PropertyUtilityMap');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 9, 7, null, this.Latitude, this.Longitude);
    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.ZoomLevel = 18;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.ArterialRoad, MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.LocalRoad, MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
    this.loadPropertyPins();
    this.createOverlay();
    this.deckOverlay = new GoogleMapsOverlay({
      layers: []
    });
    this.map.addListener('tilesloaded', () => {
      this.isMapLoaded = true;
    });
  }

  addControllers() {
    this._mapService.AddController(this.map, "layersDropdown", MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map, "layerInfoCard", MapEnum.GoogleMapControlPosition.LEFT_Top);
  }
  
  private closeInfoWindows() {
    if (this.infoWindows) {
      for (var infowindow of this.infoWindows) {
        this._mapService.ClearInfoWindow(infowindow);
      }
    }
    this.infoWindows = new Array<any>();
  }

  createOverlay() {
    this.ourOverlay = new google.maps.OverlayView();
    this.ourOverlay.draw = function () { };
    this.ourOverlay.setMap(this.map);
  }

  private setPropertyPinInfoWindow(marker: any, prop: any) {
    this._mapService.OnMarkerHover(marker, (event, eventmarker, latlng) => {
      this.closeInfoWindows();
      for (const m of this.propertyMarkers) {
        this._mapService.ClearMarkerAnimation(m);
      }
      const instance = this;
      let infoText = '';
      const infowWindow = document.createElement('div');
      const imagePath = environment.MediaS3Base + environment.MediaS3Path +
        environment.MediaS3ThumbnailPath + '/' + environment.MediaS3ThumbResolution + '/' + prop.MainPhotoUrl;
      infoText += `<div class="col-md-12 mb-2 p-0" style ="cursor:pointer;">`;
      infoText += `<div class="iwBox-img-wrap"><img src="`
        + imagePath + `" alt="img" style="width:100%; height:auto;" class="img-fluid"></div>`;
      infoText += `<ul class="iwTextBox">`;
      if (prop.PropertyName != null) {
        infoText += `<li class=" mapiw-text"><span>` + prop.PropertyName + ` </span></li>`;
      }
      if (prop.AddressStreetName && prop.PropertyName !== prop.AddressStreetName) {
        infoText += `<li class=" mapiw-text"><span>` + prop.AddressStreetName + `</span></li>`;
      }
      infoText += `<li class=" mapiw-text"><span>` + prop.City + ' ' + prop.ZipCode + `</span></li>`;
      infoText += `<li class=" mapiw-text"><span>` + prop.PropertyType + `</span>`;
      if (prop.SpecificUses != null) {
        infoText += `<span class=" mapiw-text" style="display:inline-block !important">-` + prop.SpecificUses + `</span></li>`;
      } else {
        infoText += `</li>`
      }
      infoText += `<li class=" mapiw-text"><span> PID: ` + prop.PropertyId + `</span></li>`;
      infoText += `</ul>`;
      infoText += `</div>`;
      infowWindow.innerHTML = infoText;
      const infowindow = this._mapService.ShowInfoWindow(this.map, marker, event, infowWindow, false,
        this.previousMarker, this.previousHoverMarker, this.ourOverlay);
      if (this.infoWindows == null) {
        this.infoWindows = new Array<any>();
      }
      this.infoWindows.push(infowindow);
    });
  }

  private loadPropertyPins() {
    this.propertyMarkers = this._mapService.ClearMarkers(this.propertyMarkers);
    let markers: Array<any> = new Array<any>();
    for (let property of this.selectedPropertyList) {
      const icon = this._mapHelperService.GetPropertyPinByResearchType(property.AllPropertiesDetails.ResearchTypeID, property.UseTypeID, property.SpecificUseID);
      let marker = this._mapService.PlaceMarker(this.map, property?.Location?.Latitude, property?.Location?.Longitude,false,icon);
      markers.push(marker);
      marker.data = property;
      this.propertyMarkers.push(marker);
      this.setPropertyPinInfoWindow(marker, property);
    }
  }

  shareCheckedList(item: string[]) {
    this._mapService.addOverlay(item, this.map, this.deckOverlay, 'layerInfoCard');
  }
}
