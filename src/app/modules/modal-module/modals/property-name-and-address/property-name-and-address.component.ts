import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CommonStrings } from '../../../../constants';
import { PropertyDetailsDTO } from '../../../../../app/api-client';

@Component({
  selector: 'app-property-name-and-address',
  templateUrl: './property-name-and-address.component.html',
  styleUrls: ['./property-name-and-address.component.css']
})
export class PropertyNameAndAddressComponent implements OnInit {
  @Input() property: PropertyDetailsDTO;
  @Output() onClosePropertyNameModal = new EventEmitter();
  @Output() onSavePropeertyNameAndAddress = new EventEmitter();

  pNameAndAddressForm: FormGroup;
  streetNumberMin = null;
  addressStreetName = null;
  messages = CommonStrings.Messages;
  hasStreetNumber;
  hasStreetName;

  constructor() { }

  ngOnInit(): void {
    this.createForm();
    const { StreetNumberMin, AddressStreetName } = this.property?.Address || {};
    this.hasStreetNumber = !!StreetNumberMin;
    this.hasStreetName = !!AddressStreetName;
    this.streetNumberMin = StreetNumberMin;
    this.addressStreetName = AddressStreetName;
  }

  createForm() {
    this.pNameAndAddressForm = new FormGroup({
      'StreetNumberMin': new FormControl('', Validators.required),
      'AddressStreetName': new FormControl('', Validators.required),
    });
  }

  savePropertyNameAndAddress() {
    if (this.pNameAndAddressForm?.valid) {
      this.property.Address.AddressStreetName = this.addressStreetName;
      this.property.Address.StreetNumberMin = this.streetNumberMin;
      this.onSavePropeertyNameAndAddress.emit();
    }
  }

  onCancel() {
    this.onClosePropertyNameModal.emit();
  }

}
