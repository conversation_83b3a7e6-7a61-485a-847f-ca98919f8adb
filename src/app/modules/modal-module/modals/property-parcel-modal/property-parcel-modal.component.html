<div class="col-md-12">
  <form [formGroup]="pParcelForm"  (ngSubmit)="saveParcelDetails()">
    <div class="form-group row">
      <div class="col-md-6">
        <div class="form-group row">
          <label class="col-md-6 form-control-label" for="text-input">Parcel No</label>
          <div class="col-md-6">
            <input type="text" class="form-control" formControlName="ParcelNo" [(ngModel)]="propertyParcel.ParcelNo" [ngClass]="{'error-field':(!pParcelForm.controls['ParcelNo'].valid)}" data-testId="parcel-parcel-no">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['ParcelNo'].valid)">Parcel No Required</div>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-6 form-control-label" for="text-input">Parcel Size</label>
          <div class="col-md-6">
            <input type="text" numericOnly allowNegative="false" allowDecimal="true" class="form-control" formControlName="ParcelSF" [(ngModel)]="propertyParcel.ParcelSize" (paste)="validatePasteInput($event, true)"
            (keypress)="validateIntegerInput($event, true)" data-testId="parcel-parcel-size">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['ParcelSF'].valid && pParcelForm.controls['ParcelSF'].touched)">Parcel Size Required</div>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-6 form-control-label" for="text-input">Lot</label>
          <div class="col-md-6">
            <input type="text" maxlength="100" class="form-control" formControlName="Lot" [(ngModel)]="propertyParcel.Lot" data-testId="parcel-lot"
              >
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Lot'].valid && pParcelForm.controls['Lot'].touched)">Lot Required</div>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-6 form-control-label" for="text-input">Block</label>
          <div class="col-md-6">
            <input type="text" maxlength="100" numericOnly allowNegative="false" allowDecimal="true" class="form-control" formControlName="Block" [(ngModel)]="propertyParcel.Block"
            (paste)="validatePasteInput($event, true)" data-testId="parcel-block"
            (keypress)="validateIntegerInput($event, true)">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Block'].valid && pParcelForm.controls['Block'].touched)">Block Required</div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group row">
          <label class="col-md-6 form-control-label" for="text-input">Subdivision</label>
          <div class="col-md-6">
            <input type="text"  maxlength="100" class="form-control" formControlName="Subdivision" [(ngModel)]="propertyParcel.SubDivision" data-testId="parcel-sub-division">
            <div class="validation-error" *ngIf="(!pParcelForm.controls['Subdivision'].valid && pParcelForm.controls['Subdivision'].touched)">Subdivision Required</div>
          </div>
        </div>
      </div>
    </div>
    <button type="submit" class="btn btn-warn" [disabled]="!pParcelForm.valid" data-testId="parcel-enter-save">Save Parcel info</button>
        <input *ngIf="(propertyParcel.ParcelID)" data-testId="parcel-delete"
          class="btn btn-warn" type="button" value="Delete Parcel" (click)="DeleteParcelDetails()" />
  </form>
</div>