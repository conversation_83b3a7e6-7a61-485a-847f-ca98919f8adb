import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { PropertyService } from '../../../../services/api-property.service';
import { EnumCountry } from '../../../../enumerations/county';
import { HttpStatus } from '../../../../enumerations/http-status-codes';
import { LoginService } from '../../../../services/login.service';
import { validateIntegerInput, validatePasteInput } from '../../../../../app/utils';
import { ParcelService } from '../../../../../app/services/parcel.service';
import { NotificationService } from '../../../../modules/notification/service/notification.service';
import { ApiResponseNull, ApiResponseParcelPropertyDTO, ParcelPropertyDTO, ParcelPropertyRequestDTO } from '../../../../../app/api-client';
import { CommonStrings } from '../../../../constants';

@Component({
  selector: 'app-property-parcel-modal',
  templateUrl: './property-parcel-modal.component.html',
  styleUrls: ['./property-parcel-modal.component.css']
})
export class PropertyParcelModalComponent implements OnInit {

  pParcelForm: FormGroup;
  @Input() CountryId: number;
  @Input() EntityID: number;
  @Input() propertyParcel: ParcelPropertyDTO;
  @Input() fromAddParcel: boolean = false;
  @Input() propertyId: number

  @Output() onSave: EventEmitter<ParcelPropertyDTO> = new EventEmitter<ParcelPropertyDTO>();
  @Output() onClose = new EventEmitter();
  DataArray: Array<any> = [];
  propertyParcelCopy: ParcelPropertyDTO;;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;

  constructor(private _propertyService: PropertyService
              , private _loginService: LoginService, private parcelService: ParcelService,
            private notificationService: NotificationService) { }

  ngOnInit() {
    this.createForm();
    if (this.CountryId == EnumCountry.Australia)
      this.propertyParcel.ParcelSize = this.propertyParcel.ParcelSizeSM;
    else
      this.propertyParcel.ParcelSize = this.propertyParcel.ParcelSize;

      this.propertyParcelCopy = JSON.parse(JSON.stringify(this.propertyParcel));
      this.DataArray = [];

  }

  createForm() {
    this.pParcelForm = new FormGroup({
      'Lot': new FormControl(''),
      'Block': new FormControl(''),
      'Subdivision': new FormControl(''),
      'ParcelNo': new FormControl('', Validators.required),
      'ParcelSF': new FormControl(''),
    });
  }

  saveParcelDetails() {
    if (this.pParcelForm.valid) {
      if (this.fromAddParcel) {
        this.onSave.emit(this.propertyParcel);
      } else {
        this.propertyParcel.ParcelSize = this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.propertyParcel.ParcelSize);
        let response_parcel;
        const reqBody: ParcelPropertyRequestDTO = {
          ParcelNo: this.propertyParcel.ParcelNo,
          Lot: this.propertyParcel.Lot,
          Block: this.propertyParcel.Block,
          SubDivision: this.propertyParcel.SubDivision,
          ParcelSF: this.propertyParcel.ParcelSize,
        }
        if (this.propertyParcel.ParcelID) {
          response_parcel = this.parcelService.updateParcel(this.propertyId, this.propertyParcel.ParcelID, reqBody);
        } else {
          const parcelList = [];
          parcelList.push(reqBody);
          response_parcel = this.parcelService.saveParcel(this.propertyId, parcelList);
        }
        response_parcel.subscribe((result: ApiResponseParcelPropertyDTO) => {
          if (result.error) {
            this.notificationService.ShowErrorMessage(result.message);
          } else if (result.status === HttpStatus.OK) {
            this.onClose.emit();
          }
        }, error => {
          this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveOrUpdateParcel);
        });
      }
    }
  }

  DeleteParcelDetails() {
    this.parcelService.deleteParcel(this.propertyId, this.propertyParcel.ParcelID).subscribe((result: ApiResponseNull) => {
      if (result.error) {
        this.notificationService.ShowErrorMessage(result.message);
      } else {
        this.onClose.emit();
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToDeleteParcel);
    });
  }
}
