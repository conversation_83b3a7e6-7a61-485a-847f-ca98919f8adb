<div class="background-wrapper"></div>
<div class="updates-popup">
  <div class="popup-header">
    <div>
      <div class="wrapper"><div class="heading">What's New In {{version}}</div></div>
    </div>
    
    <button class="close-btn" (click)="closeWhatsNew()">×</button>
  </div>
  <div class="popup-body">
    <div class="carousel" [style.transform]="'translateX(' + -currentSlide * 100 + '%)'">
      <div class="update-slide" *ngFor="let update of updates">
        <div *ngIf="update.media.image" class="image-container">
          <div #imageWrapper 
          class="image-wrapper" 
          [class.image-wrapper-full-screen]="isFullScreen"
          [ngStyle]="{'background-image': 'url(' + update.media.image + ')'}" >
            <i *ngIf="!isFullScreen" class="fas fa-expand expand-icon" (click)="toggleFullScreen(imageWrapper)"></i>
            <i *ngIf="isFullScreen" class="fas fa-compress minimize-icon" (click)="toggleFullScreen(imageWrapper)"></i>
          </div>
        </div>

        <div *ngIf="update.media.video">
          <video preload="metadata" controls [src]="update.media.video" alt="Update Video" class="update-video"></video>
        </div>

        <h3>{{ update.title }}</h3>

        <div class="description-wrapper">
          <p [innerHTML]="update.description"></p>
        </div>
      </div>
    </div>
  </div>

  <div class="navigation">
    <a *ngIf="currentSlide === updates.length - 1" 
           href={{releaseNoteLink}}
           target="_blank" 
           class="release-notes">
          See more about the release and videos here  <i class="fas fa-link"></i>
        </a>
        <div class="buttons-group">
          <button (click)="prevSlide()" [disabled]="currentSlide === 0">Previous</button>
          <button (click)="handleNextBtnClick()">
            {{ currentSlide === updates.length - 1 ? 'Done' : 'Next' }}
          </button>
  </div>
</div>
