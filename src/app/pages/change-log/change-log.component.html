<div class="form-group row expandBox">
    <div class="col-md-12 p-0">
        <button type="button" class="searchBtnActions selectMoreToggle" data-toggle="collapse" data-target="#Changelog" data-testId="change-log-toggle"
            aria-expanded='false'><i  class="fa fa-history"></i>Change Log
        </button>
        <div id="Changelog" class="collapse mb-2 mt-2 pl-2 pr-2">
            <hr class="mt-0">
            <mat-tab-group [(selectedIndex)]="selectedTab" (selectedTabChange)="changeTab($event)">
                <mat-tab [label]="changelogTabs?.Location">
                    <app-changelog-modal [changelogType]="changelogType" [parentId]="propertyId" *ngIf="isLocation" [location]="location" [fetchOldChangeLog]="true" [fetchNewChangeLog]="false" class="change-log">
                    </app-changelog-modal>
                </mat-tab>
                <mat-tab [label]="changelogTabs?.PropertyDetails">
                    <app-changelog-modal [changelogType]="changelogType" [parentId]="propertyId" *ngIf="isProperty" [location]="location" [fetchOldChangeLog]="true" [fetchNewChangeLog]="false" class="change-log">
                    </app-changelog-modal>
                </mat-tab>
                <mat-tab [label]="changelogTabs?.ResearchStatusHistory">       
                    <app-research-status-history [parentId]="propertyId" *ngIf="isResearchStatus" class="change-log">
                    </app-research-status-history >
                </mat-tab>
                <mat-tab [label]="changelogTabs?.NewPropertyChangeLog" data-testId="property-changelog">
                    <app-changelog-modal [changelogType]="changelogType" [parentId]="propertyId" *ngIf="isNewPropertyChangeLog"
                        [fetchOldChangeLog]="false" [fetchNewChangeLog]="true" [location]="location" class="change-log">
                    </app-changelog-modal>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</div>
