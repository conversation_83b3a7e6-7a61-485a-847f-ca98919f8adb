import { FormControl, FormGroup } from '@angular/forms';
import { environment } from '../../environments/environment';
import { ResearchType } from '../enumerations/researchType';
import { formatDateForDatePicker, getFormattedDate } from '../utils';
import { fieldUnitMap } from '../common/constants';
import { AddressDTO, ApiResponsePropertyDetailsSizeResponseDTO, LocationDTO, LoginUserInfoDTO, PropertyDetailsDTO, PropertyDetailsSizeResponseDTO } from '../api-client';
import { PropertyService } from '../services/api-property.service';
import { mapEditPropertyDTO } from '../DTO/mapEditPropertyDTO';
import { UseTypes } from '../enumerations/useTypes';
import { PropertyDetails } from '../models/PropertyDetails';
import { RollupObject } from '../models/rollupObject';

export function processPropertyResearchStatus(data: any[]): any[] {
  const researchTypeIcons = {
    [ResearchType.NeedsResearch]: environment.MapIconNeedsResearch,
    [ResearchType.BaseComplete]: environment.MapIconBaseResearchComplete,
    [ResearchType.FieldResearchComplete]: environment.MapIconFieldResearchComplete,
    [ResearchType.Hidden]: environment.MapIconHidden,
    [ResearchType.ExpressComplete]: environment.MapIconExpressComplete,
    [ResearchType.ExpressIncomplete]: environment.MapIconExpressIncomplete,
    [ResearchType.NotStarted]: environment.MapIconNotStarted
  };

  return (data || [])
    .sort((a, b) => Number(a.Sequence) - Number(b.Sequence))
    .map(status => ({
      ...status,
      IsActive: false,
      PropertyResearchStatusID: 0,
      ResearchStatusPin: researchTypeIcons[status.PropertyResearchTypeID] || environment.MapIconNotStarted
    }));
}

export function preparePropertyData({ propertyCopy, property, isAnExistingProperty, hasNoExistingParcelInTileLayer, CountryId, propertyService, initialDetails, isMultiStrata,
  addressTypeValues, streetPrefixes, streetSufixes, quadrants} : {
    propertyCopy: PropertyDetailsDTO, property: PropertyDetails, isAnExistingProperty: boolean, hasNoExistingParcelInTileLayer: boolean, CountryId: number,
    propertyService: PropertyService, initialDetails: mapEditPropertyDTO, isMultiStrata: boolean, addressTypeValues: any, streetPrefixes: any[], streetSufixes: any[], quadrants: any[] }) {
  const convert = (from: string, to: string, value: number) =>
    propertyService.convertUnit(CountryId, from, to, value);

  const sqmToSfFields = [
    'BuildingSF', 'LotSizeSF', 'TotalAnchorSF', 'OfficeSF', 'RetailSF',
    'SmallestFloor', 'LargestFloor', 'HardstandArea', 'TypicalFloorSize',
    'BuildingNRA', 'ContributedGBA_SF', 'Mezzanine_Size_SF', 'Awnings_Size_SF',
    'GLAR_SF', 'GLA_SF',
  ];
  const keysToDelete = ['Awnings_Size_SM', 'BuildingSizeSM', 'BuildingSizeSMFormatted', 'ClearHeightMaxM', 'ClearHeightMinM', 'ColumnSpacingLenM',
    'ColumnSpacingWidthM', 'DepthM', 'GLA_SM', 'GLAR_SM', 'LotSizeACSM', 'LotSizeSM', 'LotSizeSMFormatted', 'Mezzanine_Size_SM', 'NLA_SM',
    'OfficeSM', 'ParcelNumber', 'RetailSM', 'TypicalFloorSizeSM', 'WidthM', 'ContributedGBA_SM', 'SmallestFloorSM', 'LargestFloorSM',
    'RetailFrontageM', 'ClassTypeName', 'TenancyName', 'CondoTypeName', 'BldgSizeSourceName', 'EnergyStarRatingName', 'WaterStarRatingName',
    'GreenStarRatingName', 'ConstructionTypeName', 'HVACTypeName', 'SprinklerTypeName', 'ConstructionStatusName', 'BuildSpecStatusName', 'RoofTypeName',
    'GovernmentInterestName', 'PowerTypeName', 'HardstandAreaSourceName', 'NRASizeSourceName', 'ContributedGBASizeSourceName', 'GLASizeSourceName', 'GLARSizeSourceName', 'LotSizeSourceName'];

  keysToDelete.forEach(field => {
    if (field in propertyCopy) {
      delete propertyCopy[field];
    }
  });

  sqmToSfFields.forEach(field => {
    if (property[field] !== undefined) {
      propertyCopy[field] = convert('SqM', 'SF', property[field]);
    }
  });

  const mToFtFields = [
    'ColumnSpacingLen', 'ColumnSpacingWidth', 'BayWidth', 'BayDepth',
    'RetailFrontage', 'ClearHeightMin', 'ClearHeightMax', 'Depth', 'Width'
  ];

  //  convert unit function is in reverse 
  mToFtFields.forEach(field => {
    if (property[field] !== undefined) {
      propertyCopy[field] = convert('ft', 'M', property[field]);
    }
  });

  propertyCopy.HasNoExistingParcelInTileLayer = isAnExistingProperty ? property.HasNoExistingParcelInTileLayer : hasNoExistingParcelInTileLayer ? true : false;

  const dateFields = [
    'ConstructionStartDate', 'EstCompletionDate',
    'ActualCompletion', 'TitleReferenceDate', 'BookValueDate'
  ];

  dateFields.forEach(dateField => {
    propertyCopy[dateField] = propertyCopy[dateField]?.singleDate?.formatted ?? undefined;
  });

  if (propertyCopy.SpecificUseID === 0) {
    propertyCopy.SpecificUseID = undefined;
  }
  if (!property?.Location?.RooftopSourceID && !isAnExistingProperty) {
    propertyCopy.Location.RooftopSourceID = LocationDTO.RooftopSourceIDEnum.Researcher;
  }

  ['PrimaryTrafficCountDate', 'SecTrafficCntDate'].forEach(dateField => {
    if (property?.Address?.[dateField]) {
      propertyCopy.Address[dateField] = property.Address[dateField]?.singleDate?.formatted;
    }
  });

  if (!isAnExistingProperty) {
    propertyCopy.Address.StreetPrefix2 = propertyCopy.Address.StreetPrefix2;

    if ((property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.Strata ||
        property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.ChildFreehold) && !isMultiStrata) {
      propertyCopy.PropertyName = `${property.CondoUnit}/${property.PropertyName}`;
      property.PropertyName = propertyCopy.PropertyName;
    }

    if (!property?.CondoTypeID && property?.UseTypeID !== UseTypes.Land) {
      property.CondoTypeID = PropertyDetailsDTO.CondoTypeIDEnum.NotStrata;
      propertyCopy.CondoTypeID = PropertyDetailsDTO.CondoTypeIDEnum.NotStrata;
    }

    if (!property?.ConstructionStatusID && property?.UseTypeID !== UseTypes.Land) {
      property.ConstructionStatusID = PropertyDetailsDTO.ConstructionStatusIDEnum.Existing;
      propertyCopy.ConstructionStatusID = PropertyDetailsDTO.ConstructionStatusIDEnum.Existing;
    }

    if (!property?.LotSizeSourceID && (property?.LotSizeSF)) {
      property.LotSizeSourceID = PropertyDetailsDTO.SizeSourceIDEnum.CountyDataSource;
      propertyCopy.LotSizeSourceID = PropertyDetailsDTO.SizeSourceIDEnum.CountyDataSource;
    }
  }

  if (initialDetails.selectedParcel?.ShapeID) {
    property.Location.GISShapeID = initialDetails.selectedParcel.ShapeID;
  }
  propertyCopy.Location.GISShapeID = property.Location.GISShapeID;
}

export function getCondoType(condoTypeID: PropertyDetailsDTO.CondoTypeIDEnum): string {
  switch (condoTypeID) {
    case PropertyDetailsDTO.CondoTypeIDEnum.NotStrata:
      return 'Not Strata';
    case PropertyDetailsDTO.CondoTypeIDEnum.Strata:
      return 'Strata';
    case PropertyDetailsDTO.CondoTypeIDEnum.MasterStrataRecord:
      return 'Master Strata';
    case PropertyDetailsDTO.CondoTypeIDEnum.MasterFreehold:
      return 'Master Freehold';
    case PropertyDetailsDTO.CondoTypeIDEnum.ChildFreehold:
      return 'Freehold';
    default:
      return 'Unknown';
  }
}

export function preparePropertyFromResult({
  result,
  component,
  form,
  unitId,
  dateFormat,
  datePipe,
  sharedDataService,
  buildAddress,
  onPropertyFetched
}: {
  result: ApiResponsePropertyDetailsSizeResponseDTO,
  component: any,
  form: FormGroup,
  unitId: LoginUserInfoDTO.UnitIdEnum,
  dateFormat: string,
  datePipe: any,
  sharedDataService: any,
  buildAddress: () => void;
  onPropertyFetched: () => void;
}) {
  if (!result.error) {
    if (component.redirectionLoader) {
      component.redirectionLoader = false;
    }

    const property: PropertyDetailsSizeResponseDTO = result.responseData;
    const rollup = result.responseData.RollupObject;
    component.rollupMasterFreeholdFieldsObject = new RollupObject();

    component.property = { ...new PropertyDetails(), ...property };
    component.footPrintNotAvailable = !!property.HasNoBuildingFootprints;

    if (component.footPrintNotAvailable) {
      form.addControl('ContributedGBA_SF', new FormControl(undefined));
    }

    component.rollupMasterFreeholdFieldsObject = rollup;
    component.IsSkipped = property.IsSkipped;

    const isMetric = component.metricUnit === unitId;
    Object.keys(fieldUnitMap).forEach((key) => {
      const sourceField = fieldUnitMap[key][isMetric ? 'meter' : 'feet'];
      component.property[key] = property[sourceField];

      if (rollup) {
        component.rollupMasterFreeholdFieldsObject[key] = rollup[sourceField];
      }
    });

    component.previousPropertyName = property.PropertyName;
    sharedDataService.selectedFloor = property.Floors;

    if (rollup) {
      const dateFields = ['ConstructionStartDate', 'EstCompletionDate', 'ActualCompletion', 'TitleReferenceDate', 'BookValueDate'];
      dateFields.forEach(field => {
        const val = rollup[field] ?? '';
        const parts = val.split(' - ');
        if (parts.length === 2) {
          component.rollupMasterFreeholdFieldsObject[field] =
            `${getFormattedDate(parts[0], dateFormat)} - ${getFormattedDate(parts[1], dateFormat)}`;
        }
      });
    }

    const singleDateFields = [ 'ConstructionStartDate', 'EstCompletionDate', 'BookValueDate', 'ActualCompletion', 'TitleReferenceDate' ];
    singleDateFields.forEach(field => {
      const value = component.property[field];
      if (value && value !== '00/00/0000') {
        const formatted = datePipe?.transform(value, dateFormat, '+0000');
        component.property[field] = formatDateForDatePicker(value);
        component.property[field].singleDate.formatted = formatted;
      }
    });

    const addressDateFields = [ 'PrimaryTrafficCountDate', 'SecTrafficCntDate' ];
    addressDateFields.forEach(field => {
      const value = component.property.Address[field];
      if (value && value !== '00/00/0000') {
        const formatted = datePipe?.transform(value, dateFormat, '+0000');
        component.property.Address[field] = formatDateForDatePicker(value);
        component.property.Address[field].singleDate.formatted = formatted;
      }
    });

    component.property.Address.AddressType = !!property.Address?.AddressType;
    component.updateAdditionalUsesList();
    component.propertyCopy = JSON.parse(JSON.stringify(component.property));

    if (component.isAnExistingProperty) {
      component.selectedFloor = property.Floors;
      sharedDataService.selectedFloor = property.Floors;
      component.selectedUseTypeID = property.UseTypeID;
      const previousUseType = property.UseTypeID;
      component.onPropertyUseChange(false, property.UseTypeID || 5, previousUseType);
      component.resetControls();
      if (component.propertyParcelList?.length) {
        component.property.ParcelNumber = component.propertyParcelList[0].ParcelNo;
      }
    } else {
      component.selectedFloor = property.Floors;
      component.selectedUseTypeID = property.UseTypeID;
      sharedDataService.selectedFloor = property.Floors;
    }
    component.setFloorOptions();
    component.showMapModal(false, undefined, undefined, false);
    form.get('PropertyTypeName')?.setValue(component.property.UseTypeID);
    buildAddress();
    onPropertyFetched();
  }
}

export interface BuildAddressParams {
  address: AddressDTO;
  streetSuffixes: { StreetSuffix1: any; Suffix: string }[];
  cities: { CityID: any; CityName: string }[];
  states: { StateID: any; StateAbbr: string }[];
}

export function buildAddressDetails({
  address,
  streetSuffixes,
  cities,
  states
}: BuildAddressParams): {
  StreetNumber: string;
  StreetSuffix1Text?: string;
  CityName?: string;
  StateName?: string;
} {
  let StreetNumber = '';
  if (address.StreetNumberMin && !address.StreetNumberMax) {
    StreetNumber = address.StreetNumberMin;
  } else if (!address.StreetNumberMin && address.StreetNumberMax) {
    StreetNumber = address.StreetNumberMax;
  } else if (
    address.StreetNumberMin &&
    address.StreetNumberMax &&
    address.StreetNumberMin === address.StreetNumberMax
  ) {
    StreetNumber = address.StreetNumberMin;
  } else if (
    address.StreetNumberMin &&
    address.StreetNumberMax &&
    address.StreetNumberMin !== address.StreetNumberMax
  ) {
    StreetNumber = `${address.StreetNumberMin}-${address.StreetNumberMax}`;
  }

  const StreetSuffix1Text = address.StreetSuffix1
    ? streetSuffixes.find(item => item.StreetSuffix1 === address.StreetSuffix1)?.Suffix
    : undefined;

  const CityName = address.CityID
    ? cities.find(item => item.CityID === address.CityID)?.CityName
    : undefined;

  const StateName = address.StateID
    ? states.find(item => item.StateID === address.StateID)?.StateAbbr
    : undefined;

  return {
    StreetNumber,
    StreetSuffix1Text,
    CityName,
    StateName
  };
}
