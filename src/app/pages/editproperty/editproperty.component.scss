@import url('../../../assets/css/radio-btn-styles.css');
.header-container {
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
}

.text-align-center {
    text-align: center;
    margin: 0;
}

.container {
    font-size: 13px;
    padding-top: 0.5rem;
    padding-left: 0.5rem;
    max-height: 80vh;
}

.expand-btn {
    margin-bottom: 15px;
}

.container label {
    margin-bottom: 0;
}

.mat-tab-group {
    margin-left: 0.5rem;
}

.page_sub_head1 button.expand-btn,
.page_sub_head1 button {
    margin-bottom: 0;
    padding: 2px 10px;
}
.noteText{
    position: relative;
    color: #f00;
    font-size: 11px;
}
#parcel-data,
#legal-data,
#researchStatus,
#office-data,
.office-container {
    padding-top: 15px;
}

.expand-btn i.fa-minus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-plus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-minus {
    display: inline-block;
}

.container-align {
    margin-left: 10px;
}

.form-align {
    background-color: white;
    padding: 15px 0;
    padding-top: 0;
}

.mat-tab-label,
.mat-tab-link {
    font-size: 18px !important;
    font-weight: bold;
}

.page_sub_head1 {
    display: flex;
    font-size: 17px;
    background: var(--primary-white);
    color: var(--primary-blue-color);
    margin-top: 0 !important;
    width: 100%;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: -12px;
}
.checkAbslt{
    position: absolute;
    right: 8px;
    top: 6px;
}

.mat-tab-body {
    background-color: white !important;
}

.top-area {
    float: left;
    width: 100%
}

input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;

}

.lbl-propid {
    font-weight: bold;
    font-size: 1rem !important;
}

div.wrapper {
    position: relative;
    clear: both;
    width: 100%;
}

div.left {
    float: left;
    width: 40%;
    background: #ffffff;

    vertical-align: middle;
    height: 38px;
    line-height: 38px;
}

div.right {
    float: right;
    width: 10%;
    background: #ffffff;
    height: 38px;
}

.research-stat-editor {
    float: right;
    width: 50%;
    background: #ffffff;

    vertical-align: middle;
    height: 38px;
    line-height: 38px;
}

.mandatory {
    color: #FF0000;
}

@media only screen and (min-width : 992px) {
    .heightAdjust {
        height: 312px;
    }
}

.background-white{
    background-color: #fff !important;
}

.input-with-icon {
    position: relative;
}

.input-with-icon:hover i {
    color: #d94624;
}

.input-with-icon i {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
}

.mapBox {
    position: absolute;
    right: 15px;
    top: 8px;
    width: 8%;

    img {
        width: 100%
    }
}
.map_pin{
    top: 4%;
    position: absolute;
    left: 35%;
    color: #00b7ff;
    cursor: pointer;
}
.map_pin i{
    color: rgb(255, 217, 0);
}
.swichingRow{
    display: inline-block;  margin-left: 11%;text-transform: none;
    .stchText{width:80px; display: inline-block; position: relative;}
    .slider{
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #5bbd74;
        transition: .4s;
    }

}
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 20px;
  }
  .switch input {display:none;}

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 30px;
    width: 30px;
    left: 0px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    top: -4px;
    box-shadow:  0px 0px 10px rgba(0, 0, 0, 0.5);
  }
  
  input:checked + .slider {
    background-color: #4dbd74;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px #4dbd74;
  }
  
  input:checked + .slider:before {
    -webkit-transform: translateX(35px);
    -ms-transform: translateX(35px);
    transform: translateX(35px);
  }
  /* Rounded sliders */
  .slider.round {
    border-radius: 30px;
  }
  .slider.round:before {
    border-radius: 50%;
  }
  .red-text{
    color: #FF0000;
}
.green-text td{
    color: #45912c;
}
.add-allocation-btn {
    color: #fff !important;
    background: var(--primary-blue-color) !important;
    border: 1px solid var(--primary-blue-color) !important;
}
.btn-primary:disabled {
    background-color: #b0aeae !important;
    border-color: #838383 !important;
    color: #3f3f3f !important;
}
.icons-container {
    display: flex;
    flex-direction: column;
    height: 40vh;
    justify-content: space-between;
}

.text-align-center {
    text-align: center;
    margin: 0;
}

.container {
    font-size: 13px;
    padding-top: 0.5rem;
    padding-left: 0.5rem;
    max-height: 80vh;
}

.expand-btn {
    margin-bottom: 15px;
}

.container label {
    margin-bottom: 0;
}

.mat-tab-group {
    margin-left: 0.5rem;
}

.noteText {
    position: relative;
    color: #f00;
    font-size: 11px;
}

#parcel-data,
#legal-data,
#researchStatus,
#office-data,
.office-container {
    padding-top: 15px;
}

.expand-btn i.fa-minus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-plus {
    display: none;
}

.expand-btn[aria-expanded="true"] i.fa-minus {
    display: inline-block;
}

.container-align {
    margin-left: 10px;
}

.form-align {
    background-color: white;
    padding: 15px 0;
    padding-top: 0;
}

.mat-tab-label,
.mat-tab-link {
    font-size: 18px !important;
    font-weight: bold;
}

.checkAbslt {
    position: absolute;
    right: 8px;
    top: 6px;
}

.mat-tab-body {
    background-color: white !important;
}

.top-area {
    float: left;
    width: 100%
}

input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;

}

.lbl-propid {
    font-weight: bold;
    font-size: 1rem !important;
}

div.wrapper {
    position: relative;
    clear: both;
    width: 100%;
}

div.left {
    float: left;
    width: 40%;
    background: #ffffff;

    vertical-align: middle;
    height: 38px;
    line-height: 38px;
}

div.right {
    float: right;
    width: 10%;
    background: #ffffff;
    height: 38px;
}

.research-stat-editor {
    float: right;
    width: 50%;
    background: #ffffff;

    vertical-align: middle;
    height: 38px;
    line-height: 38px;
}

.mandatory {
    color: #FF0000;
}

.refresh-left {
    margin-left: 169px;
}

.ref-top {
    margin-top: -26px;
}

.icon-pos {
    font-size: 40px;
    color: var(--primary-blue-color);
    cursor: pointer;
}

.icon-pos-close i {
    border-radius: 100%;
    background: #ccc;
    color: var(--primary-blue-color);
    font-weight: normal;
    text-align: center;
    line-height: 0;
    margin-top: 3px;
    padding: 21px 6px;
    font-size: 35px;
}

.icon-pos-close i:hover {
    background: var(--primary-blue-color);
    color: white;
}

.stickyTop {
    position: sticky;
    position: -webkit-sticky;
    top: 0px;
    left: 0px;
    background-color: white;
    z-index: 1;
}

.refresh-icon {
    position: absolute;
    right: 37px;
    top: 10px;
}

@media (min-width: 1700px) {
    .refresh-left {
        margin-left: 175px;
    }
}

@media (min-width:1800px) {
    .refresh-left {
        margin-left: 185px;
    }
}

@media (min-width:1900px) {
    .refresh-left {
        margin-left: 185px;
    }
}

@media (min-width:2000px) {
    .refresh-left {
        margin-left: 185px;
    }
}

@media (min-width:2700px) {
    .refresh-left {
        margin-left: 192px;
    }

}

@media (min-width:2200px) {
    .refresh-left {
        margin-left: 185px;
    }
}

@media only screen and (min-width : 992px) {
    .heightAdjust {
        height: 312px;
    }
}

.floorBox {
    padding-left: 30px;
}

@media only screen and (min-width: 1000px) and (max-width: 1131px) {
    .type-btn{
        min-width: 25px !important;
        height: 32px !important;
    }
}



@media only screen and (min-width: 1132px) and (max-width: 1250px) {
    .type-btn{
        min-width: 28px !important;
        height: 35px !important;
    }
}

@media only screen and (min-width: 1251px) and (max-width: 1318px) {
    .type-btn{
        min-width: 32px !important;
        height: 35px !important;
    }
}

@media only screen and (min-width: 1319px) and (max-width: 1427px) {
    .type-btn{
        min-width: 35px !important;
        height: 39px !important;
    }
}

@media only screen and (min-width: 1428px) and (max-width: 1600px) {
    .type-btn{
        min-width: 35px !important;
        height: 39px !important;
    }
}

.mapInput {
    width: 95%;
}

.floorsizeinput{
    width: 150px;
}

.floor-count {
    width: 45px;
    height: 39px;
}

.description{
    width: 250px;
}

.des-floor {
    gap: 1.3rem;
}


.floorcount {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.buildingInput {
    width: 91%;
}

.mapBox {
    position: absolute;
    right: 15px;
    top: 8px;
    width: 8%;

    img {
        width: 100%
    }
}

.map_pin {
    top: 4%;
    position: absolute;
    left: 35%;
    color: #00b7ff;
    cursor: pointer;
}

.map_pin i {
    color: rgb(255, 217, 0);
}

.floor-txt {
    width: 24% !important;
    float: right;
    margin-right: 45px;

}

.fl-lbl {
    margin-left: -4px;
}

.head-address {
    font-weight: normal;
    font-size: 16px;
    width:300px;
}

.head-lat {
    font-weight: normal;
    font-size: 12px;
    //     margin-left: 11px;
}

.box {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 4px 8px;
    margin: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: auto;
}

// .bordbox {
//     border: 1px solid #ccc;
//     border-radius: 10px;
// }

.research-icon {
    flex: 0 0 auto;
    margin-right: 10px;
    width: 17px; 
    height: 27px;
}

.research-status-icon{
    flex: 0 0 auto;
    margin-right: 10px;
    width: 20px;
    height: 30px;
}

.research-name {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 11px;
    display: flex;
    align-items: center;
}

.research-checkbox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

.comments-textbox {
    width: 300px;
    height: 6rem;
    border-radius: 9px;
    border: 1px solid var(--primary-blue-color);
}

.comments-checkbox {
    width: 50px;
    height: 40px;
    border-radius: 9px;
    border: 1px solid;
}

.save-button {
    width: 160px;
    height: 40px;
    border-radius: 9px;
    color: white;
    border: 1px solid var(--primary-blue-color);
    background: var(--primary-blue-color);
    margin-top: -45px;
}

.needs-research-more-info {
    border: 2px solid var(--primary-blue-color);
    display: flex;
    flex-direction: column;
    padding: 15px;
    margin-left: 20px;
}

.switch-container {
    flex: 0 0 auto;
    margin-left: 5px;
}

.research-container {
    width: 25%;
}

.add-floor-btn{
    float:right;
}

.deleteFloor{
    margin-right: 10px;
    font-size: 18px;
    color: red;
}

.invalidateFloor{
    color: red;
    font-size: 18px;
    padding-left: 10px;
}

.validateFloor{
    color: green;
    font-size: 18px;
    padding-left: 10px;
}


.divider {
    position: relative;
    margin-top: 5%;
    margin-left: 23%;
    height: 1px;
}

.div-transparent:before {
    content: "";
    position: absolute;
    top: 0;
    left: 5%;
    right: 5%;
    width: 60%;
    height: 1px;
    background-image: linear-gradient(to right, transparent, rgb(211, 211, 211), transparent);
}

.background-white {
    background-color: #fff !important;
}

.input-with-icon {
    position: relative;
}

.input-with-icon:hover i {
    color: #d94624;
}

.input-with-icon i {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
}

.research-button {
    flex: 0 0 auto;
    margin-right: 5px;
}

.save-and-next-btn {
    width: 187px;
}

.buttons-wrapper {
    display: flex;
    flex-direction: column;
    gap: 5px;
    justify-content: flex-end;
    left:20px;
    width: 180px;
    align-items: flex-end;
}
.save-cancel-wrapper {
    display: flex;
    gap: 5px;
}

.save-and-next-button {
    width: 140px;
}

.skip-button-position {
    display: flex;
    align-items: center;
    gap: 8px;
    right: 150px;
    position: absolute;
    top: 50px;
    z-index: 10;
}

.no-padding-right {
    padding-right: 0;
}
.edit-allocation-btn {
    background-color: #0070c0;
    color: white;
    border: 1px solid #0070c0;
    font-weight: 600;
}
.delete-allocation-btn {
    background-color: #c00000;
    color: white;
    border: 1px solid #c00000;
    font-weight: 600;
}
.change-log-allocation-btn {
    background-color: #002060;
    color: white;
    border: 1px solid #002060;
    font-weight: 600;
}
.backDropWrapper{
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
}


.spinRotate{
    font-size: 45px;
    position: absolute;
    left: 68%;
    top: 38%;
    color: #fff;
}
.icon {
    height: 25px;
    width: 25px;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
    cursor: pointer;
  }
.floor-data {
    display: flex;
}
.specific-use-wrapper {
    padding-left: 0;
}
.add-floor-btn {
    color: white;
    background-color: var(--primary-blue-color);
    border: var(--primary-blue-color);
    box-shadow: none;
}
.icons-style {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
}
.building-label {
    display: flex;
    gap: 2px;
    align-items: center;
}
.info-icon {
    font-size: 15px !important;
    margin-left: 8px;
    color: #92bdea;
    height: 14px;
    width: 14px;
}
.highlight {
    background-color: rgb(245, 245, 224);
    padding: 8px;
}
.add-polygon {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-left: -20px;
}
.polygons-accordion {
    width: 95%;
    margin: 0 15px 10px;
}
.add-floor-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
}
.default-property-use {
    display: flex;
    margin-left: 10px;
}
.building-sqm {
    margin-left: 15px;
}
.research-status>* {
    width: auto;
}
.lot-parcel-wrapper {
    margin-left: 0;
}
.add-bldgs {
    background: #4180c3 !important; 
    border-color: #4180c3 !important;
    color: white !important;
    margin-top: 23px;
    line-height: 32px;
    border-radius: 0.6rem;
}

.details-wrapper {
    display : flex;
    justify-content: flex-end;
    font-weight: normal;
    font-size: 16px;
    color:#4180c3;
}

.freehold-btns-wrapper{
    display:flex;
    gap:20px;
}

.review-details-wrapper {
    display: flex;
    justify-content:flex-end;
    gap: 10px;
    color:#4180c3;
}

.reviewed {
    background-color: #1e4b7b !important;
    box-shadow: none;
    color:white !important;
}

.copy-floor-btn {
    font-size: 14px;
    color: white;
    background-color: var(--primary-blue-color);
    border: var(--primary-blue-color);
    box-shadow: none;
}
.icon-wrapper {
    display: flex;
    gap: 10px;
}
td {
    vertical-align: middle;
}
.footprint-btn-wrapper {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}
.no-footprint-label {
    background: orange;
    padding: 0 10px;
    border-radius: 15px;
    color: white;
}
