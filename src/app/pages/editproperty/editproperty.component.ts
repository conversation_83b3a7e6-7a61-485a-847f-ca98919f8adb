// Angular Core and Common Modules
import { Component, OnInit, Output, Input, OnDestroy, NgZone, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { EventEmitter } from '@angular/core';
import { DatePipe } from '@angular/common';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
// Third-party Libraries
import { Subscription } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import AES from 'crypto-js/aes';
import encUtf8 from 'crypto-js/enc-utf8';
// Application Constants and Enums
import { CommonStrings, DefaultDateFormat, DefaultUnitDisplayTextSize } from '../../constants';
import { EnumApplication } from '../../enumerations/application';
import { EditPropertyTabs } from '../../enumerations/editPropertyTabs';
import { EnumCondoTypeNameFromTiles } from '../../enumerations/condoType';
import { LocationFormControls } from '../../enumerations/locationFormControls';
import { NavigationPreferences } from '../../enumerations/searchGridNavigationTypes';
import { OfficeControls } from '../../enumerations/officeControlKeys';
import { PropertyFormControls } from '../../enumerations/propertyFormControls';
import { ResearchType } from '../../enumerations/researchType';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { UseTypes } from '../../enumerations/useTypes';
import { IndexedDBCollections, MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { HttpStatus } from '../../enumerations/http-status-codes';
// Models
import { ConstructTypeStatus, MultiPolygon } from '../../models/Common';
import { Country } from '../../models/Country';
import { County } from '../../models/County';
import { ParkingSpace } from '../../models/ParkingSpace';
import { PropertyDetails } from '../../models/PropertyDetails';
import { RollupObject } from '../../models/rollupObject';
import { PropertyResearchStatus } from '../../models/PropertyResearchStatus';
import { MediaInfoDTO } from '../../models/media-dto';
import { ContactMedia } from '../../models/ContactMedia';
// DTOs
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
// Services
import { AddFloorService } from '../../services/add-floor.service';
import { AddressService } from '../../services/address.service';
import { BuildingFootPrintService } from '../../services/building-footprint.service';
import { CommunicationModel, CommunicationService } from '../../services/communication.service';
import { IndexedDBService, IIndexedDBMedia } from '../../services/indexeddb.service';
import { LoginService } from '../../services/login.service';
import { MapService } from '../../modules/map-module/service/map-service.service';
import { MetaDataIndexedDBService, IMetaData } from '../../services/indexed-db-service.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { PropertyService } from '../../services/api-property.service';
import { SharedDataService } from '../../services/shareddata.service';
import { StagingIndexedDBService } from '../../services/indexeddb-staging.service';
import { ParcelService } from '../../services/parcel.service';
import { PropertyTrackingService } from '../../services/property-tracking.service';
// Modules and Components
import { LatLng } from '../../modules/map-module/models/LatLng';
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
import { confirmSettings, dialogConfirmSettings } from '../../modules/notification/models/confirmSettings';
import { StrataComponent } from '../strata/strata.component';
import { PropertyAllocationDetailsComponent } from '../../components/common/property-allocation-details/property-allocation-details.component';
// Environment
import { environment } from '../../../environments/environment';
// Application Constants/Utils
import { BindFormControlToPropertyVariable, BindLookupNameToVariable, IndustrialYesNoFields, YesNoFields } from '../../common/constants';
import { buildAddressDetails, getCondoType, preparePropertyData, preparePropertyFromResult, processPropertyResearchStatus } from '../core.utils';
import { getFormNameByUseType, getRange, numericValidator, setPropertyName, validateBuildingSize, validateIntegerInput, validatePasteInput } from '../../utils';
// API Client
import { ApiResponseListBuildingFootPrintDTO, ApiResponseParcelPropertyDTO, ApiResponsePropertyDetailsSizeResponseDTO,
  ApiResponsePropertyResponseDTO, ApiResponseVoid, BuildingFootprintCollectionRequestDTO, BuildingFootPrintRequestDTO,AuditLogEntityTypes,
  ApiResponseListPropertyStrataDetailsDTO, ParcelPropertyDTO, ParcelPropertyRequestDTO, PropertyDetailsDTO, 
  MediaDTO, CityRequestDTO, ApiResponseCity, 
  NotesResponseDTO,
  ApiResponseListParcelPropertyDTO,
  MultipleChildRequestDTO,
  LoginUserInfoDTO} from '../../api-client';

const CondoTypeEnum = PropertyDetailsDTO.CondoTypeIDEnum;
const ConstructionStatusEnum = PropertyDetailsDTO.ConstructionStatusIDEnum;
const SizeSourceEnum = PropertyDetailsDTO.SizeSourceIDEnum;
const LotSizeSourceEnum = PropertyDetailsDTO.LotSizeSourceIDEnum;
@Component({
  selector: 'app-editproperty',
  templateUrl: './editproperty.component.html',
  styleUrls: ['./editproperty.component.scss']
})
export class EditpropertyComponent implements OnInit, OnDestroy {
  @ViewChild('strataAndFreeholdPids', { static: false }) strataAndFreeholdPids: StrataComponent;
  @ViewChild('propertyAllocation') propertyAllocation: PropertyAllocationDetailsComponent;
  @Input() tabSelected;
  @Input() userLat;
  @Input() userLong;
  @Input() isStreetView;
  @Input() isAnExistingProperty;
  @Input() hasStreetView: boolean;
  @Input() isParcelLayerEnabled: boolean;
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() FloorSize: any;
  @Input() oldPolygon: any;
  @Input() editedFootprintID: any;
  @Input() newPropertyLocation: LatLng;
  @Input() floorPolygon: any;
  @Output() clearMultiStrataObj: EventEmitter<any> = new EventEmitter<any>();
  @Output() openStreetViewClicked = new EventEmitter<void>();
  @Output() openAerialView = new EventEmitter<void>();
  @Input() aerialCanvas: ElementRef;
  @Input() aerialViewCaptureDiv: ElementRef;
  @Output() onComplete: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() setSelectedPropertyCondoType: EventEmitter<PropertyDetailsDTO.CondoTypeIDEnum> = new EventEmitter<PropertyDetailsDTO.CondoTypeIDEnum>();
  @Output() fetchProperty: EventEmitter<any> = new EventEmitter<any>();
  @Output() fetchStrataProperty: EventEmitter<string> = new EventEmitter<string>();
  @Output() fetchNextProperty: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewChildUnitsToMaster: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewFreeholdUnitToMaster: EventEmitter<any> = new EventEmitter<any>();
  private isPropertyDirtyListner: Subscription;
  private mediaShowFileUploadModal: Subscription;
  private notesModal: Subscription;
  private imageViewerModal: Subscription;
  private updateFormSubscription: Subscription;
  private updatePropertyAlocations: Subscription;
  floorShapeChangeListener: Subscription;
  updateParcelSizeAndCountListener: Subscription;
  propertySaveOnParcelChangeListener: Subscription;
  private _zone: NgZone;
  private _communicationService: CommunicationService;
  private _propertyService: PropertyService;
  private _addressService: AddressService;
  private _loginService: LoginService;
  private _addFloorService: AddFloorService;
  private _notificationService: NotificationService;
  _sharedDataService: SharedDataService;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  IndexedDBService: IndexedDBService;
  StagingIndexedDBService: StagingIndexedDBService
  PropertyFormControlsEnum = PropertyFormControls;
  selectedTab: number = EditPropertyTabs.Property;
  propertyUseTypes = UseTypes;
  EnumCondoTypeNames = CondoTypeEnum;
  propertyTypeValues = UseTypes;
  LoginUserInfoDTO = LoginUserInfoDTO;
  validateIntegerInput = validateIntegerInput;
  validatePasteInput = validatePasteInput;
  pRange: { StrataMin: string, StrataMax: string } = { StrataMin: '', StrataMax: '' };
  property: PropertyDetails = new PropertyDetails();
  rollupMasterFreeholdFieldsObject: RollupObject = new RollupObject();
  propertyDetails: any;
  public propertyCopy: PropertyDetails = new PropertyDetails();
  countryList: Array<Country>;
  counties: Array<County>;
  public propertyResearchStatus: Array<PropertyResearchStatus>;
  propertyParcelList: Array<ParcelPropertyDTO>;
  selectedParcel: ParcelPropertyDTO;
  multifloors: MultiPolygon[] = [];
  files: MediaInfoDTO[] = [];
  mediaTitle = "Media Upload";
  selectedNote: NotesResponseDTO;
  notesTitle = "Notes";
  floorOptions: { key: string, value: number }[] = [{ key: "G", value: 1 }];
  public addressTypeValues: any = { Address: 0, Intersection: 1 };
  SearchType = 2;
  parkingPolygon: { singleSlotPolygon: ParkingSpace, parkingAreaPolygon: ParkingSpace[], parkingSpaces: number } = { singleSlotPolygon: { polygonId: null, area: 0, polygon: null }, parkingAreaPolygon: [], parkingSpaces: null };
  retailFrontagePolyline: { distance: number, polyline: any };
  hardstandAreaPolygon: { area: number, polygon: any };
  polygonToBeCopied: MultiPolygon;
  location: LatLng;
  stagingMedia: IIndexedDBMedia[] = [];
  rangeForm: FormGroup;
  propertyForm: FormGroup;
  showNoteModal: boolean = false;
  isPropertyDetailsExpanded: boolean = false;
  showDeletePolygonModal = false;
  buildingFootPrintIdToBeDeleted: any;
  showParcelWindow: boolean;
  selectedParcels: any[] = []
  showParcelDetails = false;
  selectedParcelInfo = null;
  parcelInformation: any;
  ShowProperties: boolean = false;
  disableStrataBtn = false;
  isMultiStrata = false;
  StrataMinMaxError = false;
  StreetSuffix1Text = "";
  CityName = "";
  StateName = "";
  StreetNumber: any = null;
  isFreehold = false;
  selectedFloor: any;
  selectedUseTypeID: any;
  activeGridRowNumber: any;
  isNavigationFromSearch: any;
  visitedPropertyIds: any = [];
  currentPropertyRowNumber: any;
  isAddPolygonButtonDisabled: boolean = false;
  pageDetails: any;
  strataDisplayNumber: any;
  editedPropertyIds: any = [];
  visitedStrataIdsFromSS: any;
  editedStrataIds: any = [];
  floorSizeArea: any = [];
  navigationPreference: string;
  selectedMasterParcel: any;
  LastStrataUnit: number;
  isFloorBtnDisable: boolean = false;
  IsSkipped: boolean = false;
  latLong: { lat: number, lng: number }  = {} as { lat: number, lng: number };
  showPropertyNameModal: boolean = false;
  isAutoClose: boolean;
  aggregateParcelSize: number = 0;
  parcelCount: number = 0;
  showMap: boolean = false;
  allocationsAndUses = false;
  additionalSpecificUseList: any = {};
  buildingSizeGBACopy = 0;
  buildingSizeNLACopy = 0;
  BldgSizeSourceIDCopy = null;
  roleID: string;
  IsAddStrataProperty = false;
  showChangeLog = false;
  showParkingSpace = false;
  isDeleteFloor = false;
  changelogType = '';
  parentId: number;
  isOffice: boolean;
  isRetail: boolean;
  isIndustrial: boolean;
  quadrants: any;
  streetPrefixes: any;
  streetSufixes: any;
  states: any;
  cities: any;
  condos: any;
  propertyTypes: any;
  additionalUseTypes: any;
  constructStatuses: any;
  roofTypes: any;
  sizeSource: any;
  constructTypes: any;
  validationError: boolean = false;
  CountryId: number;
  EntityID: number;
  specificUses: any;
  public sprinklerTypes: any;
  isNewProperty: boolean = true;
  displayPropertyId: any;
  zoningClasses: any;
  officeId: string = "";
  selectedSuffix: string = "";
  yearBuildError: boolean = false;
  yearRenovatedError: boolean = false;
  minFloorError: boolean = false;
  maxFloorError: boolean = false;
  streetMinMaxError: boolean = false;
  buildingSFError: boolean = false;
  clearHeightMinError: boolean = false;
  noOfOfficeFloorError: boolean = false;
  previousPropertyName: string = ""; 
  shouldUpdateLotSize = false;  
  isAustralia: boolean = false;
  floorlabels: boolean = false;
  dateFormat: string;
  showFileUpload: boolean = false;
  isFullSizeView: boolean = false;
  imageUrls: string[];
  ImageIndex: number;
  private countryCode: any;
  private stateCode: string;
  allspecificuses: any;
  public NABERSList: any;  
  showMasterStrataAlert = false;
  UnitId: LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;
  metricUnit : LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;  
  DataArray: Array<any> = [];
  isListing = false;
  isNotes = false;
  isMedia = false;
  isStrata = false;
  selectedOption: number = null;
  redirectionLoader = false;
  floors: any[] = [];
  isPolygonCollapsed = true;
  floorsWithFootprint: any;
  UnitDisplayTextSize: any;
  isCityStateLoaded = false;
  researchStatusClicked = false;
  parcelTabClick = false;
  isInitialProperty: any;
  IsUsetypeChanged = false;
  initialPropertyForm: string;
  researchChanged: boolean = false;
  propertyLocationChanged = false;
  auditStatusList: any;
  isFootprintModified: boolean = false;
  addMultiFreeholdModal: boolean = false;
  childFreeholdMin: any;
  loaderMasterPIDAfterChildsVisit = false;
  GRESBScoreError: boolean = false;
  propertyLookups: any;
  propertyLookupsCopy: any;
  showCopyPolygonModal: boolean = false;
  hasNoExistingParcelInTileLayer: boolean = false;
  footPrintNotAvailable: boolean = false;
  showContributedFieldsPopup: boolean = false;
  fetchNewChangeLog: boolean = false;
  fetchOldChangeLog: boolean = false;
  enumChangeLogEntities = AuditLogEntityTypes;
  parcelNumber: string = undefined;
  showStrataOrFreeholdTab = false;
  
  set disableCountySelection(value: boolean) {
    if (value) {
      this.propertyForm.controls['County'].disable();
    } else {
      this.propertyForm.controls['County'].enable();
    }
  }
  set disableCitySelection(value: boolean) {
    if (value) {
      this.propertyForm.controls['City'].disable();
    } else {
      this.propertyForm.controls['City'].enable();
    }
  }

  constructor(propertyService: PropertyService,
    private _mapService: MapService,
    addFloorService: AddFloorService,
    loginService: LoginService,
    communicationService: CommunicationService,
    notificationService: NotificationService,
    zone: NgZone,
    addressService: AddressService,
    private sharedDataService: SharedDataService,
    private _datePipe: DatePipe,
    private buildingFootprintService: BuildingFootPrintService,
    private formBuilder: FormBuilder,
    private parcelService: ParcelService,
    private propertyTrackingService: PropertyTrackingService
    ,) {
    this._zone = zone;
    this._notificationService = notificationService;
    this._loginService = loginService;
    this._addFloorService = addFloorService,
    this._propertyService = propertyService;
    this._addressService = addressService;
    this._communicationService = communicationService;
    this._sharedDataService = sharedDataService;
    this.CountryId = this._loginService.UserInfo.CountryID;
    this.EntityID = this._loginService.UserInfo.EntityID;
    this.dateFormat = this._loginService.UserInfo.DateFormat;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.roleID = this._loginService.UserInfo.RoleID;
    this.IndexedDBService = new IndexedDBService();
    this.StagingIndexedDBService = new StagingIndexedDBService();
    this.init();
    this.UnitId = this._loginService.UserInfo.UnitId;
    this.metricUnit = LoginUserInfoDTO.UnitIdEnum.Metric;
    this.updateFormSubscription = this._communicationService.subscribe('updatePropertyForm').subscribe(result => {
      this.footPrintNotAvailable = false;
      this.isPolygonCollapsed = true;
      this.redirectionLoader = true;
      this._zone.run(() => {
        this.property = new PropertyDetails();
        this.propertyCopy = new PropertyDetails();
        this.sharedDataService.deleteBuildingFootPrintIds = [];
        this.multifloors = [];
          this.initialDetails = result.data;
          this.initData();
          if (this.initialDetails.propertyId != result.data.propertyId) {
            setTimeout(() => {
              if (this.tabSelected) {
                this.showStrataOrFreeholdTab = true;
                this.selectedTab = this.tabSelected
                this.isStrata = true;
              }
            }, 2000)
          }
      });
    });

    this.floorShapeChangeListener = this._communicationService.subscribe('floorShapeChange').subscribe(result => {
      if (result.data && this.property?.CondoTypeID !== CondoTypeEnum.MasterStrataRecord && this.property?.CondoTypeID !== CondoTypeEnum.MasterFreehold) {
        const { multiFloors, isUpdated, isClearPolygon } = result.data;
        this.isFootprintModified = isUpdated;
        this.multifloors = JSON.parse(JSON.stringify(multiFloors));
        this.getAdditionalUses();

        this.isDeleteFloor = isClearPolygon;
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
        if (isUpdated) {
          if (this.isAnExistingProperty) {
            this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
          }
          this.updateBuildingSqmOnFloorChange();
          const sortedList = multiFloors.sort((a, b) => {
            const floorSizeA = parseFloat(a.floorSize);
            const floorSizeB = parseFloat(b.floorSize);

            return floorSizeB - floorSizeA;
          });
          if (sortedList && sortedList[0] && sortedList[0].floorSize && parseFloat(sortedList[0].floorSize) > this.property.LargestFloor) {
            this.property.LargestFloor = sortedList[0].floorSize;
          }
        } else if (result.data) {
          const { multiFloors } = result.data;
          this.multifloors = JSON.parse(JSON.stringify(multiFloors));
          this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
        }
        this.updateAddPolygonButtonDisable();
      }
    });
    this.updateParcelSizeAndCountListener = this._communicationService.subscribe('updateParcelSizeAndCount').subscribe(result => {
      if (result.data ){
        this.aggregateParcelSize = result.data.size;
        this.parcelCount = result.data.count;
      }
    });

    this.propertySaveOnParcelChangeListener = this._communicationService.subscribe('propertySaveOnParcelChange').subscribe(result => {
      if (result.data){
        this.propertyForm.get('LotSizeSF').setValue(result.data.size);
        this.propertyForm.get('LotSizeSF').markAsDirty();
        this.propertySave(false);
      }
    });

  }

  groupByRetired(item) {
    return item.Retired;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isAnExistingProperty) {
      if (changes.initialDetails) {
        if (this.initialDetails.propertyId != changes.initialDetails.currentValue.propertyId) {
          this.getPropertyDetails(changes.initialDetails.currentValue.propertyId, () => undefined);
        }
        this.initialDetails = changes.initialDetails.currentValue;
        if (changes.initialDetails.currentValue.locationData) {
          this.stateCode = changes.initialDetails.currentValue.locationData.StateCode;
        }
      }
      if (changes.newPropertyLocation && changes.newPropertyLocation.currentValue) {
        this.propertyLocationChanged = true;
        this.propertyForm.get('locationDetailsForm')?.get('Latitude').markAsDirty();
        this.propertyForm.get('locationDetailsForm')?.get('Longitude').markAsDirty();
        this.property.Location.Latitude = changes.newPropertyLocation.currentValue.Latitude;
        this.property.Location.Longitude = changes.newPropertyLocation.currentValue.Longitude;
      }
    } else {
      this.isStreetView = changes.isStreetView ? changes.isStreetView.currentValue : this.isStreetView;
      if (changes.newPropertyLocation && changes.newPropertyLocation.currentValue) {
        this.propertyLocationChanged = true;
        this.property.Location.Latitude = changes.newPropertyLocation.currentValue.Latitude;
        this.property.Location.Longitude = changes.newPropertyLocation.currentValue.Longitude;
      }
      if (changes.initialDetails) {
        if (!this.initialDetails.propertyId) {
          this.metaDataIndexedDBService && this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
          localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
          this.multifloors = [];
        }
      }
    }
  }

  ngOnDestroy(): void {
    this.isPropertyDirtyListner?.unsubscribe();
    this.mediaShowFileUploadModal?.unsubscribe();
    this.notesModal?.unsubscribe();
    this.imageViewerModal?.unsubscribe();
    this.updateFormSubscription?.unsubscribe();
    this.updatePropertyAlocations?.unsubscribe();
    this.floorShapeChangeListener?.unsubscribe();
    this.updateParcelSizeAndCountListener?.unsubscribe();
    this.propertySaveOnParcelChangeListener?.unsubscribe();
    this._sharedDataService.propertyResearchStatus = [];
    this._sharedDataService.selectedPropertyParcel = [];
    this._sharedDataService.additionalAddressList = [];
    this._sharedDataService.propertyMedia = [];
    this.sharedDataService.deleteBuildingFootPrintIds = [];
    this.multifloors = [];
  }

  removeCommas(str: string): any {
    return str.replace(/,/g, '');
  }

  private init() {
    this.property = new PropertyDetails();
    this.property.UseTypeID = undefined;
    this.propertyResearchStatus = new Array<PropertyResearchStatus>();
    // Assign loggin user id.
    this.isPropertyDirtyListner = this._communicationService.subscribe('IsPropertyDirty').subscribe(result => {
      this.cancelConfirmation(false, () => {
        this.propertySave(false);
        this.allowMapToChangePinSelection(true, result.data);
      }, () => {
        this.allowMapToChangePinSelection(true, result.data);
      }, () => {
        this.allowMapToChangePinSelection(false, result.data);
      });
    });
    this.notesModal = this._communicationService.subscribe('showNoteModal').subscribe(result => {
      this.initialDetails = result.data.initialDetails;
      this.selectedNote = result.data.selectedNote;
      this.notesTitle = !!this.selectedNote ? 'Note Edit' : 'Add Note';
      this.showNoteModal = true;
    });
    this.mediaShowFileUploadModal = this._communicationService.subscribe('showFileUploadModal').subscribe(result => {
      this.initialDetails = result.data.initialDetails;
      this.files = result.data.files;
      this.mediaTitle = !!this.files[0].MediaID ? 'Media Edit' : 'Media Upload';
      this.showFileUpload = true;
    });
    this.imageViewerModal = this._communicationService.subscribe('imageViewer').subscribe(result => {
      this.imageUrls = result.data.imageUrls;
      this.ImageIndex = result.data.ImageIndex;
      this.isFullSizeView = true;
    });

    setTimeout(() => {
      this.showMapModal(true, this.userLat, this.userLong, false);
    }, 100);
  }

  private allowMapToChangePinSelection(isAllowed: boolean, result) {
    if (isAllowed) {
      result.callback(result.latlng, result.marker)
    }
  }

  updateMarketList(){
    let commModel = new CommunicationModel();
    commModel.Key = 'updateMarketList';
    commModel.data = {useTypeID: this.property.UseTypeID};
    this._communicationService.broadcast(commModel);
  }

  broadCastNewPropertyFetch(){
    let commModel = new CommunicationModel();
    commModel.Key = 'newPropertyFetched';
    this._communicationService.broadcast(commModel);
  }

  broadCastFetchChangeLog() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchChangeLog';
    commModel.data = this.property.PropertyID;
    this._communicationService.broadcast(commModel);
  }

  updateAdditionalUsesList() {
    // Excluding land and property general use for additonal use list
    this.additionalUseTypes = this.propertyTypes?.filter(type => type.UseTypeID !== UseTypes.Land && type.UseTypeID !== this.property.UseTypeID)
  }

  onSelectUseTypeButton(useType) {
    if (this.isAnExistingProperty) {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.property.UseTypeID = this.selectedUseTypeID;
      this.updateMarketList();
      this.onPropertyUseChange(true, this.selectedUseTypeID, previousUseType);
    } else {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.property.UseTypeID = this.selectedUseTypeID;
      this.changePropertyUse(useType, previousUseType);
    }
    this.getAdditionalUses();
    this.updateAdditionalUsesList();
    this.propertyForm.get('PropertyTypeName').setValue(this.property.UseTypeID)
  }

  changePropertyUse(event, previousUseType) {
    if (!!event) {
      this.getPropertySpecificUse()
      if ((event.UseTypeID === this.propertyTypeValues.Land) && this.multifloors.length>0) {
        this.multiFloorConfirmation(previousUseType);
      }
    }
  }

  updateMultiFloors() {
    this.isFootprintModified = true;
  }

  multiFloorConfirmation(previousUseType) {
    const okCallback = () => {
      this.multifloors = [];
      this.property.BuildingSF = 0;
      this.property.CondoTypeID = null;
      this.property.MasterPropertyId = null;
      this.property.CondoUnit = null;
      if (this.isAnExistingProperty) {
        //To trigger alloacations save
        this.isFootprintModified = true;
        this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
      }
      let commModel = new CommunicationModel();
      commModel.Key = 'propertyUseTypeUpdate';
      commModel.data = this.property.UseTypeID;
      this._communicationService.broadcast(commModel);
    }
    const cancelCallback = () => {
      this.selectedUseTypeID = previousUseType;
      this.property.UseTypeID = previousUseType;
      this.propertyForm.get("PropertyTypeName")?.setValue(previousUseType);
    }
    const message = CommonStrings.DialogConfigurations.Messages.PropertyUseChangeConfirmationMessage;
    const title = CommonStrings.DialogConfigurations.Title.Allocations;
    const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: okCallback };
    const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: cancelCallback };
    this.showDialogConfirmation(message, title, okButton, cancelButton);
  }

  onSpecificUseChange(item, index, key) {
    this.isFootprintModified = true;
    this.multifloors[index][key] = item.specificUse;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.getAdditionalUses();
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
  }

  onChangeFloorOrUseType(floor, index, key) {
    if (this.isAnExistingProperty) {
      this.isFootprintModified = true;
    }
    const item = { ...floor };
    const result = [];

    for (const key in this.floorsWithFootprint) {
      if (key !== index.toString()) {
        result.push(...this.floorsWithFootprint[key]);
      }
    }
    const isValid = (item.minFloor && item.maxFloor) ? item.minFloor <= item.maxFloor : true;
    const hasGreaterFloor = item.minFloor > this.property.Floors || item.maxFloor > this.property.Floors;
    if (hasGreaterFloor) {
      this._notificationService.ShowErrorMessage('Min or Max Floor connot be more than property floors');
      setTimeout(() => {
        this.multifloors.forEach((floor, i) => {
          if (i === index) {
            if (key === 'minFloor') {
              floor.minFloor = undefined;
            } else if (key === 'maxFloor') {
              floor.maxFloor = undefined;
            }
          }
        })
      }, 0)
    } else if (isValid) {
      setTimeout(() => {
        this.multifloors.forEach(floor => {
          if (floor.minFloor && !floor.maxFloor) {
            floor.maxFloor = floor.minFloor;
          }
          if (!floor.minFloor && floor.maxFloor) {
            floor.minFloor = floor.maxFloor;
          }
          if (floor.maxFloor && floor.minFloor) {
            floor.floorCount = (floor.maxFloor - floor.minFloor) + 1
          } else if ((floor.maxFloor && !floor.minFloor) || (!floor.maxFloor && floor.minFloor)) {
            floor.floorCount = 1;
          }

        });
        this.updateBuildingSqmOnFloorChange();
        this.updateAddPolygonButtonDisable();
        if (this.isAnExistingProperty) {
          this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
        }
        const range = item.minFloor && item.maxFloor ? getRange(item.minFloor, item.maxFloor) : item.minFloor ? [item.minFloor] : [item.maxFloor];
        this.floorsWithFootprint = { ...this.floorsWithFootprint, [index]: range };
        this.getAdditionalUses();
        let commModel = new CommunicationModel();
        commModel.Key = 'floorInfoChange';
        commModel.data = this.multifloors;
        this._communicationService.broadcast(commModel);
      }, 10);
    } else {
      this._notificationService.ShowErrorMessage('Min Floor connot be more than max floor');
      setTimeout(() => {
        this.multifloors.forEach(item => {
          if (item.minFloor > item.maxFloor) {
            if (key === 'minFloor') {
              item.minFloor = item.maxFloor;
            } else if (key === 'maxFloor') {
              item.maxFloor = item.minFloor;
            }
          }
        });
        if (this.isAnExistingProperty) {
          this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
        }
      }, 0)
    }
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
  }

  getAdditionalUses() {
    let commModel = new CommunicationModel();
    commModel.Key = 'updateAdditionalUses';
    commModel.data = { multiFloors: this.multifloors, useType: this.property.UseTypeID };
    this._communicationService.broadcast(commModel);
  }

  onChangeAdditionalUse(index) {
    this.isFootprintModified = true;
    this.multifloors[index].additionalSpecificUseTypeId = null;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.getAdditionalUses();
  }

  onChangeDescription(item, index, key) {
    if (this.isAnExistingProperty) {
      this.isFootprintModified = true;
    }
    this.multifloors[index][key] = (item.description === null || item.description === undefined) ? '' : item.description;
    let commModel = new CommunicationModel();
    commModel.Key = 'floorInfoChange';
    commModel.data = this.multifloors;
    this._communicationService.broadcast(commModel);
    this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
  }

  setFloorOptions() {
    const options: { key: string, value: number }[] = [];
    if (this.property && this.property.Floors > 0) {
      for (let i = 1; i <= this.property.Floors; i++) {
        if (i === 1) options.push({ key: "G", value: 1 });
        else options.push({ key: (i - 1).toString(), value: i });
      }
      this.floorOptions = options;
    }
  }

  validateSizeFieldsAgainstBuildingSize() {
    if (this.isAnExistingProperty) {
      const formName = getFormNameByUseType(this.property?.UseTypeID);
      const useTypeForm = this.propertyForm?.get(formName);
      validateBuildingSize(this.property.BuildingSF, this.property, useTypeForm);
    }
  }

  updateBuildingSqmOnFloorChange() {
    if (this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold) {
      return;
    }
    const sum = this.multifloors.reduce((sum, item) => item.floorCount ? sum + (item.floorCount * (item.floorSize || 0)) : sum + 0, 0);
    this.property.BuildingSF = parseFloat(sum.toFixed(2));
    if (sum > 0) {
      this.footPrintNotAvailable = false;
      this.property.HasNoBuildingFootprints = false;
      this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBA_SF');
      this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBASource');
    }
    this.validateSizeFieldsAgainstBuildingSize();
  }

  showCustomMessage(fromInput) {
    const applyFloorSelection = () => {
      const floor = this.isAnExistingProperty ? this._sharedDataService.selectedFloor : (fromInput ? this.selectedFloor : this.property.Floors);
      this.property.Floors = floor;
      this.selectedFloor = floor;
      this.setFloorOptions();
    };
    const message = CommonStrings.DialogConfigurations.Messages.SomeFloorsHaveFootprintMessage;
    const title = CommonStrings.DialogConfigurations.Title.FloorwisePolygon;
    const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: applyFloorSelection };
    const cancelBtn = { Visible: false }
    this.showDialogConfirmation(message, title, okButton, cancelBtn);
  }

  onSelectFloor(fromInput = false) {
    let floorsWithFootprint = [];
    this.multifloors.forEach(floor => {
      if (floor) {
        const range = (floor.minFloor && floor.maxFloor) ? getRange(floor.minFloor, floor.maxFloor) : floor.minFloor ? [floor.minFloor] : [floor.maxFloor];
        floorsWithFootprint = [...floorsWithFootprint, ...range];
      }
    });
    const check = fromInput ? floorsWithFootprint.some((value) => value > this.property.Floors) : floorsWithFootprint.some((value) => value > this.selectedFloor);
    if (check) {
      this.showCustomMessage(fromInput);
    } else {
      if (!fromInput) {
        this.property.Floors = this.selectedFloor;
      } else {
        this.selectedFloor = this.property.Floors;
      }
      this.updateBuildingSqmOnFloorChange();
      if (this.isAnExistingProperty) {
        if (fromInput) {
          this.property.Floors = this.selectedFloor;
        } else {
          this.selectedFloor = this.property.Floors;
        }
      }
      this._sharedDataService.selectedFloor = this.selectedFloor;
    }
    this.setFloorOptions();
  }

  showDeleteModal(shapeId) {
    this.showDeletePolygonModal = true;
    this.buildingFootPrintIdToBeDeleted = shapeId;
  }

  deletePolygon() {
    this.buildingFootprintService.deleteBuildingFootprints(this.property.PropertyID, this.buildingFootPrintIdToBeDeleted,).subscribe((result: ApiResponseVoid) => {
      if (result.error) {
        this.showDeletePolygonModal = false;
        this._notificationService.ShowErrorMessage(result.message);
        return;
      }
      this.close();
      let commModel = new CommunicationModel();
      commModel.Key = 'deleteBuildingFootprint';
      commModel.data = result;
      this._communicationService.broadcast(commModel);
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToDeleteBuildingFootprints);
    })
  }

  close() {
    this.showDeletePolygonModal = false;
    this.buildingFootPrintIdToBeDeleted = undefined;
  }

  setSelectedUseType() {
    this.selectedUseTypeID = this.property.UseTypeID;
  }
  updateAddPolygonButtonDisable() {
    if (this.multifloors && this.multifloors.length === 0 || this.multifloors.every(item => (item.floorSize > 0 && !!item.minFloor && !!item.maxFloor))) {
      this.isAddPolygonButtonDisabled = false;
    } else {
      this.isAddPolygonButtonDisabled = true;
    }
  }

  expandCollapsePolygons() {
    setTimeout(() => {
      this.isPolygonCollapsed = !this.isPolygonCollapsed;
    }, 0);
  }
  
  addfloor() {
    const multipolyObj: MultiPolygon = {
      specificUse: this.selectedUseTypeID, maxFloor: undefined, minFloor: undefined, floorCount: undefined,
      floorSize: undefined, shape: undefined, localBuildingFootPrintID: uuidv4(), description: null
    };
    this.isPolygonCollapsed = false;
    this.multifloors = [...this.multifloors, multipolyObj];
    this._addFloorService.enableFloorLables(true);
    this.isAddPolygonButtonDisabled = true;
    this.setFloorOptions();
  }
  deleteFloorData(item, index) {
    if (this.isAnExistingProperty) {
      const currentIds = [...this.sharedDataService.deleteBuildingFootPrintIds];
      const newId = item.BuildingFootPrintID;
      if (newId && !currentIds.includes(newId)) {
        currentIds.push(newId);
      }
      this.sharedDataService.deleteBuildingFootPrintIds = currentIds;
      this.isDeleteFloor = true;
      if (this.floorsWithFootprint && this.floorsWithFootprint[index]) {
        delete this.floorsWithFootprint[index];
      }
      this.multifloors = this.multifloors.filter((floor) => {
        if (floor.BuildingFootPrintID && item.BuildingFootPrintID) {
          return floor.BuildingFootPrintID !== item.BuildingFootPrintID
        } else {
          return floor.localBuildingFootPrintID !== item.localBuildingFootPrintID
        }
      });
    } else {
      if (this.floorsWithFootprint && this.floorsWithFootprint[index]) {
        delete this.floorsWithFootprint[index];
      }
      this.multifloors = this.multifloors.filter((floor) => floor.localBuildingFootPrintID !== item.localBuildingFootPrintID);
    }
    //To trigger allocations update
    this.isFootprintModified = true;
    this.updateAddPolygonButtonDisable();
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.MultiFloors, value: this.multifloors });
    this.getAdditionalUses();
    this.updateBuildingSqmOnFloorChange();
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
    const buildingFootPrintID = item.BuildingFootPrintID ? item.BuildingFootPrintID.toString() : item.localBuildingFootPrintID;
    let commModel = new CommunicationModel();
    commModel.Key = 'deleteFloor';
    commModel.data = { floorId: buildingFootPrintID, multifloors: this.multifloors };
    this._communicationService.broadcast(commModel);
  }

  deleteFloorConfirmation(item, index: number) {
    if (item.minFloor || item.maxFloor || item.floorSize || item.shape) {
      const message = CommonStrings.DialogConfigurations.Messages.DeleteFloorData;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => this.deleteFloorData(item, index) };
      const cancelBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { } };
      this.showDialogConfirmation(message, title, okButton, cancelBtn);
    } else {
      this.deleteFloorData(item, index);
    }
  }

  allocatedFloorsConfirmationPopup() {
    // Confirmation modal to update allocations before updating floors
    const configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.AllocatedFloorsMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Ok;
    this._notificationService.CustomDialog(configuration);
  }

  isResearchVisible(researchTypeID: number, propertyID: number): boolean {
    return !(researchTypeID === ResearchType.NotStarted);
  }

  onFileUploaded($event) {
    let model = new CommunicationModel();
    model.data = $event;
    model.Key = 'onFileUpload';
    this._communicationService.broadcast(model);
    this.showFileUpload = false;
  }

  loadResearchStatus() {
    // To get all research status.
    if (!!this._sharedDataService.researchStatusList && this._sharedDataService.researchStatusList.length > 0) {
      this.propertyResearchStatus = this._sharedDataService.researchStatusList;
      if (this.initialDetails.propertyId) {
        this.getPropertyResearchStatus(this.initialDetails.propertyId);
      }
    } else {
      const researchStatus = this.propertyLookups['ResearchType'] ?? []
      this.propertyResearchStatus = researchStatus || [];
      this.propertyResearchStatus.sort((a, b) => Number(a.Sequence) - Number(b.Sequence));
      this.propertyResearchStatus = processPropertyResearchStatus(this.propertyResearchStatus);
      this._sharedDataService.researchStatusList = this.propertyResearchStatus;
      if (this.initialDetails.propertyId) {
        this.getPropertyResearchStatus(this.initialDetails.propertyId);
      }
    }
  }

  getUserInfoFromStorage() {
    const loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
    if (loginData != "" && !!loginData) {
      const bytes = AES.decrypt(loginData?.toString(), environment?.EncryptionKey);
      const loggedinData = JSON.parse(bytes?.toString(encUtf8));
      if (loggedinData) {
        const { UnitID, EntityID, DateFormat, UnitDisplayTextSize, RoleID } = loggedinData;
        this.UnitId = UnitID || this.metricUnit;
        this.EntityID = EntityID;
        this.dateFormat = DateFormat || DefaultDateFormat;
        this.UnitDisplayTextSize = UnitDisplayTextSize || DefaultUnitDisplayTextSize;
        this.roleID = RoleID;
      }
    }
  }

  ngOnInit() {
    this._sharedDataService.parcelInfoPickedFromTileLayer = null;
    this.getUserInfoFromStorage();
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    if (!this.isAnExistingProperty) {
      this.property = new PropertyDetails();
      this.property.TypicalFloorSize = 0;
    }
    this.isNavigationFromSearch = JSON.parse(sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch));
    if (this.isNavigationFromSearch) {
      this.getVisitedAndEditedProperties();
      const activeGridRowNumberFromSs = sessionStorage.getItem(SessionStorageKeys.ActiveGridRowNumber);
      const PageDetailsFromSs = sessionStorage.getItem(SessionStorageKeys.SearchResultsPageDetails);
      if (activeGridRowNumberFromSs && PageDetailsFromSs) {
        this.activeGridRowNumber = JSON.parse(activeGridRowNumberFromSs);
        this.pageDetails = JSON.parse(PageDetailsFromSs);
        this.setPropertyRecordDisplayNumber(this.activeGridRowNumber);
      }
    }
    this.getMultiFloorsFromMetaData();
    this.visitedStrataIdsFromSS = JSON.parse(sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)) || [];
    this.editedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.EditedStrataIds)) || [];
    this.navigationPreference = JSON.parse(sessionStorage.getItem(SessionStorageKeys.NavigationPreference)) || NavigationPreferences.UnVisited;
    this.initData();
  }

  async getPropertyLookupData(onLoad: () => void) {
    try {
      this.propertyLookups = (await this.propertyTrackingService.getPropertyLookup()) ?? [];
      this._sharedDataService.setLookupDropdowns(this.propertyLookups);
      this.propertyLookupsCopy = {...this.propertyLookups};
      this.getAuditStatuses();
      onLoad();
      this._sharedDataService.setLookupDropdowns(this.propertyLookups);
    } catch (error) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.LookupDataFailureMessage);
      this.propertyLookups = []; // fallback value
      this.redirectionLoader = false;
    }
  }

  async getMultiFloorsFromMetaData() {
    try {
      const floorsData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
      if (floorsData) {
        this.multifloors = floorsData.value;
        this._addFloorService.enableFloorLables(true)
        this.updateAddPolygonButtonDisable();
        this.updateBuildingSqmOnFloorChange();
        let commModel = new CommunicationModel();
        commModel.Key = 'floorInfoChange';
        commModel.data = this.multifloors;
        this._communicationService.broadcast(commModel);
      } else if (this.isAnExistingProperty) {
        this._addFloorService.enableFloorLables(true)
      }
      if (!this.isAnExistingProperty) {
        this.property.TypicalFloorSize = 0;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }
  getAuditStatuses = async () => {
    this.auditStatusList = this.propertyLookups['PropertyAuditStatus'] ?? [];
  }

  async getVisitedAndEditedProperties() {
    this.visitedPropertyIds = (await this.propertyTrackingService.getVisitedPropertyIds()) ?? [];
    this.editedPropertyIds = (await this.propertyTrackingService.getEditedPropertyIds()) ?? [];
  }

  loadCountryStateCities() {
    // Normalize country list
    this.countryList = this.countryList?.map(c => {
      const country = c as Country;
      country.Alpha2Code = c['Alpha-2Code'];
      country.Alpha3Code = c['Alpha-3Code'];
      delete country['Alpha-2Code'];
      delete country['Alpha-3Code'];
      return country;
    });
    let countryId = this.countryList?.find(x => (x.Alpha2Code == this.countryCode || x.Alpha3Code == this.countryCode || x.CountryId == this.countryCode))?.CountryId;
    if (countryId) {
      this.states = this.states?.filter(state => state.CountryID === countryId);
      let state = this.states?.find(x => x.StateAbbr.trim() == this.stateCode);
      this.cities = this.cities?.filter(city => city.StateID === state?.StateID);
      this.counties = this.counties?.filter(county => county.StateID === state?.StateID);
      this.populatePropertyLocation();
    } else {
      this.redirectionLoader = false;
    }
  }

  initData() {
    if (this.initialDetails && this.initialDetails.locationData) {
      this.countryCode = this.initialDetails.locationData.CountryCode;
      this.stateCode = this.initialDetails.locationData.StateCode;
    }
    // Map lookup to variables
    this.getPropertyLookupData(() => {
      // Bind lookup data
      Object.keys(BindLookupNameToVariable).forEach(key => this[BindLookupNameToVariable[key]] = this.propertyLookups[key]);
    
      // Process construct types
      this.constructTypes = (this.constructTypes || []).sort((a, b) => a.Retired - b.Retired).map(type => ({
        ...type,
        Retired: type.Retired === 1 ? ConstructTypeStatus.Retired : ConstructTypeStatus.InUse,
        disabled: type.Retired === 1
      }));
    
      // Process property types
      this.propertyTypes?.forEach(data => {
        data.isSelected = false;
        if (!data.UseTypeLabel) data.UseTypeLabel = data.UseTypeName.charAt(0);
        if (data.UseTypeName === "Office") this.officeId = data.UseTypeID;
      });
    
      this.updateAdditionalUsesList();
      this.loadCountryStateCities();
      this.loadResearchStatus();
    });
    
    this.propertyForm = new FormGroup({
      locationDetailsForm: new FormGroup({
        'Latitude': new FormControl('', Validators.required),
        'Longitude': new FormControl('', Validators.required)
      }),
      //Top section fields
      'Condo': new FormControl('', Validators.required),
      'CondoUnit': new FormControl(''),
      'MasterPropertyId': new FormControl(''),
      'ZoningCode': new FormControl(''),
      'ConstructionStatus': new FormControl('', Validators.required),
      'ConstructionType': new FormControl(''),
      'BuildingSF': new FormControl('', [Validators.required, numericValidator(), Validators.min(1)]),
      'BldgSizeSourceID': new FormControl('', [Validators.required, Validators.min(1)]),
      'LotSizeSourceID': new FormControl('', Validators.required),
      'LotSizeSF': new FormControl('', [numericValidator()]),
      'PropertyTypeName': new FormControl('', [Validators.required, Validators.min(1)]),
      'Floors': new FormControl('', Validators.required),
      'PropertyNameAsAddress': new FormControl(''),
      'IsMultiplePolygonsNeeded': new FormControl(''),
      'NeedsResearchComments': new FormControl(''),
      'IsSkipped': new FormControl(''),
      'ParcelCount': new FormControl(''),
      'AggregateParcelSize': new FormControl(''),
      'ContributedSourceComments': new FormControl(''),
      'OfficeForm': new FormGroup({}),
      'IndustrialForm': new FormGroup({}),
      'RetailForm': new FormGroup({}),
      'LandForm': new FormGroup({})
    });

    // Dynamically add controls based on LocationFormControls enum
    Object.values(LocationFormControls).forEach(controlName => {
      const locationDetailsForm = this.propertyForm?.get('locationDetailsForm') as FormGroup;
    
      if (!locationDetailsForm.contains(controlName)) {
        locationDetailsForm.addControl(controlName, new FormControl(''));
      } 
    });

    this.propertyForm.get('PropertyTypeName').valueChanges.subscribe((PropertyType) => {
      const formName = getFormNameByUseType(this.property?.UseTypeID); 
      const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
      if (PropertyType === this.officeId.toString()) {
        useTypeForm?.get('BuildingClass')?.clearValidators();
        useTypeForm?.get('BuildingClass')?.setValidators([Validators.required]);
      } else {
        useTypeForm?.get('BuildingClass')?.clearValidators();
      }
      useTypeForm?.get('BuildingClass')?.updateValueAndValidity();
      this.handleFieldsOnUseTypeOrStrataChange(this.property.UseTypeID,this.property?.CondoTypeID);
      this.propertyLookupsCopy && Object.keys(this.propertyLookupsCopy).forEach((lookup) => {
        if(lookup == 'BuildingClassID') {
          this.propertyLookups[lookup] = this.propertyLookupsCopy[lookup].filter((item) => {
            return item.UseTypeID == this.propertyTypes.find(a => a.UseTypeID === PropertyType)?.UseTypeID;
          });
        }
        if(lookup == 'SpecificUsesID'){
          this.propertyLookups[lookup] = this.propertyLookupsCopy[lookup].filter((item)=>{
            return item.UseTypeID == PropertyType;
          })
        }
      })
    })

    this.rangeForm = this.formBuilder.group({
      StrataMin: ['', [Validators.required, Validators.maxLength(6)]],
      StrataMax: ['', [Validators.required, Validators.maxLength(6)]]
    });

    if (!this.isAnExistingProperty && this.isParcelLayerEnabled && this._sharedDataService && this._sharedDataService.parcelInfoPickedFromTileLayer) {
      const parcels = this._sharedDataService.parcelInfoPickedFromTileLayer || [];
      this.parcelInformation = parcels;
      this.showParcelWindow = true;
      this.hasNoExistingParcelInTileLayer = parcels.length === 0;
      this.initTypeAndFloor();
    }

    this.propertyForm.get('Condo').valueChanges.subscribe((Condo) => {
      if (!Condo) return;
      const form = this.propertyForm;
      const enable = (name, validators = []) => {
        form.get(name).enable();
        form.get(name).setValidators(validators);
        form.get(name).markAsTouched();
      };
      const disable = (name) => {
        form.get(name).disable();
        form.get(name).clearValidators();
        form.get(name).markAsUntouched();
      };
      if (([CondoTypeEnum.Strata, CondoTypeEnum.ChildFreehold] as PropertyDetailsDTO.CondoTypeIDEnum[]).includes(this.property?.CondoTypeID)) {
        enable('CondoUnit', [Validators.required]);
        enable('MasterPropertyId', [Validators.required]);
        disable('LotSizeSourceID');
        disable('AggregateParcelSize');
      } else if ([this.EnumCondoTypeNames.MasterFreehold, this.EnumCondoTypeNames.MasterStrataRecord].includes(Condo)) {
        this.property.SmallestFloor = 0;
        this.property.LargestFloor = 0;
        disable('CondoUnit');
        enable('LotSizeSourceID', [Validators.required]);
        enable('AggregateParcelSize');
      } else {
        disable('CondoUnit');
        disable('MasterPropertyId');
        form.get('BuildingSF')?.setValidators([Validators.required, numericValidator(), Validators.min(1)]);
        enable('LotSizeSourceID');
        disable('AggregateParcelSize');
      }
      ['CondoUnit', 'MasterPropertyId', 'LotSizeSourceID', 'AggregateParcelSize'].forEach(c => form.get(c).updateValueAndValidity());
    });    

    if (this.property && this.property.Address && !this.property.Address.AddressTypeId) {
      this.property.Address.AddressTypeId = this.addressTypeValues.Address;
    }

    if (this.isAnExistingProperty) {
      //To revert to previous property use type
      const previousUseType = this.property.UseTypeID;
      this.onPropertyUseChange(false, this.property.UseTypeID ? this.property.UseTypeID : 5, previousUseType);
    }

    setTimeout(() => {
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
    }, 2000)
  }

  initTypeAndFloor() {
    this.selectedUseTypeID = null;
    this.selectedFloor = !!this._sharedDataService.selectedFloor ? this._sharedDataService.selectedFloor : 1;
    this.property.UseTypeID = this.selectedUseTypeID;
    setTimeout(() => {
      this.property.Floors = this.selectedFloor;
      this.property.ConstructionStatusID = this.property.ConstructionStatusID ?? ConstructionStatusEnum?.Existing;
      this.setFloorOptions();
    }, 100);
  }

  private landValidator =   function () {
    const formName = getFormNameByUseType(this.property?.UseTypeID); 
    const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
    useTypeForm.get('BuildingClass').clearValidators();
  }

  handleFieldsOnUseTypeOrStrataChange(useType, condoType) {
    const form = this.propertyForm;
    const enable = (ctrl, validators = []) => {
      form.get(ctrl)?.enable();
      form.get(ctrl)?.setValidators(validators);
      form.get(ctrl)?.markAsTouched();
      form.get(ctrl)?.updateValueAndValidity();
    };
    const disable = (ctrl) => {
      form.get(ctrl)?.disable();
      form.get(ctrl)?.clearValidators();
      form.get(ctrl)?.markAsUntouched();
      form.get(ctrl)?.updateValueAndValidity();
    };
  
    if (useType === this.propertyTypeValues.Land) {
      if (!this.isAnExistingProperty) this.checkForParcelLayer();
      ['ConstructionType', 'ConstructionStatus', 'Condo'].forEach(c => {
        form.get(c)?.clearValidators(); form.get(c)?.updateValueAndValidity();
      });
    } else {
      if (condoType !== this.EnumCondoTypeNames.MasterFreehold) {
        enable('ConstructionStatus', [Validators.required]);
        enable('ConstructionType', [Validators.required]);
      } else {
        disable('ConstructionStatus');
        form.get('ConstructionType')?.clearValidators();
        form.get('ConstructionType')?.updateValueAndValidity();
      }
      enable('Condo', [Validators.required]);
    }
  
    const isLandOrMaster = useType === this.propertyTypeValues.Land ||
      [this.EnumCondoTypeNames.MasterFreehold, this.EnumCondoTypeNames.MasterStrataRecord].includes(condoType);
  
    if (isLandOrMaster) {
      this.property.TypicalFloorSize = 0.0;
      if (this.isAnExistingProperty) {
        this.property.SmallestFloor = 0.0;
        this.property.LargestFloor = 0.0;
      }
      ['BuildingSF', 'Floors', 'TypicalFloorSizeSM'].forEach(disable);
      enable('LotSizeSourceID', [Validators.required]);
      form.get('ConstructionType')?.clearValidators();
      form.get('ConstructionType')?.updateValueAndValidity();
      enable('AggregateParcelSize');
    } else {
      enable('Floors', [Validators.required]);
      enable('TypicalFloorSizeSM', [Validators.required]);
      enable('BuildingSF', [Validators.required, numericValidator(), Validators.min(1)]);
      disable('LotSizeSourceID');
      enable('ConstructionType', [Validators.required]);
      disable('AggregateParcelSize');
    }
  
    if (condoType === this.EnumCondoTypeNames.NotStrata || !condoType) {
      enable('LotSizeSourceID', [Validators.required]);
    }
  }

  private applyValidation(action: 'clear' | 'update') {
    const controls = [ 'Floors', 'ConstructionStatus', 'ConstructionType', 'BldgSizeSourceID', 'LotSizeSourceID', 'ZoningCode', 'BuildingSF' ];
    controls.forEach(control => {
      const ctrl = this.propertyForm.get(control);
      if (!ctrl) return;
      if (action === 'clear') {
        ctrl.clearValidators();
      }
      ctrl.updateValueAndValidity();
    });
  }

  addSpecificValidation(PropertyType) {
    this.applyValidation('clear');
    switch (Number(PropertyType)) {
      case this.propertyTypeValues.Land:
        if (!(this.propertyResearchStatus.length > 0 && this.propertyResearchStatus[3].IsActive)) { this.landValidator(); }
        break;
    }
    this.applyValidation('update'); 
  }

  broadCastFetchAdditionalAddress() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchAdditionalAddress';
    commModel.data = this.property.PropertyID;
    this._communicationService.broadcast(commModel);
  }

  broadcastFetchParcels() {
    let commModel = new CommunicationModel();
    commModel.Key = 'fetchParcels';
    commModel.data = { shouldUpdate: false };
    this._communicationService.broadcast(commModel);
  }

  private populatePropertyLocation() {
    // If add new property, bind country, city, state with what we get from google
    if (this.initialDetails.propertyId && !this.initialDetails.isNewProperty) {
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
      this.isNewProperty = false;
      this.displayPropertyId = this.initialDetails.propertyId;
      this.property.PropertyID = this.initialDetails.propertyId;
      this.getPropertyDetails(this.property.PropertyID, () => {
        if (this.isAnExistingProperty) {
          this.getPropertyResearchStatus(this.property.PropertyID);
          this.broadcastFetchParcels();
          this.broadCastFetchAdditionalAddress();
        }
      });
    }
    else if (this.initialDetails.isNewProperty || !!this.initialDetails.locationData) {
      this.redirectionLoader = false;
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
      this.property = new PropertyDetails();
      this.property.UseTypeID = undefined;
      this.property.SpecificUseID = 0;
      this.property.SizeSourceID = null;
      this.property.ClassTypeID = undefined;
      this.property.PropertyID = 0;
      this.isNewProperty = true;
      this.displayPropertyId = 0;
      if (!this.isAnExistingProperty) {
        this.propertyForm.get('ConstructionStatus').setValue(ConstructionStatusEnum?.Existing);
        this.property.ConstructionStatusID = ConstructionStatusEnum?.Existing;
      }
      // Binding country based on the google country code
      this.defaultPropertyLocationForNewProperty();
      if (this.isAnExistingProperty) {
        if (this.initialDetails.selectedParcel) {
          this.populatePropertyWithTaxData();
        }
        //To revert to previous property use type
        const previousUseType = this.property.UseTypeID;
        this.onPropertyUseChange(false, this.property.UseTypeID ? this.property.UseTypeID : 5, previousUseType);
      }
    } else {
      if (!this.isAnExistingProperty) {
        this.selectedTab = EditPropertyTabs.Property;
      }
    }
  }
  
  private populatePropertyWithTaxData() {
    let parcel = this.initialDetails.selectedParcel;
    this.property.UseTypeID = parcel.PropertyUse;
    if (!(this.initialDetails.parcelProperties && this.initialDetails.parcelProperties.length > 0)) {
      this.property.BuildingSF = parcel.Area;
      this.property.SizeSourceID = SizeSourceEnum.AerialEstimation;
    }
  }

  private defaultPropertyLocationForNewProperty(): void {
    this.initializeNewLocationAndParcel();
    this.setBasicLocationDetails();
    this.setLocationDataDetails();
    if (!this.isAnExistingProperty) {
      this.initTypeAndFloor();
      this.buildAddress();
    }
    this.addressAsPropertyName(false);
    if (this.initialDetails.fromMasterStrata && this.initialDetails.masterStrataObj.property) {
      this.handleMasterStrata();
    } else {
      this.showMapModal(true, null, null, false);
    }
  }
  
  private initializeNewLocationAndParcel(): void {
    this.property.Address.AddressTypeId = this.property?.Address.AddressTypeId ?? this.addressTypeValues.Address;
  }
  
  private setBasicLocationDetails(): void {
    const latLng = this.initialDetails.latLng;
    if (latLng.Latitude) this.property.Location.Latitude = latLng.Latitude;
    if (latLng.Longitude) this.property.Location.Longitude = latLng.Longitude;
  }
  
  private setLocationDataDetails(): void {
    const data = this.initialDetails.locationData;
    if (!data) return;
    if (data.ZipCode) this.property.Address.ZipCode = data.ZipCode;
    if (data.MaximumStreetNumber) this.property.Address.StreetNumberMax = data.MaximumStreetNumber;
    if (data.MinimumStreetNumber) {
      this.property.Address.StreetNumberMin = data.MinimumStreetNumber;
    } else if (data.StreetNumber) {
      this.property.Address.StreetNumberMin = data.StreetNumber;
    }  
    this.setCountry(this.countryCode)
    this.setState(data.StateCode);
    this.setCity(data.City, data.StateCode);
    this.setCounty(data.CountyName);
    this.setStreetName(data.StreetNameShort);
  }

  private setCountry(countryCode: string): void {
    const country = this.countryList?.find(x => (x.Alpha2Code == countryCode || x.Alpha3Code == countryCode));
    if (country)  this.property.Address.CountryID = country.CountryId    
  }
  
  private setState(stateCode: string): void {
    if (!stateCode) return;
    const state = this.states.find(s => s.StateAbbr.trim() === stateCode);
    if (state) this.property.Address.StateID = state.StateID;
  }
  
  private setCity(cityName: string, stateCode: string): void {
    if (!cityName) return;
    const matchedCity = this.cities.find(c => c.CityName.toUpperCase().trim() === cityName.toUpperCase());
    if (matchedCity) {
      this.property.Address.CityID = matchedCity.CityID;
    } else {
      const state = this.states.find(s => s.StateAbbr.trim() === stateCode);
      if (state) {
        const cityReqBody: CityRequestDTO = {
          CityName: cityName.trim(),
          StateId: state.StateID,
        }
        this._addressService.createCity(cityReqBody).subscribe((result: ApiResponseCity) => {
          if (!result.error && result.responseData) {
            this.property.Address.CityID = result.responseData?.cityId;
          } else {
            this._notificationService.ShowErrorMessage(result.message);
          }
        }, error => {
          this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToCreateCity);
        });
      }
    }
  }
  
  private setCounty(countyName: string): void {
    if (!countyName) return;
    let county = (this.counties || []).find(c => c.CountyName.toUpperCase().trim() === countyName.toUpperCase());
    if (!county) {
      const cleaned = countyName.toUpperCase().replace('CITY', '').trim();
      county = (this.counties || []).find(c => c.CountyName.toUpperCase().trim() === cleaned);
    }
    if (county) {
      this.property.Address.CountyID = county.CountyID;
    }
  }
  
  private setStreetName(streetName: string): void {
    if (!streetName) return;
    this.property.Address.AddressStreetName = streetName;
    const suffixParts = streetName.split(" ");
    this.selectedSuffix = suffixParts[suffixParts.length - 1];
    const matchedSuffix = this.streetSufixes.find(s =>
      s?.Suffix?.toUpperCase() === this.selectedSuffix?.toUpperCase() ||
      s?.SuffixName?.toUpperCase() === this.selectedSuffix?.toUpperCase()
    );
    if (matchedSuffix) {
      this._zone.run(() => {
        const lastIndex = streetName.lastIndexOf(" ");
        this.property.Address.AddressStreetName = streetName.substring(0, lastIndex);
        this.property.Address.StreetSuffix1 = matchedSuffix.SuffixId;
      });
    }
  }
  
  private handleMasterStrata(): void {
    const masterStrata = this.initialDetails.masterStrataObj;
    this.disableStrataBtn = true;
    this.isFreehold = masterStrata.isFreehold;
    this.isMultiStrata = masterStrata.isMultiStrata;
    this.pRange.StrataMin = masterStrata.minStrataUnit;  
    this.property = { ...masterStrata.property };
    this.property.MasterPropertyId = masterStrata.property.PropertyID;

    this.property.PropertyID = undefined;
    this.property.CondoTypeID = this.isFreehold ? CondoTypeEnum.ChildFreehold : CondoTypeEnum.Strata;
    this.property.CondoUnit = masterStrata.minStrataUnit;
  
    this.property.BuildingSF = undefined;
    this.property.TypicalFloorSize = undefined;  
    this.selectedOption = masterStrata.property.PropertyResearchTypeID;
    this.selectedFloor = '1';
    this.property.Floors = 1;
    this.rangeForm.valueChanges.subscribe(() => {
      this.checkStrataMinMaxError();
    });
    this.propertyForm.patchValue({ Condo: this.property.CondoTypeID });
    this.propertyForm.get('Condo').markAsDirty();
    this.setFormFieldValidators('CondoUnit');
    this.setFormFieldValidators('MasterPropertyId');
    this.handleFieldsOnUseTypeOrStrataChange(
      this.property.UseTypeID,
      this.property.CondoTypeID
    );
    this.showMapModal(true, null, null, false);
  }
  
  private setFormFieldValidators(fieldName: string): void {
    const control = this.propertyForm.get(fieldName);
    control?.enable();
    control?.setValidators([Validators.required]);
    control?.markAsTouched();
  }
  
  private getPropertyDetails(propertyId, onPropertyFetched: () => void) {
    let apiCounter = 0;
    apiCounter++;
    this.footPrintNotAvailable = false;
    const responseProperty = this._propertyService.getPropertyById(propertyId);
    responseProperty.subscribe((result:ApiResponsePropertyDetailsSizeResponseDTO) => {
        if (result.error) {
          this._notificationService.ShowErrorMessage(result.message);
          return;
        }
        preparePropertyFromResult({
          result,
          component: this,
          form: this.propertyForm,
          unitId: this.UnitId,
          dateFormat: this.dateFormat,
          datePipe: this._datePipe,
          sharedDataService: this.sharedDataService,
          buildAddress: this.buildAddress.bind(this),
          onPropertyFetched: onPropertyFetched, // or this.onPropertyFetched if it's a method          
        });
        const showStrata = this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.Strata || this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterStrataRecord;
          const showFreehold = this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.ChildFreehold || this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterFreehold;
          if (showFreehold || showStrata) {
            this.showStrataOrFreeholdTab = true;
            this.selectedTab = EditPropertyTabs.Strata;
          } else {
            this.showStrataOrFreeholdTab = false;
          }
      },
      error => {
        this.redirectionLoader = false;
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchPropertyDetails)
      }
    );
    if (!this.isAnExistingProperty) {
      this.loadResearchStatus();
      this.resetControls()
    }
    if (this.isAnExistingProperty) {
      this.broadCastFetchChangeLog();
    }
    this.handleFieldsOnUseTypeOrStrataChange(this.property.UseTypeID, this.property.CondoTypeID);
    const propertyTabExpandStatus = sessionStorage.getItem(SessionStorageKeys.PropertyDetailsTabStatus);
    this.isPropertyDetailsExpanded = propertyTabExpandStatus ? JSON.parse(propertyTabExpandStatus) : false;
    setTimeout(() => {
      this.getPropertySpecificUse();
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
      if (this.isAnExistingProperty) {
        this.broadCastNewPropertyFetch();
      }
    }, 800)

  }

  addProperty(isFreehold = false) {
    this.ShowProperties = true;
    this.isFreehold = isFreehold
  }

  getPropertySpecificUse(): void {
    const useTypeId = this.property.UseTypeID;
    if (!useTypeId || useTypeId === 0) return;
    // Clear existing specific uses
    this.specificUses = [];
    if (this.allspecificuses?.length) {
      this.specificUses = this.allspecificuses.filter(item => item.UseTypeID === useTypeId);
    } else {
      this.specificUses = (this.propertyLookups['SpecificUsesID'] || []).filter(use => use.UseTypeId === useTypeId);
    }
  }
  
  getAdditionalSpecificUse(useTypeId) {
    let specificUses = []
    if(!!this.allspecificuses && this.allspecificuses.length > 0) {
      specificUses = [ ...this.allspecificuses ].filter((item) => item.UseTypeID == useTypeId);
    }
    return specificUses;
  }

  onAddProperties(SelectedProperties) {
    if (SelectedProperties) {
      this.propertyForm.get('MasterPropertyId').markAsDirty();
      this.propertyForm.get('MasterPropertyId').markAsUntouched();
      if (this.property.CondoTypeID === CondoTypeEnum.Strata) {
        this.property.MasterPropertyId = SelectedProperties[0].PropertyID;
        this.selectedOption = SelectedProperties[0].ResearchTypeID;
        this.property.PropertyResearchTypeID = SelectedProperties[0].ResearchTypeID;
      } else if (this.property.CondoTypeID === CondoTypeEnum.ChildFreehold) {
        if (SelectedProperties[0].ParcelInfo === this.parcelNumber) {
          this.property.MasterPropertyId = SelectedProperties[0].PropertyID;
          //Update LotSize of the property to its Master Lotsize
          this.property.LotSizeSF = this._propertyService.convertUnit(this.CountryId, 'SF', 'SqM', SelectedProperties[0].LotSizeSF);
          this.selectedOption = SelectedProperties[0].ResearchTypeID;
          this.property.PropertyResearchTypeID = SelectedProperties[0].ResearchTypeID;
          this.selectedMasterParcel = SelectedProperties[0].ParcelInfo;
          this.LastStrataUnit = parseInt(SelectedProperties[0].LastStrataUnit);
          if (!this.property.CondoUnit) {
            this.property.CondoUnit = (this.LastStrataUnit + 1).toString();
          }
        } else {
          this.completeChanges();
          this.onComplete.emit(false);
          this.footPrintNotAvailable = false;
          this._notificationService.ShowWarningMessage(CommonStrings.DialogConfigurations.Messages.MasterAndChildParcelMismatch);
        }
      }
    }
  }

  closeProperty() {
    this.ShowProperties = false;
  }

  onStrataChange() {
    this.handleFieldsOnUseTypeOrStrataChange(this.property?.UseTypeID, this.property?.CondoTypeID);
    if (this.property.CondoTypeID !== CondoTypeEnum.MasterFreehold && this.property.CondoTypeID !== CondoTypeEnum.MasterStrataRecord) {
      this.propertyForm.get('BuildingSF') && this.propertyForm.get('BuildingSF').setValidators([Validators.required, numericValidator(), Validators.min(1)]);
    }
    if (this.property.CondoTypeID === CondoTypeEnum.Strata || this.property.CondoTypeID === CondoTypeEnum.ChildFreehold) {
      this.propertyForm.get('CondoUnit').enable();
      this.propertyForm.get('CondoUnit').setValidators([Validators.required]);
      this.propertyForm.get('CondoUnit').markAsTouched();
      this.propertyForm.get('MasterPropertyId').enable();
      this.propertyForm.get('MasterPropertyId').setValidators([Validators.required]);
      this.propertyForm.get('MasterPropertyId').markAsTouched();
      this.propertyForm.get('LotSizeSourceID')?.clearValidators();
      this.propertyForm.get('LotSizeSourceID')?.updateValueAndValidity();
    } else {
      this.propertyForm.get('LotSizeSourceID')?.setValidators([Validators.required]);
      this.propertyForm.get('LotSizeSourceID')?.updateValueAndValidity();
    }
    if (this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold) {
      this.multifloors = [];
      this.property.BuildingSF = 0.00;
      this.property.TypicalFloorSize = 0.00;
      if (this.isAnExistingProperty) {
        //To trigger alloacations save
        this.isFootprintModified = true;
        this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
      }
    } else {
      this.isFloorBtnDisable = false;
    }

    let commModel = new CommunicationModel();
    commModel.Key = 'CondoTypeChange';
    const data = {
      isFloorBtnDisable: (this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold) ? true : false,
      Condo: this.property.CondoTypeID
    }
    commModel.data = data;
    this._communicationService.broadcast(commModel);

    if (this.property.CondoTypeID !== CondoTypeEnum.Strata && this.property.CondoTypeID !== CondoTypeEnum.ChildFreehold) {
      this.property.MasterPropertyId = null;
      this.property.CondoUnit = null;
    }
    if (this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold) {
      let commModel = new CommunicationModel();
      commModel.Key = 'StrataTypeChangedToMaster';
      commModel.data = this.property.CondoTypeID;
      this._communicationService.broadcast(commModel);
    }

    if (this.property && (this.property.CondoTypeID === CondoTypeEnum.NotStrata || this.property.CondoTypeID === CondoTypeEnum.Strata ||
      this.property.CondoTypeID === CondoTypeEnum.ChildFreehold)) {
      this.updateAddPolygonButtonDisable()
    }
    this.setSelectedPropertyCondoType.emit(this.property.CondoTypeID);
    if (!this.isAnExistingProperty && (this.property.CondoTypeID !== CondoTypeEnum.Strata && this.property.CondoTypeID !== CondoTypeEnum.ChildFreehold)) {
      this.checkForParcelLayer();
    }
  }

  showStrataConfirmation() {
    if (this.isAnExistingProperty && this.property.CondoTypeID != CondoTypeEnum.Strata && this.property.CondoTypeID != CondoTypeEnum.MasterStrataRecord &&
      this.property.CondoTypeID != CondoTypeEnum.ChildFreehold && this.property.CondoTypeID != CondoTypeEnum.MasterFreehold && this.selectedTab == EditPropertyTabs.Strata) {
      this.selectedTab = EditPropertyTabs.Property;
    }
    this.setSelectedPropertyCondoType.emit(this.property.CondoTypeID);
    const previousCondoType = this.propertyCopy.CondoTypeID;
    if (previousCondoType === CondoTypeEnum.Strata || previousCondoType === CondoTypeEnum.ChildFreehold) {
      if (this.isAnExistingProperty) {
        const message = CommonStrings.DialogConfigurations.Messages.StrataTypeChangeConfirmationMessage;
        const title = CommonStrings.DialogConfigurations.Title.Arealytics;
        const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: () => { this.property.MasterPropertyId = null;
          this.strataChange(this.property.CondoTypeID);}}
        const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { this.property.CondoTypeID = previousCondoType; }}
        this.showDialogConfirmation(message, title, okButton, cancelButton);
      } else {
        this.property.MasterPropertyId = null;
        this.strataChange(this.property.CondoTypeID);
      }
    }
    else if (this.propertyCopy.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.propertyCopy.CondoTypeID === CondoTypeEnum.MasterFreehold) {
      const response = this._propertyService.getLinkedPropertyDetails(this.displayPropertyId);
      response.subscribe((result: ApiResponseListPropertyStrataDetailsDTO) => {
        if (!result.error) {
          var LinkedPropertyList = result.responseData || [];
          if ((this.propertyCopy.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.propertyCopy.CondoTypeID === CondoTypeEnum.MasterFreehold) && LinkedPropertyList.length > 1) {
            this.showMasterStrataAlert = true;
            this.property.CondoTypeID = this.propertyCopy.CondoTypeID;
            this.propertyForm.get('Condo').disable();
          } else if ((this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold) && LinkedPropertyList.length > 1) {
            this.showMasterStrataAlert = true;
            this.propertyForm.get('Condo').disable();
          } else {
            this.showMasterStrataAlert = false;
            this.propertyForm.get('Condo').enable();
          }
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchLinkedProperties);
      });
    }
  }

  strataChange(condo) {
    if (condo !== CondoTypeEnum.Strata || condo !== CondoTypeEnum.ChildFreehold) {
      this.propertyForm.get('CondoUnit').markAsDirty();
      this.propertyForm.get('MasterPropertyId').markAsDirty();
      this.propertyForm.get('CondoUnit').markAsUntouched();
      this.propertyForm.get('MasterPropertyId').markAsUntouched();
      this.property.MasterPropertyId = null;
      this.property.CondoUnit = null;
    }
  }
  resetControls() {
    this.showMasterStrataAlert = false;
    this.propertyForm.get('Condo').enable();
  }

  buildAddress() {
    const addressDetails = buildAddressDetails({
      address: this.property.Address,
      streetSuffixes: this.streetSufixes,
      cities: this.cities,
      states: this.states
    });
    this.StreetNumber = addressDetails.StreetNumber;
    this.StreetSuffix1Text = addressDetails.StreetSuffix1Text;
    this.CityName = addressDetails.CityName;
    this.StateName = addressDetails.StateName;
  }


  checkStrataMinMaxError() {
    const pRangeMin = this.rangeForm.get('StrataMin').value;
    const pRangeMax = this.rangeForm.get('StrataMax').value;
    const strataMin = parseInt(pRangeMin);
    const strataMax = parseInt(pRangeMax);

    if (!strataMin || !strataMax) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMinMaxUnitMessage);
      this.StrataMinMaxError = true;
      return;
    }
    const units = strataMax - strataMin;

    if (strataMax <= strataMin) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMaxUnitMessage);
      this.StrataMinMaxError = true;
      return;
    }

    if ((units + 1) > 30) {
      this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataUnitsExceedingMessage);
      this.StrataMinMaxError = true;
      return
    }

    this.StrataMinMaxError = false;
  }

  setLotInformation() {
    if (this.selectedParcelInfo) {
      this.property.LotSizeSF = this.selectedParcelInfo.Lot_Area;
      // Set the default value of LotSizeSource to 'County Source Data'.
      if (!this.property.LotSizeSourceID) {
        this.property.LotSizeSourceID = LotSizeSourceEnum.CountyDataSource;
      }
    } 
  }

  onPropertyUseChange(valueChanged = false, UseTypeID, previousUseType) {
    if (!!UseTypeID && valueChanged) {
      const value = UseTypeID;
      if (UseTypeID === this.propertyTypeValues.Land) {
        this.IsUsetypeChanged = true;
        if(this.multifloors.length>0){
          this.multiFloorConfirmation(previousUseType);
        }else{
          this.property.CondoTypeID = null;
          this.property.MasterPropertyId = null;
          this.property.CondoUnit = null;
          this.property.BuildingSF = 0;
        }
      }
    }
    if (!!event) {
      const value = UseTypeID;
      this.addSpecificValidation(value);

    }
    this.isIndustrial = false;
    this.isOffice = false;
    this.isRetail = false;
    switch (~~this.property.UseTypeID) {
      case 3:
        this.isIndustrial = true;
        break;
      case 2:
        this.isRetail = true;
        break;
      case 5:
        this.isOffice = true;
        break;
    }
    if (this.property.UseTypeID && this.property.UseTypeID != 0) {
      if (!!valueChanged) {
        this.property.SpecificUseID = 0;
        const formName = getFormNameByUseType(this.property?.UseTypeID);
        this.propertyForm?.get(formName)?.get('SpecificUse')?.updateValueAndValidity();
      }

      this.specificUses = [];
      if (!!this.allspecificuses && this.allspecificuses.length > 0) {
        this.allspecificuses.forEach(item => {
          if (item.UseTypeID == this.property.UseTypeID) {
            this.specificUses.push(item);
          }
        });
      } else {
        this.specificUses = (this.propertyLookups['SpecificUsesID'] || []).filter(use => use.UseTypeId === this.property.UseTypeID);
      }
    }
  }

  checkForValidFloorSizes = () => {
    const buildingSFStatus = this.property.CondoTypeID === CondoTypeEnum.MasterFreehold || this.property.UseTypeID === this.propertyTypeValues.Land || this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.footPrintNotAvailable ? true : this.propertyForm.controls.BuildingSF.valid;
    if (buildingSFStatus) {
      return true;
    }
    return false;
  }

  checkForMandatoryFieldsinTopSection() {    
      const isLotSizeSourceValid =    this.propertyForm?.controls['LotSizeSourceID']?.enabled ? this.propertyForm?.controls['LotSizeSourceID']?.valid : true;
      const isRecordTypeValid = this.property?.UseTypeID === this.propertyTypeValues?.Land ? true :  this.propertyForm?.controls['Condo']?.valid;
      const isConstructionStatus = this.propertyForm.controls['ConstructionStatus']?.disabled ? true : this.propertyForm.controls['ConstructionStatus']?.valid
      return (this.propertyForm.controls['ConstructionType']?.valid && isConstructionStatus && isLotSizeSourceValid && isRecordTypeValid);
  }

  isPinLocationValid() {
    if (([CondoTypeEnum.MasterStrataRecord, CondoTypeEnum.MasterFreehold] as PropertyDetailsDTO.CondoTypeIDEnum[]).includes(this.property.CondoTypeID) || this.footPrintNotAvailable) {
      return true;
    }
    if (!this.multifloors || !this.multifloors.length) {
      return true;
    } else {
      return this.multifloors.some((floor) => {
        return this._mapService.checkPinLocationWithFootprint(floor.shape, {lat: Number(this.property.Location.Latitude), lng: Number(this.property.Location.Longitude)})
      })
    }
  }

  async savePropertyDetails(isAutoClose) {
    const changed = JSON.stringify(this.propertyForm.value) != this.initialPropertyForm;
    const isValid = this.propertyForm.dirty || changed || this.property.IsReviewed;
    const isValidMultiStrata = this.isMultiStrata ? this.isMultiStrata && !this.StrataMinMaxError : true;
    let researchStatusValidationError: boolean = false;
    //Research status validation check
    if (this.selectedOption === ResearchType.NeedsResearch) {
      if ((this.property && (this.property.IsMultiplePolygonsNeeded || (this.property.NeedsResearchComments && this.property.NeedsResearchComments.length >= 25))) || false
      ) {
        researchStatusValidationError = false;
      } else {
        researchStatusValidationError = true;
      }
    }

    if (!this.property.UseTypeID) {
      const message = CommonStrings.DialogConfigurations.Messages.SelectPropertyUseMessage;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok };
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    }

    const formName = getFormNameByUseType(this.property?.UseTypeID); 
    const useTypeForm = this.propertyForm?.get(formName) as FormGroup;
    if (!useTypeForm) {
      return;
    }

    // check for mandatory fields and validations
    if (this.isAnExistingProperty && this.property?.CondoTypeID === CondoTypeEnum.MasterStrataRecord) {
      let areAllFieldsValid = true;
      if (!useTypeForm?.valid) {
        const controls = useTypeForm?.controls;
        //Skip check for LargestFloor and SmallestFloor if record type is Master
        for (const key in controls) {
          if (key !== OfficeControls?.LargestFloor && key !== OfficeControls?.SmallestFloor) {
            const control = controls[key];
            if (control && !control.valid && control.enabled) {
              areAllFieldsValid = false;
            }
          }
        }
      }
      if (!this.propertyForm?.get('locationDetailsForm')?.valid || !areAllFieldsValid) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
        return;
      }
    } else if (this.isAnExistingProperty && (!this.propertyForm?.get('locationDetailsForm')?.valid || (!useTypeForm?.valid && this.property?.CondoTypeID !== CondoTypeEnum?.MasterFreehold))) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
      return;
    } 

    const isMasterStrata = this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold || this.isMultiStrata;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;

    const isBuildingSqmLessThanOne = this.propertyForm.controls['BuildingSF'].value ? parseInt(this.propertyForm.controls['BuildingSF'].value) < 1 : true;
    if (this.footPrintNotAvailable) {
      if (this.isAnExistingProperty) {
        if (useTypeForm.get('ContributedGBA_SF')?.valid && !useTypeForm.get('ContributedGBA_SF')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBAErrorMessage);
          return;
        } else if (!useTypeForm.get('ContributedGBASource')?.valid) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBASourceErrorMessage);
          return;
        }
      } else {
        if (this.propertyForm.get('ContributedGBA_SF')?.valid && !this.propertyForm.get('ContributedGBA_SF')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBAErrorMessage);
          return;
        } else if (!this.propertyForm.get('ContributedGBASource')?.value) {
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ContributedGBASourceErrorMessage);
          return;
        }
      } 
    } else if (!isMasterStrata && !isLand) {
      if (this.property.CondoTypeID !== CondoTypeEnum.MasterFreehold && this.property.CondoTypeID !== CondoTypeEnum.MasterStrataRecord && isBuildingSqmLessThanOne && !isLand) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
        return
      }
    } else {
      if (this.property.CondoTypeID !== CondoTypeEnum.MasterFreehold && this.property.CondoTypeID !== CondoTypeEnum.MasterStrataRecord && isBuildingSqmLessThanOne && !isLand) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage);
        return
      }
    }

    if ((isValid|| this.researchChanged || this.isDeleteFloor || this.isFootprintModified || this.property.IsSkipped !== this.IsSkipped)&& this.isPinLocationValid() && this.checkForMandatoryFieldsinTopSection() && !researchStatusValidationError && this.selectedOption && isValidMultiStrata && this.checkForValidFloorSizes()) {
      const strataValidationError = this.property.CondoTypeID === CondoTypeEnum.Strata && !this.isMultiStrata &&
        (!this.propertyForm.controls[PropertyFormControls.MasterPropertyId].valid || !this.propertyForm.controls[PropertyFormControls.CondoUnit].valid);
      if (!!this.property.UseTypeID && this.checkMasterSizes() && !this.clearHeightMinError && !this.streetMinMaxError && !this.buildingSFError && !this.noOfOfficeFloorError && !this.yearRenovatedError && !strataValidationError && !this.GRESBScoreError) {
        if (this.isMultiStrata || this.property.PropertyID || (await this.hasbothStreetAndAerialImages())) {
          this.setLotInformation();
          this.validationError = false;
          this.redirectionLoader = true;
          this.property.HasNoBuildingFootprints = this.footPrintNotAvailable ? true : false;
          if (!this.isAnExistingProperty) {
            this.property.SizeSourceID = SizeSourceEnum.AerialEstimation; // Areal estimation
          }
          let propertyCopy = JSON.parse(JSON.stringify(this.property));
          this.footPrintNotAvailable = false;

          preparePropertyData({ propertyCopy, property: this.property, isAnExistingProperty: this.isAnExistingProperty, hasNoExistingParcelInTileLayer: this.hasNoExistingParcelInTileLayer,
            CountryId: this.CountryId, propertyService: this._propertyService, initialDetails: this.initialDetails, isMultiStrata: this.isMultiStrata, 
            addressTypeValues: this.addressTypeValues, streetPrefixes: this.streetPrefixes, streetSufixes: this.streetSufixes, quadrants: this.quadrants});
          if (this.isAnExistingProperty && this.isFootprintModified && (this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold)) {
            this.propertyAllocation?.updateAllocationsData([], this.property.PropertyID);
          }
          if (this.isMultiStrata && !this.property.PropertyID) {
            this.propertyCopy = JSON.parse(JSON.stringify(propertyCopy));
            const pRangeMin = parseInt(this.pRange.StrataMin);
            const pRangeMax = parseInt(this.pRange.StrataMax);
            this.saveMultiStrata(pRangeMin, pRangeMax, true);
          } else if (this.property.PropertyID && this.sharedDataService.deleteBuildingFootPrintIds && this.sharedDataService.deleteBuildingFootPrintIds.length > 0) {
            var buildingFootPrintIDArr = this.sharedDataService.deleteBuildingFootPrintIds;
            this.buildingFootprintService.deleteBuildingFootprints( this.property.PropertyID, buildingFootPrintIDArr.join(',')).subscribe(
              (result: ApiResponseVoid) => {
                if (!result.error) {
                  this.sharedDataService.deleteBuildingFootPrintIds = [];
                  this.savePropertyInfo(propertyCopy, isAutoClose);
                } else {
                  this.redirectionLoader = false;
                  this._notificationService.ShowErrorMessage(result.message);
                }
              }, error => {
                this.redirectionLoader = false;
                this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToDeleteBuildingFootprints);
              }
            );
          } else {
            this.savePropertyInfo(propertyCopy, isAutoClose);
          }
          
        } else {
          this.addPropertyImages();
        }
      }
      else {
        this.validationError = true;
      }
      this.sharedDataService.deleteBuildingFootPrintIds = [];
      this.researchChanged = false;
      this.initialPropertyForm = JSON.stringify(this.propertyForm.value);
    }
    else {
      if (!this.checkForValidFloorSizes() && this.isAnExistingProperty) {
        if (this.property.CondoTypeID !== CondoTypeEnum.MasterFreehold && !isLand && !this.propertyForm.controls['BuildingSF'].valid && this.property.CondoTypeID !== CondoTypeEnum.MasterStrataRecord) {
          isMasterStrata ? this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage) : this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
        }
      } else if (this.property.CondoTypeID !== CondoTypeEnum.MasterFreehold && this.property.CondoTypeID !== CondoTypeEnum.MasterStrataRecord && !this.footPrintNotAvailable && !isLand && !this.propertyForm.controls['BuildingSF'].valid) {
        isMasterStrata ? this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MasterStrataBuildingErrorMessage) : this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintErrorMessage);
      } else if (researchStatusValidationError) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.MinimumCommentsLengthErrorMessage);
      } else if (this.StrataMinMaxError) {
        this._notificationService.ShowErrorMessage(CommonStrings.DialogConfigurations.Messages.StrataMinMaxUnitMessage);
      } else if (!this.selectedOption) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.ResearchStatusErrorMessage);
      } else if (!this.isPinLocationValid()) {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PinInsideFootPrint);
      } else if (!this.checkForMandatoryFieldsinTopSection()){
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FillAllMandatoryfields);
      } else {
        this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.NoPropertyChanges);
      } 
    }
  }

  isPropertyNameAndAddressValid(){
    return this.property.PropertyName && this.property.Address.AddressStreetName && this.property.Address.StreetNumberMin;
  }

  onClosePropertyNameModal(){
    this.showPropertyNameModal = false;
  }

  onSavePropertyAddress(){
    this.showPropertyNameModal = false;
    //Build property name from location details
    this.addressAsPropertyName(false);
    this.checkForMultiFloors(this.isAutoClose);
  }

  propertySave(isAutoClose: boolean = true) { 
    if (!this.property.Location.Latitude && !this.property.Location.Longitude && !this.isAnExistingProperty) {
      const message = CommonStrings.DialogConfigurations.Messages.DropPinMessage;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok };
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
      return
    }
    if (this.isAnExistingProperty) {
      this.checkForMultiFloors(isAutoClose);
    } else {
      if (this.isPropertyNameAndAddressValid()) {
        this.checkForMultiFloors(isAutoClose);
      } else {
        this.showPropertyNameModal = true;
        this.isAutoClose = isAutoClose;
      }
    }

  }

  checkForMultiFloors(isAutoClose){
    if (this.aggregateParcelSize) {
      this.property.LotSizeSF = this.aggregateParcelSize;
    }
    // If a new property is being added, and it does not have a lot size, and the condo type is neither Strata nor Child Freehold, display a notification prompting the user to select a parcel.
    if (!this.isAnExistingProperty && !this.property?.LotSizeSF && this.property.CondoTypeID !== CondoTypeEnum?.Strata && this.property?.CondoTypeID !== CondoTypeEnum?.ChildFreehold) {
      this.checkForParcelLayer();
      return;
    } 
    const isMasterStrata = this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord || this.property.CondoTypeID === CondoTypeEnum.MasterFreehold || this.isMultiStrata;
    const isLand = this.property.UseTypeID === this.propertyTypeValues.Land;
    let floorsWithFootprint = [];
    if(isLand){
      this.multifloors = []
    }else{
      this.multifloors.forEach(floor => {
        if (floor.floorSize) {
          const range = (floor.minFloor && floor.maxFloor) ? getRange(floor.minFloor, floor.maxFloor) : floor.minFloor ? [floor.minFloor] : [floor.maxFloor];
          floorsWithFootprint = [...floorsWithFootprint, ...range];
        }
      });  
    }
    const propertyRange = getRange(1, this.property.Floors);

    //Showing error if any of the polygons are missing bulding footprints
    const isFootPrintsMissing = this.multifloors.some(floor => !floor.floorSize || !floor.shape);
    if (isFootPrintsMissing) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.FootprintsAreMissing);
      return;
    }

    const allFloorsAllocated = propertyRange.every(element => floorsWithFootprint.includes(element)) || isMasterStrata || isLand || this.footPrintNotAvailable;
    if (!isMasterStrata && !this.footPrintNotAvailable && !this.multifloors.some(floor => floor.floorSize > 0) && !isLand) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.AtleastOneFootprintIsRequired);
      return;
    }

    if (!allFloorsAllocated) {
      const message = CommonStrings.DialogConfigurations.Messages.AllFloorsDoesntHaveFootprintMessage;
      const title = CommonStrings.DialogConfigurations.Title.FloorwisePolygon;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Yes, Callback: () => { this.savePropertyDetails(isAutoClose) } };
      const cancelButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.No, Callback: () => { } };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    } else {
      this.savePropertyDetails(isAutoClose);
    }
  }

  savePropertyInfo(propertyCopy: any, isAutoClose: boolean = true) {
    if (this._loginService.UserInfo.RoleID == LoginUserInfoDTO.RoleIDEnum.Auditor && this.isAnExistingProperty) {
      const auditStatus = this.auditStatusList.find(status => status.StatusName == "Corrected and Verified");
      this.property.PropertyAuditStatusID = auditStatus.StatusDefinationID;
    }
    const response = this._propertyService.saveProperty(propertyCopy);
    response.subscribe((result: ApiResponsePropertyResponseDTO) => {
      if(result.error) {
        this.redirectionLoader = false;
        this.onSave(isAutoClose, this.property.PropertyID);
        this._notificationService.ShowErrorMessage(result.message);
      } else if (result.status === HttpStatus.CREATED) {
        this.redirectionLoader = false;
        this._notificationService.ShowErrorMessage(result.message);
      } else if (result.status === HttpStatus.OK) {
        if (!result.error) {
          const propertyData = result.responseData;
          this.property.PropertyID = propertyCopy.PropertyID = propertyData.propertyID;
          propertyCopy.CountryID = this.CountryId;
          propertyCopy.IsSkipped = this.IsSkipped;
          this.propertyLocationChanged = false;
          this.sharedDataService.deleteBuildingFootPrintIds = [];
          this.isAddPolygonButtonDisabled = false;
          this.clearParkingRetailAndHardStandPolygons();
          if (!this.isAnExistingProperty) {
            this.fetchAndProcessStagingMedia(this.property.PropertyID)   //processing the staging media before procedding further
          }
          this.onSaveProperty(propertyCopy, isAutoClose);
        } else {
          this.redirectionLoader = false;
          this.onSave(isAutoClose, this.property.PropertyID);
          this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.PropertySaveFail);
        }
      } else {
        this.redirectionLoader = false;
        this.onSave(isAutoClose, this.property.PropertyID);
        if (!this.isAnExistingProperty) {
          this.isAddPolygonButtonDisabled = false;
        }
      }
    },
    error => {
      this.redirectionLoader = false;
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.PropertySaveFail)
    });
  }

  onSaveProperty(property: PropertyDetailsDTO, isAutoClose: boolean = true) {
    const propertyId = property.PropertyID;
    if (this.isFootprintModified && this.multifloors && this.multifloors.length > 0) {
      const MultiBuildingFootPrintData: BuildingFootPrintRequestDTO[] = this.multifloors
        .map(({ shape, BuildingFootPrintID, floorSize, minFloor, maxFloor, specificUse, description, additionalUse, additionalSpecificUseTypeId, mainSpecificUseTypeId }) => ({
          BuildingFootPrint: shape ? `POLYGON((${shape}))` : null,
          BuildingFootPrintId: BuildingFootPrintID || null,
          Area: floorSize,
          MinFloorNumber: minFloor || maxFloor,
          MaxFloorNumber: maxFloor || minFloor,
          UseTypeId: specificUse,
          Description: description,
          AdditionalUseTypeId: additionalUse,
          AdditionalSpecificUseTypeId: additionalSpecificUseTypeId,
          MainSpecificUseTypeId: mainSpecificUseTypeId
        }))
        .filter(item => item.BuildingFootPrint);
      const requestBody: BuildingFootprintCollectionRequestDTO = {
          BuildingFootPrints: MultiBuildingFootPrintData,
          PropertyId: property.PropertyID,
          IsNewProperty: !this.isAnExistingProperty
        };
      this.buildingFootprintService.saveMultiFootprint(requestBody).subscribe(async (result: ApiResponseListBuildingFootPrintDTO) => {
        const response = result;
        if (!response.error) {
          let commModel = new CommunicationModel();
          commModel.Key = 'updatedBuildingFootprintAddedForProperty';
          commModel.data = { result, PropertyID: property.PropertyID };
          this._communicationService.broadcast(commModel);
          this.redirectionLoader = false;
          this.isFootprintModified = false;
        } else {
          this.redirectionLoader = false;
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this.redirectionLoader = false;
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveOrUpdateBuildingFootprints);
      })
      this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
      localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
      this.multifloors = [];
      this.floorsWithFootprint = {};
    } else {
      this.redirectionLoader = false;
    }
    if (this.isAnExistingProperty) {
      this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
      localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
    } else {
      this.savePropertyParcelInformation(propertyId);
    }
    this.onSave(isAutoClose, property.PropertyID);
    this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull);
    this.propertyForm.reset();
  }

  onResearchStatusChange(PropertyResearchTypeID, PropertyResearchStatusID, status) {
    this.selectedOption = PropertyResearchTypeID;
    this.property.PropertyResearchTypeID = PropertyResearchTypeID
    this.clearValidator(PropertyResearchTypeID, status);
    this.researchChanged = true
  }

  clearValidator(PropertyResearchTypeID, status) {
    if (PropertyResearchTypeID === 4) {

      if (status) {
        this.propertyForm.clearValidators();
        this.propertyForm.setErrors({ 'invalid': false });
        this.propertyForm.setErrors(null);
        if (!this.isAnExistingProperty) {
          this.applyValidation('clear');
        }

      } else {
        if (!this.isAnExistingProperty) {
          this.addSpecificValidation(this.property.UseTypeID ? this.property.UseTypeID : 5);
        }
      }
      this.applyValidation('update');
    }
  }

  getPropertyResearchStatus(propertyId) {
    setTimeout(()=>{
      this.selectedOption = this.property.PropertyResearchTypeID
    }, 300)
  }

  onCancel() {
    if (this.isAnExistingProperty) {
      if (this.isNavigationFromSearch) {
        this.visitedPropertyIds.push(this.property.PropertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
      }
      this.clearStrataSessionStorageData();
      this.loaderMasterPIDAfterChildsVisit = false;
      this.cancelConfirmation(true, () => { this.propertySave(true); }, () => {
        this._notificationService.ShowErrorMessage("Property save cancelled");
        this.onComplete.emit(false);
        this.sharedDataService.deleteBuildingFootPrintIds = [];
      });
    } else {
      this.cancelConfirmation(true, () => {
        this.propertySave(true);
      }, () => {
        this.clearParkingRetailAndHardStandPolygons();
        this._notificationService.ShowErrorMessage("Property save cancelled");
        this.completeChanges();
        this.onComplete.emit(false);
      });
    }
  }

  private cancelConfirmation(isAutoClose: boolean, onSave: () => void, onDoNotSave: () => void, onCancel?: () => void) {
    if (this.propertyForm.dirty || this.isDeleteFloor) {
      const message: string = CommonStrings.DialogConfigurations.Messages.CancelChangesConfirmationMessage;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.SaveChanges, Callback: () => { onSave(); }};
      const cancelBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.DoNotSave, Callback: () => { onDoNotSave(); }};
      const additionalBtn = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Cancel, Callback: () => { if (onCancel) { onCancel(); }}, Classname: 'btn btn-danger'};
      this.showDialogConfirmation(message, title, okButton, cancelBtn, additionalBtn);
    }
    else if (isAutoClose) {
      this.clearParkingRetailAndHardStandPolygons();
      if (this.isAnExistingProperty) {
        this.onComplete.emit(false);
        this.sharedDataService.deleteBuildingFootPrintIds = [];
      } else {
        this.completeChanges();
        this.onComplete.emit(false);
      }
    } else {
      onDoNotSave();
    }
  }

  clearParkingRetailAndHardStandPolygons() {
    this.parkingPolygon = { singleSlotPolygon: { polygonId: null, area: 0, polygon: null }, parkingAreaPolygon: [], parkingSpaces: null };
    this.hardstandAreaPolygon = null;
    this.retailFrontagePolyline = null;
    let commModel = new CommunicationModel();
    commModel.Key = 'onClearFrontage';
    commModel.data = true;
    this._communicationService.broadcast(commModel);
  }

  completeChanges() {
    this.isPolygonCollapsed = true;
    this.footPrintNotAvailable = false;
    this.hasNoExistingParcelInTileLayer = false;
    this.showParcelWindow = false;
    this.disableStrataBtn = false;
    this.isMultiStrata = false;
    this.initialDetails = new mapEditPropertyDTO();
    this.pRange = { StrataMin: '', StrataMax: '' };
    this.property = new PropertyDetails();
    this.isNewProperty = true;
    this.displayPropertyId = 0;
    this.selectedOption = null;
    this.selectedMasterParcel = null;
    this.LastStrataUnit = null;
    this.aggregateParcelSize = 0;
    this.parcelCount = 0;
    this.parcelNumber = undefined;
    this.newPropertyLocation = undefined;
    this.showStrataOrFreeholdTab = false;
    this.resetControls();
    let commModel = new CommunicationModel();
    commModel.Key = 'ClearPolygonShape';
    this._communicationService.broadcast(commModel);
    this.isAddPolygonButtonDisabled = false;
    // Clear the previously stored property parcel information upon completion of saving.
    if (this._sharedDataService) {
      this._sharedDataService.parcelInfoPickedFromTileLayer = null;
      this.parcelInformation = null;
      this.selectedParcelInfo = null;
    }
    this.selectedParcels = [];
    this.initData();
  }

  setPropertyRecordDisplayNumber(index) {
    this.currentPropertyRowNumber = (index + 1).toString().padStart(3, '0');
  }

  getNextProperty(list: any[], currentIndex: number) {
    if (currentIndex >= 0 && currentIndex < list.length - 1) {
      const nextIndex = this.getNextPropertyIndex(list, currentIndex);
      if (nextIndex !== -1) {
        sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(nextIndex));
        this.setPropertyRecordDisplayNumber(nextIndex);
        return list[nextIndex];
      } else {
        this.onComplete.emit(false);
      }
    } else if (currentIndex === list.length - 1) {
      this.onComplete.emit(false);
    }
  }


  getNextPropertyIndex(list: any[], currentIndex: number): number {
    let nextIndex = currentIndex + 1;
    let propertyIdsList = this._loginService.UserInfo.RoleID == LoginUserInfoDTO.RoleIDEnum.Auditor ? [] : this.navigationPreference === NavigationPreferences.UnVisited ? this.visitedPropertyIds : this.editedPropertyIds;
    while (nextIndex < list.length) {
      let propertyId = list[nextIndex].PropertyId;
      if (!propertyIdsList.includes(propertyId)) {
        return nextIndex;
      }
      nextIndex++;
    }
    return -1;
  }

  getNextStrataPropertyIndex(currentPropertyIndex, strataIds) {
    let propertyIdsList = this._loginService.UserInfo.RoleID == LoginUserInfoDTO.RoleIDEnum.Auditor ? [] : this.isNavigationFromSearch ? this.navigationPreference === NavigationPreferences.UnVisited ? this.visitedPropertyIds : this.editedPropertyIds : this.visitedStrataIdsFromSS;
    let nextIndex = currentPropertyIndex + 1;
    while (nextIndex < strataIds.length) {
      let propertyId = strataIds[nextIndex];
      if (!propertyIdsList.includes(propertyId)) {
        return nextIndex;
      }
      nextIndex++;
    }
    return -1;
  }


  async setActiveGridRowNumber(propertyId) {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyList);
      if (searchData) {
        const propertyList = searchData.value.propertyList;
        if (propertyList) {
          const index = propertyList.findIndex(property => property.PropertyId === propertyId);
          if (index > 0) {
            this.currentPropertyRowNumber = (index + 1).toString().padStart(3, '0');
            sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(index));
          }
        }
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  handleSaveAndNextForStrataProperty(propertyID, strataIds, masterPID) {
    const currentPropertyIndex = strataIds.findIndex(id => id === propertyID);
    const nextPropertyIndex = this.getNextStrataPropertyIndex(currentPropertyIndex, strataIds);
    if (currentPropertyIndex >= 0 && !(nextPropertyIndex > (strataIds.length - 1)) && nextPropertyIndex != -1) {
      // To Load Selected Property Data, resetting the existing form
      this.propertyForm.reset();
      // Resetting  to send selected Property Change Log
      this.DataArray = [];
      this.fetchStrataProperty.emit(strataIds[nextPropertyIndex]);
      this.visitedStrataIdsFromSS.push(propertyID);
      sessionStorage.setItem(SessionStorageKeys.VisitedStrataIds, JSON.stringify(this.visitedStrataIdsFromSS));
      this.completeChanges();
      if (this.isNavigationFromSearch) {
        this.visitedPropertyIds.push(propertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
        this.setActiveGridRowNumber(strataIds[nextPropertyIndex]);
      }
    } else {
      if ((nextPropertyIndex > (strataIds.length - 1) || nextPropertyIndex == -1) && masterPID) {
        this.propertyForm.reset();
        this.DataArray = [];
        this.clearStrataSessionStorageData();
        this.loaderMasterPIDAfterChildsVisit = true;
        this.fetchProperty.emit(masterPID);
      } else {
        this.clearStrataSessionStorageData();
        if (sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch)) {
          const index = parseInt(this.activeGridRowNumber);
          const PropertyListFromSessionStorage = JSON.parse(sessionStorage.getItem(SessionStorageKeys.SearchResults)).CurrentPagePropertyList;
          const prop = PropertyListFromSessionStorage[index];
          this.handleSaveAndNextIfNavigationIsFromSearch(prop.PropertyId);
        } else {
          this.onComplete.emit(true);
        }
      }

    }
  }

  checkForParcelLayer() {
    if (!(this.selectedParcelInfo || (this.selectedParcels && this.selectedParcels.length > 0))) {
      let commModel = new CommunicationModel();
      commModel.Key = 'EnableParcel';
      commModel.data = this.property.CondoTypeID;
      this._communicationService.broadcast(commModel);
      setTimeout(()=>{      
        this.completeChanges();
        this.onComplete.emit(false);},200)
      this.footPrintNotAvailable = false;
      this._notificationService.ShowWarningMessage(CommonStrings.DialogConfigurations.Messages.PleaseEnableAndSelectParcelLayer);
    }
  }

  handleSaveAndNextIfNavigationIsFromSearch(propertyID) {
    this.visitedPropertyIds.push(propertyID);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    const PropertyListFromSessionStorage = sessionStorage.getItem(SessionStorageKeys.SearchResults)
    if (PropertyListFromSessionStorage) {
      const propertyList = JSON.parse(PropertyListFromSessionStorage).CurrentPagePropertyList || [];
      const propertyIndex = propertyList.findIndex(property => property.PropertyId === propertyID);
      if (propertyIndex !== -1) {
        const nextProperty = this.getNextProperty(propertyList, propertyIndex);
        const nextPropertyId = nextProperty?.PropertyId;
        this.propertyForm.reset();
        this.DataArray = [];
        const propertyLocation = { Latitude: nextProperty.Latitude, Longitude: nextProperty.Longitude };
        this.fetchNextProperty.emit({ nextPropertyId, propertyLocation });
      } else {
        this.onComplete.emit(false);
      }
    } else {
      this.onComplete.emit(false);
    }
  }


  onClickNextHandler() {
    const changed = JSON.stringify(this.propertyForm.value) !== this.initialPropertyForm;
    const isValid = this.propertyForm.dirty || changed;
    if (isValid) {
      const configuration: confirmConfiguration = new confirmConfiguration();
      configuration.Message = CommonStrings.DialogConfigurations.Messages.CancelChangesConfirmationMessage;
      configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
      configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
      configuration.OkButton.Callback = () => {
        this.saveAndNextHandler(true, this.property.PropertyID);
      }
      configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.No;
      configuration.CancelButton.Callback = () => {

      }
      this._notificationService.CustomDialog(configuration);
    } else {
      this.saveAndNextHandler(true, this.property.PropertyID);
    }
  }


  saveAndNextHandler(isAutoClose: boolean = true, propertyID) {
    const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
    if (this.isNavigationFromSearch) {
      this.visitedPropertyIds.push(propertyID);
      this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    }
    if (isAutoClose) {
      if (this.isNavigationFromSearch) {
        if (strataIds && !this.loaderMasterPIDAfterChildsVisit && this.property) {
          this.handleSaveAndNextForStrataProperty(propertyID, strataIds, this.property.MasterPropertyId);
        } else {
          this.handleSaveAndNextIfNavigationIsFromSearch(propertyID);
        }
      } else {
        if (strataIds && !this.loaderMasterPIDAfterChildsVisit && this.property) {
          this.handleSaveAndNextForStrataProperty(propertyID, strataIds, this.property.MasterPropertyId);
        } else {
          this.onComplete.emit(true);
        }
      }
    }
    else {
      this.propertyForm.reset();
      this.DataArray = [];
      this.fetchProperty.emit(propertyID);
    }
  }



  onSave(isAutoClose: boolean = true, propertyID) {
    
    if (this.isAnExistingProperty) {
      const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
      if (strataIds) {
        this.editedStrataIds.push(propertyID);
        sessionStorage.setItem(SessionStorageKeys.EditedStrataIds, JSON.stringify(this.editedStrataIds));
      }
      if (this.isNavigationFromSearch) {
        this.editedPropertyIds.push(propertyID);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.EditedPropertyIds, value: this.editedPropertyIds });
      }
      this.saveAndNextHandler(isAutoClose, propertyID)
    } else {
      if (isAutoClose) {
        this.completeChanges();
        if (this.initialDetails.fromMasterStrata) {
          this.fetchProperty.emit(this.initialDetails.masterStrataObj.property.PropertyID);
        } else {
          this.onComplete.emit(true);
        }
      } else {
        this.completeChanges();
        this.fetchProperty.emit(propertyID)
      }
    }
  }

  clearStrataSessionStorageData() {
    sessionStorage.removeItem(SessionStorageKeys.StrataPropertyIds);
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    sessionStorage.removeItem(SessionStorageKeys.EditedStrataIds);
  }

  addressAsPropertyName(IsChange: boolean) {
    if (this.isAnExistingProperty) {
      this.StreetMinMaxValidation();
    }
    if (this.property.UseAddressAsPropertyName) {
      this.property.PropertyName = '';
      if (this.property.Address.AddressType == !!this.addressTypeValues.Address) {
        if (this.property.Address.StreetNumberMin)
          this.property.PropertyName = this.property.Address.StreetNumberMin.toString();
        if (this.property.Address.StreetNumberMax) {
          if (this.property.Address.StreetNumberMin == this.property.Address.StreetNumberMax) {
            this.property.PropertyName += ' ';
          } else {
            this.property.PropertyName += this.property.Address.StreetNumberMin ? ' -' : '';
            this.property.PropertyName += ` ${this.property.Address.StreetNumberMax.toString()} `;
          }
        } else {
          this.property.PropertyName += ' ';
        }

        if (this.property.Address.StreetPrefix1) {
          this.property.PropertyName = this.property.PropertyName + this.streetPrefixes.find(x => x.PrefixEnum == this.property.Address.StreetPrefix1)?.PrefixName + ' ';
        }
        if (this.property.Address.StreetPrefix2) {
          this.property.PropertyName = this.property?.PropertyName + this.streetPrefixes?.find(x => x?.PrefixEnum == this.property.Address?.StreetPrefix2)?.PrefixName + ' ';
        }
        if (this.property.Address.AddressStreetName) {
          this.property.PropertyName = this.property.PropertyName + this.property.Address.AddressStreetName + ' ';
        }
        if (this.property.Address.StreetSuffix1) {
          this.property.PropertyName = this.property.PropertyName + this.streetSufixes.find(x => x.StreetSuffix1 == this.property.Address.StreetSuffix1)?.Suffix + ' ';
        }
        if (this.property.Address.StreetSuffix2) {
          this.property.PropertyName = this.property.PropertyName + this.streetSufixes.find(x => x.StreetSuffix1 == this.property.Address.StreetSuffix2)?.Suffix + ' ';
        }
      }
      else {
        if (this.property.Address.Quadrant) {
          this.property.PropertyName = this.property.PropertyName + this.quadrants.find(x => x.QuadrantEnum == this.property.Address.Quadrant).QuadrantName + ' ';
        }
        if (this.property.Address.EastWestStreet) {
          this.property.PropertyName = this.property.PropertyName + this.property.Address.EastWestStreet + ' ';
        }
        if (this.property.Address.NorthSouthStreet) {
          this.property.PropertyName = this.property.PropertyName + this.property.Address.NorthSouthStreet + ' ';
        }
      }
      if (this.property.CondoTypeID === CondoTypeEnum.Strata && this.property.CondoUnit) {
        this.property.PropertyName = `${this.property.CondoUnit}/${this.property.PropertyName.slice(0, -1)}`;
      } else {
        this.property.PropertyName = this.property.PropertyName.slice(0, -1);
      }
      // Marking for dirty if the property name is changed on edit property
      if (this.isAnExistingProperty)
        this.onChangeSetAsDirty('PropertyName');
    }
    else if (IsChange) {
      this.property.PropertyName = this.previousPropertyName;
    }
  }

  StreetMinMaxValidation() {
    if (this.property.Address.StreetNumberMin && this.property.Address.StreetNumberMax && parseInt(this.property.Address.StreetNumberMin.toString()) > parseInt(this.property.Address.StreetNumberMax.toString())) {
      this.streetMinMaxError = true;
    } else {
      this.streetMinMaxError = false;
    }
  }

  ValidateBuildingSize() {
    if (this.property.LargestFloor > this.property.BuildingSF) {
      this.maxFloorError = true;
    } else {
      this.maxFloorError = false;
    }
    if (this.property.LargestFloor > this.property.BuildingSF) {
      this.maxFloorError = true;
    } else {
      this.maxFloorError = false;
    }
  }

  checkValidations() {
    this.clearHeightMinError = false;
    this.buildingSFError = false;
    this.noOfOfficeFloorError = false;
    if ((this.property.ClearHeightMin && this.property.ClearHeightMax) && (this.property.ClearHeightMin > this.property.ClearHeightMax)) {
      this.clearHeightMinError = true;
    }
    if ((this.property.BuildingSF && this.property.OfficeSF) && (Number(this.property.BuildingSF) < Number(this.property.OfficeSF))) {
      this.buildingSFError = true;
    }
    if ((this.property.NoOfOfficeFloor && this.property.Floors) && (this.property.Floors < this.property.NoOfOfficeFloor)) {
      this.noOfOfficeFloorError = true;
    }
  }
  closeMediaUploadModal() {
    this.files = new Array<MediaInfoDTO>();
    this.showFileUpload = false
  }
  onUploadEvent(path: string) {
    this.closeMediaUploadModal();
  }

  closeNoteModal() {
    this.selectedNote = null;
    this.showNoteModal = false;
    let model = new CommunicationModel();
    model.Key = 'onNoteClose';
    this._communicationService.broadcast(model);
  }
  closeMapModal() {
    this.showMap = false;
  }

  showMapModal(isdefaultProp: boolean, lat = null, long = null, newMap = false) {
    this.propertyDetails = {} as any;
    this.propertyDetails.PropertyID = this.property.PropertyID;
    if (isdefaultProp) {
      this.propertyDetails.Latitude = this.property?.Location?.Latitude;
      this.propertyDetails.Longitude = this.property?.Location?.Longitude;
    }
    else {
      this.propertyDetails.Latitude = this.property?.Location?.Latitude;
      this.propertyDetails.Longitude = this.property?.Location?.Longitude;
      this.propertyDetails.Floors = this.property.Floors;
    }
    if (!!lat && !!long) {
      this.propertyDetails.Latitude = lat;
      this.propertyDetails.Longitude = long;
      this.propertyDetails.zoomLevel = 14;
    }
    if (!newMap) {
      this.propertyDetails.CondoTypeID = this.property.CondoTypeID;
      this.propertyDetails.CondoTypeName = this.property.CondoTypeID;
      this.propertyDetails.UseTypeID = this.property.UseTypeID;
      let commModel = new CommunicationModel();
      commModel.Key = 'NewPropertySelected';
      commModel.data = this.propertyDetails;
      this._communicationService.broadcast(commModel);
      this._addFloorService.enableFloorLables(true);
    }
    this.showMap = true;
  }

  onSaveBuildingSize(areaSM) {
    areaSM = areaSM?.toFixed(2);
    this.propertyForm.get('TypicalFloorSizeSM') && this.propertyForm.get('TypicalFloorSizeSM').markAsDirty();
    this.propertyForm.get('BuildingSF') && this.propertyForm.get('BuildingSF').markAsDirty();
    this.updateAddPolygonButtonDisable();
  }

  getValue(controlName, value) {
    const yesNoFields = [...YesNoFields, ...IndustrialYesNoFields];
    if (yesNoFields.includes(controlName)) {
      return value === 1 ? 'Yes' : value === 0 ? 'No' : value;
    } else {
      return value;
    }
  }

  onChangeSetAsDirty(control: string) {
    const data = this.propertyForm?.controls;
  
    // Traverse through the main form and handle nested forms
    Object.keys(data).forEach((key) => {
      const formControl = data[key];
      
      if (formControl instanceof FormGroup) {
        // If the control is a FormGroup, check its child controls
        const nestedControl = formControl?.get(control);
        if (nestedControl) {
          nestedControl?.markAsDirty();
        }
      } else if (key === control) {
        // Mark control directly if it's at the top level
        formControl.markAsDirty();
      }
    });
  }

  floorValidation(value) {
    if (value === 'Existing') {
      this.propertyForm.get('Floors').setValidators([Validators.required]);
    } else {
      this.propertyForm.get('Floors').clearValidators();
    }
    this.propertyForm.get('Floors').updateValueAndValidity();
  }
  changeTab($event) {
    if (!!$event.tab.textLabel) {
      if ($event.tab.textLabel === 'Listing') {
        this.isListing = true;
      }
      if ($event.tab.textLabel === 'Media') {
        this.isMedia = true;
      }
      if ($event.tab.textLabel === 'Notes') {
        this.isNotes = true;
      }
      this.isStrata = $event.tab.textLabel === 'Strata';
    }
  }

  showPropertyResearchStatus() {
    this.researchStatusClicked = true;
    this.getPropertyResearchStatus(this.property.PropertyID);
  }
  onMediaChange(path) {
    this.onComplete.emit(true);
  }

  showAllocationsAndUses() {
    this.allocationsAndUses = true;
    this.getPropertyResearchStatus(this.property.PropertyID);
  }

  viewChangeLog(type, parentid, fetchNewChangeLog, fetchOldChangeLog) {

    this.showChangeLog = true;
    this.changelogType = type;
    this.parentId = parentid;
    this.fetchNewChangeLog = fetchNewChangeLog;
    this.fetchOldChangeLog = fetchOldChangeLog;
  }

  clearAdditionalUses() {
    let commModel = new CommunicationModel();
    commModel.Key = 'clearAdditionalUses';
    commModel.data = true;
    this._communicationService.broadcast(commModel);
  }

  showPropertySummary(propertyID) {
    this.clearAdditionalUses();
    this.clearParkingRetailAndHardStandPolygons();
    this.fetchStrataProperty.emit(propertyID);
    const strataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds));
    this.visitedStrataIdsFromSS.push(propertyID);
    sessionStorage.setItem(SessionStorageKeys.VisitedStrataIds, JSON.stringify(this.visitedStrataIdsFromSS));
    if (strataIds && this.isNavigationFromSearch) {
      this.visitedPropertyIds.push(propertyID);
      this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    }
  }

  onPropertySpacesSave({ parkingSpaces, singleSlot, parkingArea }) {
    if (parkingSpaces) {
      this.property.ParkingSpaces = parkingSpaces;
      this.propertyForm.get('ParkingSpaces').markAsDirty();
      this.parkingPolygon = { singleSlotPolygon: singleSlot, parkingAreaPolygon: parkingArea, parkingSpaces: parkingSpaces };
    }
    this.showParkingSpace = false;
  }

  fetchAndProcessStagingMedia(propertyId: any) {
    this.stagingMedia.forEach(async (mediaObj) => {
      const { attachments, fileObject, media } = mediaObj.mediaInformation
      media.RelationID = propertyId;
      await this.IndexedDBService.saveMedia({
        mediaIdentifier: propertyId + '-' + fileObject.fileName,
        mediaInformation: {
          fileObject: fileObject,
          media: media,
          fileName: fileObject.fileName,
          attachments: attachments
        }
      });
    });
    this.stagingMedia.forEach((media) => {
      this.StagingIndexedDBService.removeMedia(media.mediaIdentifier, IndexedDBCollections.stagingImages);
    });
  }

  onCopyPolygonSave(polygon: MultiPolygon) {
    const id = uuidv4();
    polygon.localBuildingFootPrintID = id;
    let commModel = new CommunicationModel();
    commModel.Key = 'copyPolygon';
    commModel.data = polygon;
    this._communicationService.broadcast(commModel);
    this.showCopyPolygonModal = false;
    this.isFootprintModified = true;
    this.multifloors = JSON.parse(JSON.stringify([...this.multifloors, polygon]));
    this.updateBuildingSqmOnFloorChange();
    this.getAdditionalUses();
    if (this.isAnExistingProperty) {
      this.propertyAllocation?.updateAllocationsData(this.multifloors, this.property.PropertyID);
    }
  }

  addNewUnitsToMaster(data) {
    this.addNewChildUnitsToMaster.emit({
      property: {
        ...this.property,
        PropertyResearchTypeID: this.selectedOption
      }, ...data
    });
  }

  addNewFreeholdUnit(data) {
    let parcelObj: any = {};
    if (data.isFreehold && !data.isMultiStrata) {
      parcelObj.ParcelNumber = this.parcelNumber
    }
    this.addNewFreeholdUnitToMaster.emit({
      property: {
        ...this.property,
        PropertyResearchTypeID: this.selectedOption,
        ...parcelObj
      }, ...data
    });
  }

  onNext() {
    const auditStatus = this.auditStatusList.find(status => status.StatusName == "Verified");
    const reqBody = { PropertyID: this.property.PropertyID, PropertyAuditStatusID: auditStatus.StatusDefinationID}
    const response = this._propertyService.saveProperty(reqBody);
    response.subscribe(result => {
      if (!result.error) {
        sessionStorage.setItem(SessionStorageKeys.FetchProperties, 'true');
        this._notificationService.ShowSuccessMessage(CommonStrings.SuccessMessages.SaveAuditStatusSuccessMessage);
      } else {
        this._notificationService.ShowErrorMessage(result.message ?? CommonStrings.ErrorMessages.SaveAuditStatusFailMessage);
      }
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.SaveAuditStatusFailMessage);
    });
    this.onSave(true, this.property.PropertyID);
  }

  onParkingMapClicked() {
    this.latLong = {lat: this.property.Location.Latitude ? this.property.Location.Latitude : this.initialDetails.latLng.Latitude, lng: this.property?.Location?.Longitude ? this.property?.Location?.Longitude : this.initialDetails.latLng.Longitude}
    this.showParkingSpace = true;
  }

  onCopyPolygonClicked(polygon) {
    this.polygonToBeCopied = polygon;
    this.showCopyPolygonModal = true;
  }

  showStrataOrFreeHoldTab(Condo: PropertyDetailsDTO.CondoTypeIDEnum) {
    return (Condo === CondoTypeEnum.Strata || Condo === CondoTypeEnum.MasterStrataRecord ||
      Condo === CondoTypeEnum.ChildFreehold || Condo === CondoTypeEnum.MasterFreehold);
  }

  getStrataOrFreeholdTabText(Condo: PropertyDetailsDTO.CondoTypeIDEnum) {
    if (Condo === CondoTypeEnum.Strata || Condo === CondoTypeEnum.MasterStrataRecord) {
      return 'Strata';
    }
    if (Condo === CondoTypeEnum.ChildFreehold || Condo === CondoTypeEnum.MasterFreehold) {
      return 'Freehold';
    }
  }
  isFreeholdProp(Condo: PropertyDetailsDTO.CondoTypeIDEnum) {
    return Condo === CondoTypeEnum.ChildFreehold || Condo === CondoTypeEnum.MasterFreehold;
  }

  addBldgs() {
    this.addMultiFreeholdModal = true;
    let freeholdMin = undefined;
    const findLastValue = (arr: any[]): number => {
      if (!arr || arr.length === 0) {
        return 0;
      }

      let max = Number.MIN_VALUE;
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] && arr[i].CondoUnit) {
          const freeholdUnit = arr[i].CondoUnit ? Number(arr[i].CondoUnit) : 0;
          if (!isNaN(freeholdUnit) && freeholdUnit > max) {
            max = freeholdUnit;
          }
        }
      }
      return max;
    };

    const lastMax = findLastValue(this.strataAndFreeholdPids.linkedProperties);
    freeholdMin = `${lastMax + 1}`;
    this.childFreeholdMin = freeholdMin;
  }

  formatPropertyLocation(propertyLocation) {
    let propertyLocationCopyForChild = JSON.parse(JSON.stringify(propertyLocation));
    setPropertyName(propertyLocationCopyForChild, this.addressTypeValues, this.streetPrefixes, this.streetSufixes, this.quadrants);
    return propertyLocationCopyForChild;
  }

  saveMultiStrata(min, max, isFromFreeholdOrStrataTab = false) {
    const children = isFromFreeholdOrStrataTab ? this.initialDetails.masterStrataObj.strataList.filter(res => !res.IsMaster && res.StrataType == 'Strata') : (this.strataAndFreeholdPids && this.strataAndFreeholdPids.linkedProperties && this.strataAndFreeholdPids.linkedProperties.length > 0 ? this.strataAndFreeholdPids.linkedProperties.filter(res => res && !res.IsMaster&& res.StrataType == 'Strata') : []);
    this.property.HasNoBuildingFootprints = this.footPrintNotAvailable ? true : false;
    let propertyCopy = JSON.parse(JSON.stringify(this.property));
    if (!isFromFreeholdOrStrataTab) {
      propertyCopy.PropertyID = undefined;
      propertyCopy.MasterPropertyId = this.property.PropertyID;
      propertyCopy.CondoTypeID = this.property.CondoTypeID === CondoTypeEnum.MasterFreehold ? CondoTypeEnum.ChildFreehold : CondoTypeEnum.Strata;
    }
    this.footPrintNotAvailable = false;
    preparePropertyData({ propertyCopy, property: this.property, isAnExistingProperty: this.isAnExistingProperty, hasNoExistingParcelInTileLayer: this.hasNoExistingParcelInTileLayer,
      CountryId: this.CountryId, propertyService: this._propertyService, initialDetails: this.initialDetails, isMultiStrata: this.isMultiStrata, 
      addressTypeValues: this.addressTypeValues, streetPrefixes: this.streetPrefixes, streetSufixes: this.streetSufixes, quadrants: this.quadrants});
    const data: MultipleChildRequestDTO = {
      Property: { ...propertyCopy },
      Range: { Min: min, Max: max },
      Children: children.map(child => ({ CondoUnit: child.CondoUnit })),
    }

    const response_detailSave = this._propertyService.saveMultiChild(data)
    response_detailSave.subscribe(result => {
      if (!result.error) {
        if (isFromFreeholdOrStrataTab) {
          this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.PropertySaveSuccessfull);
        } else {
          this._notificationService.ShowSuccessMessage(CommonStrings.DialogConfigurations.Messages.ChildPropertyCreationMessage);
        }
        if (isFromFreeholdOrStrataTab) {
          Object.keys(this.propertyForm.controls).forEach(control => {
            if (this.propertyForm.controls[control].dirty) {
              this.propertyForm.controls[control].reset();
            }
          });
        }
        const response = result;
        if (!response.error) {
          response.responseData.forEach((row, index) => {
            if (data.Property.CondoTypeID === this.EnumCondoTypeNames.ChildFreehold) {
              let parcel: ParcelPropertyDTO = {};
              parcel.ParcelNo = data.Property.ParcelInfo;
              parcel.Lot = data.Property && data.Property.ParcelInfo && data.Property.ParcelInfo.split('//')[0];
              parcel.ParcelSize = data.Property.LotSizeSF ? data.Property.LotSizeSF : null;
              this.saveAndUpdateParcel(row.Ret_PropId, parcel);
            }
          })
        }
        if (isFromFreeholdOrStrataTab) {
          const propID = response.responseData[response.responseData.length - 1].Ret_PropId;
          this.completeChanges();
          this.fetchProperty.emit(propID);
          this.clearMultiStrataObj.emit(true)
        } else {
          let commModel = new CommunicationModel();
          commModel.Key = 'fetchFreeholdList';
          this._communicationService.broadcast(commModel);
          this.addMultiFreeholdModal = false;
        }
      } else {
        this._notificationService.ShowErrorMessage(result.message ?? CommonStrings.ErrorMessages.PropertySaveFail);
        if (!isFromFreeholdOrStrataTab) {
          this.addMultiFreeholdModal = false;
        }
      }
      this.redirectionLoader = false;
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.PropertySaveFail);
      if (!isFromFreeholdOrStrataTab) {
        this.addMultiFreeholdModal = false;
      }
      this.redirectionLoader = false;
    });
  }

  onSaveMultiFreeholdUnits(data: { min: number, max: number }) {
    this.saveMultiStrata(data.min, data.max);
  }

  onCancelMultiFreehold() {
    this.addMultiFreeholdModal = false;
  }

  selectBuildings() {
    let commModel = new CommunicationModel();
    commModel.Key = 'mapBuildingsToMaster';
    commModel.data = this.property.ParcelInfo;
    this._communicationService.broadcast(commModel);
  }

  closeParcelWindow(event: any) {
    this.completeChanges();
    this.onComplete.emit(false);
    this.showParcelWindow = false;
    this.selectedParcels = [];
  }

  addPropertyFromParcels(parcels) {
    this.selectedParcels = [...parcels];
    this.showParcelWindow = false;
    this.setMultiCondoTypeAndUnit();
    this.setMultiParcelAndLotSize();
  }

  setMultiParcelAndLotSize() {
    if (this.selectedParcels && this.selectedParcels.length > 0) {
      const size = this.selectedParcels?.reduce((acc, parcel) => acc + (parcel?.Lot_Area || parcel?.ParcelSize || 0), 0);
      this.aggregateParcelSize = size;
      this.parcelCount = this.selectedParcels?.length;
      this.property.LotSizeSF = parseFloat(parseFloat(size).toFixed(2));
      this.parcelNumber = this.selectedParcels[0].Parcel_No;
      if (!this.property?.LotSizeSourceID) {
        this.property.LotSizeSourceID = SizeSourceEnum.CountyDataSource;
      }
    }
  }

  setMultiCondoTypeAndUnit() {
    if (this.selectedParcels && this.selectedParcels.length > 0) {
      this.property.CondoTypeID = this.getCondoID(this.selectedParcels[0].Strata_Typ);
      this.onStrataChange();
      // Assign the condoUnit value only when the condo type is 'Strata'.
      if (this.selectedParcels[0].Strata_Typ === EnumCondoTypeNameFromTiles.Strata) {
        this.property.CondoUnit = this.selectedParcels[0].Lot;
      }
    }
  }

  getCondoID(condoType) {
    switch (condoType) {
      case EnumCondoTypeNameFromTiles.NotStrata:
        return CondoTypeEnum.NotStrata;
      case EnumCondoTypeNameFromTiles.Strata:
        return CondoTypeEnum.Strata;
      case EnumCondoTypeNameFromTiles.Master:
        return CondoTypeEnum.MasterStrataRecord
    }
  }

  addNewProperty(parcel) {
    this.showParcelWindow = false;
    this.selectedParcelInfo = parcel;
    this.setCondoTypeAndUnit();
    this.setParcelAndLotSize()
  }

  setCondoTypeAndUnit() {
    if (this.selectedParcelInfo) {
      this.property.CondoTypeID = this.getCondoID(this.selectedParcelInfo.Strata_Typ);
      this.onStrataChange();
      // Assign the condoUnit value only when the condo type is 'Strata'.
      if (this.selectedParcelInfo.Strata_Typ === EnumCondoTypeNameFromTiles.Strata) {
        this.property.CondoUnit = this.selectedParcelInfo.Lot;
      }
    }
  }

  setParcelAndLotSize() {
    this.property.LotSizeSF = this.selectedParcelInfo.Lot_Area.toFixed(2);
    this.parcelNumber = this.selectedParcelInfo.Parcel_No;
    // when a sigle parcel is selected set parcel count to 1
    this.aggregateParcelSize = this.property?.LotSizeSF;
    this.parcelCount = 1;
    // Set the default value of LotSizeSource to 'County Source Data'.
    if (!this.property.LotSizeSourceID) {
      this.property.LotSizeSourceID = SizeSourceEnum.CountyDataSource;
    }
  }

  //Save the parcel information associated with the property
  savePropertyParcelInformation(propertyId) {
    if (this.selectedParcels && this.selectedParcels.length > 0 && !this.isAnExistingProperty) {
      const parcelList = [];
      this.selectedParcels.forEach(parcel => {
        let propertyParcel: ParcelPropertyRequestDTO = {} as ParcelPropertyRequestDTO;
        const parcelSize = parcel.Lot_Area ? parcel.Lot_Area : parcel.ParcelSize;
        propertyParcel.ParcelNo = parcel.Parcel_No ? parcel.Parcel_No : parcel.ParcelNo;
        propertyParcel.Lot = parcel.Lot;
        propertyParcel.Block = parcel.Block || null;
        propertyParcel.SubDivision = parcel.SubDivision || null;
        propertyParcel.ParcelSF = parcelSize ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', parcelSize) : null;
        parcelList.push(propertyParcel);
      });
      const response = this.parcelService.saveParcel(propertyId, parcelList);
      response.subscribe((result: ApiResponseListParcelPropertyDTO) => {
        if(result.error) {
          this._notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message);
      });
    } else if (this.selectedParcelInfo || this.parcelNumber) {
      let parcel: ParcelPropertyDTO = {};
      const parcelSize = this.selectedParcelInfo && this.selectedParcelInfo.Lot_Area
      parcel.ParcelNo = this.selectedParcelInfo ? (this.property.CondoTypeID === CondoTypeEnum.ChildFreehold ? this.selectedParcelInfo.Parcel_No : this.selectedParcelInfo.Parcel_No) : this.parcelNumber;
      parcel.Lot = this.selectedParcelInfo ? this.selectedParcelInfo.Lot : null;
      parcel.ParcelSize = parcelSize ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', parcelSize) : this.property.LotSizeSF ? this._propertyService.convertUnit(this.CountryId, 'SqM', 'SF', this.property.LotSizeSF) : null;
      this.saveAndUpdateParcel(propertyId, parcel)
    }
  }

  saveAndUpdateParcel(propertyId, parcel, errorMsg = null) {
    let response_parcel;
    const reqBody: ParcelPropertyRequestDTO = {
      ParcelNo: parcel.ParcelNo,
      Lot: parcel.Lot,
      Block: parcel.Block,
      SubDivision: parcel.SubDivision,
      ParcelSF: parcel.ParcelSize
    }
    if (parcel.ParcelID) {
      response_parcel = this.parcelService.updateParcel(propertyId, parcel.ParcelID, reqBody);
    } else {
      const parcelList = [];
      parcelList.push(reqBody);
      response_parcel = this.parcelService.saveParcel(propertyId, parcelList);
    }
    response_parcel.subscribe((result: ApiResponseParcelPropertyDTO) => {
      if (result.error) {
        errorMsg && this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      errorMsg && this._notificationService.ShowErrorMessage(error?.error?.message ?? errorMsg);
    });
  }

  addPropertyImages = () => {
    let hasStreet = false;
    let hasAerial = false;
    this.stagingMedia.forEach(media => {
      if (media.mediaInformation.media.MediaName.includes('Streetview') || media.mediaInformation.media.MediaName.includes('Building') || media.mediaInformation.media.MediaTypeID === MediaDTO.MediaTypeIDEnum.BuildingImage) {
        hasStreet = true;
      }
      if (media.mediaInformation.media.MediaName.includes('Aerial') || media.mediaInformation.media.MediaTypeID === MediaDTO.MediaTypeIDEnum.AerialImagery) {
        hasAerial = true;
      }
    });
    const hasStreetViewImage = this.hasStreetView ? hasStreet : true;
    if ((!hasAerial && !hasStreetViewImage) || (hasStreetViewImage && !hasAerial)) {
      const bothImagesMissingMsg = CommonStrings.DialogConfigurations.Messages.AerialAndStreetViewImageMissing;
      const aerialImageMissingMsg = CommonStrings.DialogConfigurations.Messages.AerialViewImageMissing;
      const message = this.footPrintNotAvailable ? aerialImageMissingMsg : this.hasStreetView ? bothImagesMissingMsg : aerialImageMissingMsg;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => { this.openAerialView.emit(); } }
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    } else if (!hasStreet && hasAerial) {
      const message = CommonStrings.DialogConfigurations.Messages.AerialAndStreetViewImageMissing;
      const title = CommonStrings.DialogConfigurations.Title.Arealytics;
      const okButton = { Label: CommonStrings.DialogConfigurations.ButtonLabels.Ok, Callback: () => { this.openStreetViewClicked.emit(); } }
      const cancelButton = { Visible: false };
      this.showDialogConfirmation(message, title, okButton, cancelButton);
    }
  }

  hasbothStreetAndAerialImages = async () => {
    const stagingMedia = await this.StagingIndexedDBService.fetchAllStagingMedia()
    this.stagingMedia = stagingMedia;
    let hasStreet = false;
    let hasAerial = false;
    stagingMedia.forEach(media => {
      if (media.mediaInformation.media.MediaName.includes('Streetview') || media.mediaInformation.media.MediaName.includes('Building') || media.mediaInformation.media.MediaTypeID === MediaDTO.MediaTypeIDEnum.BuildingImage) {
        hasStreet = true;
      }
      if (media.mediaInformation.media.MediaName.includes('Aerial') || media.mediaInformation.media.MediaTypeID === MediaDTO.MediaTypeIDEnum.AerialImagery) {
        hasAerial = true;
      }
    });
    const hasStreetViewImage = this.hasStreetView ? hasStreet : true;
    const masterCondoTypes = ([CondoTypeEnum.MasterFreehold, CondoTypeEnum.MasterStrataRecord] as PropertyDetailsDTO.CondoTypeIDEnum[]);
    return masterCondoTypes.includes(this.property.CondoTypeID) ? true : this.footPrintNotAvailable ? hasAerial : (hasAerial && hasStreetViewImage);

  }

  checkMasterSizes() {
    return this.property.CondoTypeID === CondoTypeEnum.MasterFreehold || this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord ||  this.property.UseTypeID === this.propertyTypeValues.Land || this.footPrintNotAvailable ? true : (!!this.property.Floors && !!this.property.BuildingSF);
  }

  checkForBuildingSF() {
    return this.property.CondoTypeID === CondoTypeEnum.MasterFreehold || this.property.CondoTypeID === CondoTypeEnum.MasterStrataRecord ||  this.property.UseTypeID === this.propertyTypeValues.Land || this.footPrintNotAvailable ? true : this.propertyForm.controls.BuildingSF.valid;
  }

  getCondoType() {
    const condoTypeID = this.property.CondoTypeID;
    return getCondoType(condoTypeID);
  }

  showGresbScore() {
    const useTypesToShowGresbScore = [UseTypes.Office, UseTypes.Industrial, UseTypes.Retail];
    return useTypesToShowGresbScore.includes(this.selectedUseTypeID);
  }

  onReviewed () {
    if (this.property.IsReviewed) {
      this.property.IsReviewed = false;
    } else {
      this.property.IsReviewed = true;
    }
  }

  togglePropertyDetails(): void {
    setTimeout(()=>{
      this.isPropertyDetailsExpanded = !this.isPropertyDetailsExpanded;
      sessionStorage.setItem(SessionStorageKeys.PropertyDetailsTabStatus, JSON.stringify(this.isPropertyDetailsExpanded));
    },200);
  }

  footPrintNotAvailablePopup() {
    this.showContributedFieldsPopup = true;
  }

  closeManualfootprintAddPopup() {
    this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBA_SF');
    this.propertyForm.get('ContributedGBA_SF') && this.propertyForm.removeControl('ContributedGBASource');
    this.showContributedFieldsPopup = false;
  }

  mapFormToProperty(form) {
    let formValue = {};
    Object.keys(form.value).forEach(key => {
      formValue[BindFormControlToPropertyVariable[key]] = form.value[key];
    })
    return formValue;
  }

  saveManualFootprintInfo(form) {
    const controlKeys = Object.keys(form.controls)
    this.footPrintNotAvailable = true;
    this.propertyForm.addControl('ContributedGBA_SF', new FormControl(null, []));
    this.propertyForm.addControl('ContributedGBASource', new FormControl(null, []));
    this.showContributedFieldsPopup = false;
    const formValue = this.mapFormToProperty(form);
    this.property = {...this.property, ...formValue};
    controlKeys.map(key => {
      const control = form.controls[key]
      if (control.dirty) {
        this.updateControlInPropertyForm(key, control);
      }
    })
  }
  updateControlInPropertyForm(key, control) {
    if (this.propertyForm.get(key)) {
      this.propertyForm.get(key).setValue(control.value);
      this.propertyForm.get(key).markAsDirty();
    } 
    ['OfficeForm', 'IndustrialForm', 'RetailForm', 'LandForm'].forEach(groupName => {
      const nestedControl = this.propertyForm.get(`${groupName}.${key}`);
      if (nestedControl) {
        nestedControl.setValue(control.value);
        nestedControl.markAsDirty();
      }
    });
  }

  getProperty() {
    const dummyProperty = JSON.parse(JSON.stringify(this.property));
    return dummyProperty
  }

  isFootprintNotAvailableDisabled() {
    return this.multifloors.some(floor => floor.floorSize);
  }
  getFormGroupName() {
    return getFormNameByUseType(this.property.UseTypeID);
  }

  showDialogConfirmation(message: string, title: string, okButton: dialogConfirmSettings, cancelButton: dialogConfirmSettings, additionalBtn?: dialogConfirmSettings) {
    let dialogConfiguration: confirmConfiguration = new confirmConfiguration();
    const { Label: oklabel, Callback: okCallback } = okButton;
    const { Visible: showCancelBtn, Label: cancelLabel, Callback: cancelCallback, Classname: cancelBtnClass } = cancelButton;
    const { Visible: showAdditionalBtn, Label: additionalBtnLabel, Classname: additionalBtnClassname, Callback: additionalBtnCallback} = additionalBtn || {};
    dialogConfiguration.Message = message;
    dialogConfiguration.Title = title;
    dialogConfiguration.OkButton.Label = oklabel;
    dialogConfiguration.OkButton.Callback = () => {
      okCallback && okCallback();
    };
    dialogConfiguration.CancelButton.Label = cancelLabel ??  dialogConfiguration.CancelButton.Label;
    dialogConfiguration.CancelButton.Callback = () => {
      cancelCallback && cancelCallback();
    };
    dialogConfiguration.CancelButton.Classname = cancelBtnClass ?? dialogConfiguration.CancelButton.Classname;
    dialogConfiguration.CancelButton.Visible = showCancelBtn;
   if (additionalBtn) {
      dialogConfiguration.AdditionalButton = new confirmSettings();
      dialogConfiguration.AdditionalButton.Visible = showAdditionalBtn;
      dialogConfiguration.AdditionalButton.Label = additionalBtnLabel;
      dialogConfiguration.AdditionalButton.Classname = additionalBtnClassname;
      dialogConfiguration.AdditionalButton.Callback = () => {
        additionalBtnCallback && additionalBtnCallback();
      };
    }
    this._notificationService.CustomDialog(dialogConfiguration);
  }

  isMasterOrNotStrata(): boolean {
    return  (!this.property.CondoTypeID ||
    ([CondoTypeEnum.MasterFreehold, CondoTypeEnum.MasterStrataRecord, CondoTypeEnum.NotStrata] as PropertyDetailsDTO.CondoTypeIDEnum[]).includes(this.property.CondoTypeID)||
    this.property.UseTypeID === this.propertyTypeValues.Land);
  }

  getContactMedia() {
    const contact: ContactMedia = {
      RelationID: this.property.PropertyID,
      RelationshipTypeID: MediaDTO.MediaRelationTypeIDEnum.Property,
      PropertyID: this.property.PropertyID
    }
    return contact;
  }
}
