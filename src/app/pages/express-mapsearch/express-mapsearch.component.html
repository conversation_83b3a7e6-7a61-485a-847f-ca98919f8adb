<!-- <div class="container-fluid header-wrapper">
    <div class="row">
        <div class="login-log navbar-brand">
        </div>
        <div class="col-md-10">
            <div class="float-right admin-area">
                <div class="user-name-admin">Welcome <span>{{UserName}}</span></div>
                <a (click)="logOut()" class="btn btn-warn px-4 login-btn">Logout</a>
            </div>
        </div>
    </div>
</div> -->

<div class="main-container">
    <app-header [hideModeLabel]="!showAdvancedSearchLabel" [showAdvancedSearchLabel]="showAdvancedSearchLabel"></app-header>
</div>

<div >
    <div id="fullScreenMapContainer" class="mapcontainer" [ngClass]="{'shorten-map': showAddEditPropertyForm, 'full-map': !showAddEditPropertyForm}">
        <div id="screen" #screen class="canvas-container" [style.zIndex]="selectionEnabled ? '2147483647' : '-1'" [style.display]="selectionEnabled ? 'inline' : 'none'">
            <button class="clear-canvas" (click)="onClearCanvas()"> <i class="fa fa-remove"></i></button>
            <canvas #streetViewCanvas>
            </canvas>
        </div>
        <div>
            <div #captureDiv>
                <div id="map-canvas-express" [style.display]="showStreetView ? '' : 'block'" [style.visibility]="showStreetView ? 'hidden' : 'visible'" [ngClass]="{'mapCanvasClass': selectionEnabled}" [style.height]="showStreetView ? '0vh' : mapHeight" [style.width]=" isExpressMapInFullScreenMode ? '100vw' : ''"></div>
                <div id="map-express" [style.display]="showStreetView ? 'block' : ''" [style.visibility]="showStreetView ? 'visible' : 'hidden'" [ngClass]="{'mapCanvasClass': selectionEnabled}" [style.height]="showStreetView ? mapHeight : '0vh'" [style.width]=" isExpressMapInFullScreenMode ? '100vw' : ''"></div>
                <div id="layerInfoCardInExpressMapSearch" class="info-card-pos"></div>
            </div>
            <input type="text" id="searchbox" data-testId="search-location" class="form-control" [style.display]="showStreetView ? 'none' : 'block'" [value]="address">
            <div class="pinColorWrap" id="legend" [ngClass]="{'hidesearchCodeBox': showStreetView}">      
                <div class="pinBox" *ngFor="let item of ZoningColorData">
                    <span><svg width="19" height="19">
                        <circle cx="9" cy="9" r="9" stroke-width="4" [attr.fill]="item.fill" />
                        </svg></span><span class="label">{{item.zone}}</span>
                    </div>          
            </div>
            <div id="layersdropdown" class="layers-dropdown" [ngClass]="{'hidesearchCodeBox': showStreetView}">
                <app-multi-select-dropdown [list]="list" [checkedList]="checkedList"
                    (shareCheckedList)="shareCheckedList($event)" [isEditPropertyOpen]="false">
                </app-multi-select-dropdown>
            </div>
            <input type="text" id="idsearchbox" [style.display]="showStreetView ? 'none' : 'block'" placeholder="Property ID"
                [(ngModel)]="propertyID" (keyup.enter)="onIDChange($event.target.value)" class="form-control search-box" data-testid="map-search-propertyId">
             <!-- Full-screen-expand-icon -->
            <div class="btn-fs" id="fullScreenExpand" (click)="expressMapFullScreenModeHandler()"
                [ngStyle]="{'display': isMapSearch ? (showAddEditPropertyForm ? 'inline' : 'none') : (isExpressMapInFullScreenMode || showStreetView ? 'none' : 'inline')}">
                <i class="fas fa-expand fs-icon-pos"></i>
            </div>
            <!-- Azure Map buuton -->
           <app-map-switch id="map-switch" [checkedList]="checkedList" [deckOverlay]="deckOverlay" [map]="map"
              *ngIf="map"></app-map-switch>

            <!-- Full-screen-compress-icon -->
            <div id="fullScreenCompress" class="btn-fs" (click)="expressMapExitFullScreenModeHandler()"
                [ngStyle]="{'display': isMapSearch ? (showAddEditPropertyForm ? 'none' : 'inline') : (isExpressMapInFullScreenMode && !isExpressMapStreetViewVisible ? 'inline' : 'none')}">
                <i class="fas fa-compress fs-icon-pos" aria-hidden="true"></i>
            </div>
             <!-- Camera-Icon -->
            <div id="streetViewCamViewCam" title="Select map" (click)="expressMapCamClickHandler($event)" class="cam-icon-pos"
                [ngStyle]="{'display': isExpressMapInFullScreenMode && isStreetView && mapEditProperty.propertyId ? 'inline' : 'none'}">
                <i class="fa fa-camera icon-pos"></i>
            </div>
            <!-- Capture-and-close-button -->
            <div id="saveAndClose" class="save-and-close-btn fs-buttons-margin"
                [ngStyle]="{'display': isExpressMapInFullScreenMode && isStreetView ? 'flex' : 'none'}"
                (click)="expressMapExitFullScreenModeHandler()">Confirm & Exit</div>
            <!-- street view -->
            <div class="btn-fs screen-mode-change-icons-pos street-view-btns-bg" (click)="expressMapFullScreenModeHandler()"
                [ngStyle]="{'display': (!isExpressMapInFullScreenMode && isExpressMapStreetViewVisible && !isBuildingModalInFullScreenMode)  ? 'inline' : 'none'}">
                <i class="fas fa-expand fs-icon-pos fs-icon-pos-sv"></i>
            </div>
            <!-- Full-screen-compress-icon -->
            <div class="btn-fs screen-mode-change-icons-pos street-view-btns-bg" (click)="expressMapExitFullScreenModeHandler()"
                [ngStyle]="{'display': (isExpressMapInFullScreenMode && isExpressMapStreetViewVisible)  ? 'inline' : 'none'}">
                <i class="fas fa-compress fs-icon-pos fs-icon-pos-sv" aria-hidden="true"></i>
            </div>
               <!-- Camera-Icon -->
            <div title="Select map" (click)="expressMapCamClickHandler($event)" class="cam-icons-pos"
                [ngStyle]="{'display': (isExpressMapInFullScreenMode && isExpressMapStreetViewVisible  && isStreetView ) ? 'inline' : 'none'}">
                <i class="fa fa-camera icon-pos"></i>
            </div>
            <!-- Save-and-close-button -->
            <div class="save-and-close-btn save-btn-pos"   [ngStyle]="{'display': (isExpressMapInFullScreenMode && isExpressMapStreetViewVisible && isStreetView) ? 'flex' : 'none'}" (click)="expressMapExitFullScreenModeHandler()" >Confirm & Exit</div>
            <div id="searchCodeBox" [style.display]="showStreetView ? 'none' : 'block'"
                class="postal-code-serachbox">
                <div class="position-relative">
                    <input type="text" placeholder="Postal Code" [(ngModel)]="searchText" style="width: 130px;"
                        (keyup.enter)="onIDChange(null, $event.target.value,'zipcode')" data-testId="search-postal-code"
                        class="form-control idsearchboxCode webView">
                    <i class="fa fa-spinner fa-spin zipcodeSpin" *ngIf="SpinLoader"></i>
                </div>
            </div>
        </div>

        
        <div [style.visibility]="isStreetView ? 'visible' : 'hidden'" [style.height]="isStreetView ? '' : '0vh'" id="utility">
            <app-building-size-utility-modal [propertyDetails]="propertyDetails" (onSave)="onSaveBuildingSize($event)"
                (onClose)="closeMapModal()" (showDeleteShapeModal)="showDeleteBSModal($event)"
                (buildingModalCameraClick)="buildingModalCamClickHandler($event)"
                (buildingModalFullScreenMode)="buildingModalFullScreenModeHandler()"
                (buildingModalExitFullScreenMode)="buildingModalExitFullScreenModeHandler()"
                [isBuildingModalInFullScreenMode]="isBuildingModalInFullScreenMode" [selectedCamera]="selectedCamera"
                [selectionEnabled]="selectionEnabled" [isStreetView]="isStreetView"
                [isExpressMapInFullScreenMode]="isExpressMapInFullScreenMode"
                [isBuildingModalStreetViewVisible]=" isBuildingModalStreetViewVisible"
                (updateBuildingModalStreetView)="updateBuildingModalStreetView($event)" (updateNewLatLng)="updateNewLatLng($event)"
                [masterStrataObj]="masterStrataObj" (saveOldFootprint)="saveOldPolygon($event)"
                [condoType]="selectedPropertyCondoType" #buildingModalComponent></app-building-size-utility-modal>
        </div>
        <div *ngIf="showFileUploadModal">
            <imperium-modal [(visible)]="showFileUploadModal" [title]="mediaTitle" [bodyTemplate]="bodyTemplate" [width]="'xl-medium'" [height]="mediaHeight" [closeOnClickOutside]="false">
              <ng-template #bodyTemplate>
                <image-upload-modal
                    [initialDetails]="initialDetails"
                    [media]="media"
                    [uploadFiles]="ArrayFiles"
                    [IsProperty]="true"
                    [IsBranchAdd]="false"
                    [IsCompanyAdd]="false"
                    [MinifiedMedia]="false"
                    [HideDefault]="false"
                    (onSave)="onUploadEvent($event)"
                    (onClose)="closeMediaUploadModal()"
                    (onRetake)="mediaRetakeHandler()"
                ></image-upload-modal>
              </ng-template>
            </imperium-modal>
        </div>
    </div>
<div class="form-container" [ngClass]="{'shorten-form': showAddEditPropertyForm, 'hide-form': !showAddEditPropertyForm}">
    <div>
        <app-editproperty [initialDetails]="mapEditProperty" (onComplete)="onComplete($event)" [userLat]="userLat"
            [userLong]="userLong" [isStreetView]="isStreetView"
            (fetchProperty)="fetchPropertyById($event)"
            (fetchStrataProperty)="fetchStrataProperty($event)" (clearMultiStrataObj)="clearMultiStrataObj($event)"
            [isAnExistingProperty]="isAnExistingProperty" (fetchNextProperty)="fetchNextProperty($event)"
            [tabSelected]="tabSelected" [editedFootprintID]="editedFootprintID" [newPropertyLocation]="newPropertyLocation"
            (setSelectedPropertyCondoType)="setSelectedPropertyCondoType($event)"
            (openStreetViewClicked)="expressMapFullScreenModeHandler()" (openAerialView)="openAerialView()"
            [oldPolygon]="oldPolygon" (addNewChildUnitsToMaster)="addNewChildUnitsToMaster($event)"
            (addNewFreeholdUnitToMaster)="addNewFreeholdUnitToMaster($event)" [hasStreetView]="showStreetView"
            [isParcelLayerEnabled]="isParcelLayerEnabled()"
            #editPropertyForm></app-editproperty>
    </div>
</div>
