// Angular Core & Common
import { Component, OnInit, ElementRef, ViewChild, NgZone } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
// RxJS
import { Subscription } from 'rxjs';
// Angular Environment
import { environment } from '../../../environments/environment';
// Third-Party Libraries
import { GoogleMapsOverlay } from '@deck.gl/google-maps';
import html2canvas from 'html2canvas-pro';
import { CryptoJS } from 'crypto-js';
import { isNumber } from 'util';
// Enums
import { CameraType } from '../../enumerations/cameraType';
import { EditPropertyTabs } from '../../enumerations/editPropertyTabs';
import { IndexedDBCollections, MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { Overlays, ZoningColorData } from '../../enumerations/overlays';
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { UseTypes } from '../../enumerations/useTypes';
// Models
import { ContactMedia } from '../../models/ContactMedia';
import { PropertyDetails } from '../../models/PropertyDetails';
// DTOs
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
// Map Module Models
import { LatLng } from '../../modules/map-module/models/LatLng';
import { MapBound } from '../../modules/map-module/models/mapBound';
import { MapOptions } from '../../modules/map-module/models/MapOptions';
import * as MapEnum from '../../modules/map-module/models/MapEnum';
import { PolygonStyleOption } from '../../modules/map-module/models/PolygonSyleOption';
import { StreetViewParams } from '../../modules/map-module/models/streetViewParams';
// Components
import { BuildingSizeUtilityModalComponent } from '../../modules/modal-module/modals/building-size-utility-modal/building-size-utility-modal.component';
import { EditpropertyComponent } from '../editproperty/editproperty.component';
// Services
import { BuildingFootPrintService } from '../../services/building-footprint.service';
import { CommunicationModel, CommunicationService } from '../../services/communication.service';
import { ImageUploadWorkerService } from '../../services/image-upload-worker.service';
import { IndexedDBService } from '../../services/indexeddb.service';
import { LoginService } from '../../services/login.service';
import { MapHelperService } from '../../services/map-helper.service';
import { MapService } from '../../modules/map-module/service/map-service.service';
import { MediaService } from '../../services/media.service';
import { MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { PropertyService } from '../../services/api-property.service';
import { SharedDataService } from '../../services/shareddata.service';
import { StagingIndexedDBService } from '../../services/indexeddb-staging.service';
// Constants & Utilities
import { CommonStrings, DefaultMapSearchPageSize, ZoomLevel18, ZoomLevel20 } from '../../constants';
import { clearSessionStorage } from '../../utils';
// API Clients
import { ApiResponseListPropertyMapSearchSizeResponseDTO, ApiResponsePropertyDetailsSizeResponseDTO, ApiResponseVoid, PropertyDetailsDTO,
  PropertyMapSearchRequestDTO, PropertyMapSearchSizeResponseDTO, MediaDTO } from '../../../app/api-client';
import { MediaInfoDTO } from '../../models/media-dto';

declare var google: any;
var CryptoJS = require("crypto-js");
const splitViewMapHeight = 'calc(60vh - 100px)';
const fullScreenMapHeight ='calc(100vh - 50px)';
@Component({
  selector: 'app-expressmapsearch',
  templateUrl: './express-mapsearch.component.html',
  styleUrls: ['./express-mapsearch.component.css']
})
export class ExpressMapsearchComponent implements OnInit {
  @ViewChild('streetViewCanvas') streetViewCanvas: ElementRef;
  @ViewChild('captureDiv') captureDiv: ElementRef;
  @ViewChild('editPropertyForm') editPropertyForm: EditpropertyComponent;
  ZoningColorData=ZoningColorData;
  canvas:ElementRef;
  divToCapture:ElementRef;
  ctx: CanvasRenderingContext2D;
  canvasx: number=0;
   canvasy: number=0;
   canvasWidth: number;
  canvasHeight: number;
   startMouseX: number=0;
   startMouseY: number=0;
   startMouseXToCapture: number=0;
   startMouseYToCapture: number=0;
   mouseX: number=0;
   mouseY: number=0;
   isMouseDown: boolean = false;
   selectionEnabled:boolean=false;
   selectedCamera:string;
   enableStreetViewSelection: boolean = false;
   capturedImage: string | null = null;
   selectionRect: { startX: number, startY: number, endX: number, endY: number } | null = null;
   canvasOffset :number;
   offsetX :number;
   offsetY:number;
   showFileUploadModal: boolean = false;
   mediaTitle = '';
   mediaHeight = '';
   media: MediaInfoDTO;
   files: MediaInfoDTO[] = [];
   ArrayFiles: MediaInfoDTO[] = [];
   mapCanvasClass: string;
   initialDetails: ContactMedia;
   isStreetView:boolean;
   capturingAerialView: boolean;
   isMediaListUpdated: any = false;
   IsLoader = false;
   galleryImages: MediaInfoDTO[] = [];
   public property: PropertyDetails = new PropertyDetails();
   dateFormat: string;
  private _mapService: MapService;
  private _mapHelperService: MapHelperService;
  private _communicationService: CommunicationService;
  private _propertyService: PropertyService;
  private _notificationService: NotificationService;
  deckOverlay: any;
  list : any[]= [
    {checked :false,id:Overlays.Zoning},
    {checked :true,id:Overlays.Parcel},
    {checked :false,id:Overlays.Building},
    {checked :false,id:Overlays.PostalCode}
  ];
  checkedList = [Overlays.Parcel];

  showDeletePolygonModal = false;
  buildingFootPrintIdToBeDeleted: any;
  public mapOptions: MapOptions;
  public streetviewOptions: StreetViewParams;
  public map: any;
  public searchText: string = null;
  public showAddEditPropertyForm: boolean;
  public mapEditProperty: mapEditPropertyDTO;
  public UserName: string;

  private neutralPin: any;
  private clickLocationPin: any;
  private infoWindows: Array<any>;
  private propertyMarkers: Array<any>;
  private selectedMarker: any;
  private isMarkerAnimationSet: boolean;
  private polylines: any;
  private markerCluster: any;

  private properties: any;
  saveEventSubscription: Subscription;
  isclusterclick: boolean = false;
  UnitId: any;
  metricUnit: any;
  UnitDisplayTextSize: string;
  ourOverlay = null;
  previousHoverMarker: any = null;
  previousMarker: any = null;
  isMarkerClicked = false;
  selectedProperty: any = null;
  propertyResearchStatusListner: Subscription;
  SpinLoader = false;
  public propertyID: any;
  panorama: any;
  visible = false;
  userLat = null;
  userLong = null;
  oldPolygon = {};
  editedFootprintID = undefined;
  mapHeight: string = fullScreenMapHeight;
  selectedPosition = undefined;
  isAnExistingProperty = undefined;
  propertyDetails: PropertyDetails = new PropertyDetails();
  marker_pano;
  showStreetView = false;
  tabSelected: number;
  selectedPropertyCondoType: PropertyDetailsDTO.CondoTypeIDEnum;
  fromMasterStrata = false;
  masterStrataObj: any;
  @ViewChild('buildingModalComponent', {static: false}) buildingModalComponent: BuildingSizeUtilityModalComponent;
  isBuildingModalInFullScreenMode: boolean = false;
  isExpressMapInFullScreenMode: boolean = false;
  isExpressMapStreetViewVisible: boolean = false;
  isBuildingModalStreetViewVisible: boolean = false;
  newPropertyLocation: LatLng;
  selecetdPositionLatLng: any;
  selectedPropertyIdfromSearch: number;
  private _zone: NgZone;
  ImageUploadWorkerService: ImageUploadWorkerService;
  IndexedDBService:IndexedDBService;
  StagingIndexedDBService: StagingIndexedDBService;
  showAdvancedSearchLabel: any;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  parcelLayerListener: Subscription;
  address: string = null;
  latitude: string = null;
  longitude: string = null;
  isMapSearch = false;
  cityName: string;
  sourceAddress: string;
  searchLocation: string = '';
  constructor(mapService: MapService, mapHelperService: MapHelperService,
    communicationService: CommunicationService
    , propertyService: PropertyService
    , private _loginService: LoginService
    , private router: Router
    , notificationService: NotificationService,
    private sharedDataService: SharedDataService,
    private buildingFootprintService: BuildingFootPrintService,
    private mediaService: MediaService,
  ) {
    this._mapService = mapService;
    this._mapHelperService = mapHelperService;
    this._communicationService = communicationService;
    this._propertyService = propertyService;
    this._notificationService = notificationService;
    this.parcelLayerListener = this._communicationService.subscribe('EnableParcel').subscribe(result => {
      if (!this.checkedList.includes(Overlays.Parcel)) {
        this.checkedList.push(Overlays.Parcel);
        const parcelLayer = this.list.find(overlay => overlay.id === Overlays.Parcel);
        if (parcelLayer) {
          parcelLayer.checked = true;
        }
        this._mapService.addOverlay(this.checkedList, this.map, this.deckOverlay, 'layerInfoCardInExpressMapSearch');
      }
    });
    this.propertyResearchStatusListner = this._communicationService.subscribe('PropertyResearchTypeChanged').subscribe(result => {
      this.loadPropertyPins(this.sharedDataService.mapSearchPropertyList, !this.selectedPosition);
    });
    var loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);

    if (loginData != "" && !!loginData) {
      var bytes = CryptoJS.AES.decrypt(loginData.toString(), environment.EncryptionKey);
      var loggedinData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));

      if (loggedinData) {
        // this._loginService.UserInfo.IsLoggedin = true;
        this._loginService.UserInfo.CountryID = loggedinData.CountryId;
        this._loginService.UserInfo.EntityID = loggedinData.EntityID;
        this._loginService.UserInfo.PersonName = loggedinData.PersonName;
        this._loginService.UserInfo.MetroCentroidLat = loggedinData.MetroCentroidLat;
        this._loginService.UserInfo.MetroCentroidLong = loggedinData.MetroCentroidLong;
        this._loginService.UserInfo.UnitDisplayTextSize = loggedinData.UnitDisplayTextSize;
        this._loginService.UserInfo.RoleID = loggedinData.RoleID;
        this._loginService.UserInfo.RoleName = loggedinData.RoleName;

        this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
        this.UserName = loggedinData.PersonName;
        this.userLat = !!this._loginService.UserInfo ? this._loginService.UserInfo.MetroCentroidLat : null;
        this.userLong = !!this._loginService.UserInfo ? this._loginService.UserInfo.MetroCentroidLong : null;
      } else {
        this.router.navigate(['/login']);
      }
    } else {
      this.router.navigate(['/login']);
    }
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.handleNavigation(event.url);
      }
    });
  }
  ngOnDestroy(): void {
    this.propertyResearchStatusListner.unsubscribe();
    this.parcelLayerListener.unsubscribe();
  }
  logOut() {
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }
  ngOnInit() {
    this.showAdvancedSearchLabel = !sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch);
    const propertyIdFromStorage = sessionStorage.getItem(SessionStorageKeys.PropertyId);
    const storedLocJsonString = sessionStorage.getItem(SessionStorageKeys.SelectedPropertyPos);
    if (storedLocJsonString) {
      const storedLocationData = JSON.parse(storedLocJsonString);
      this.selecetdPositionLatLng = storedLocationData;
    }
    if (propertyIdFromStorage) {
      this.selectedPropertyIdfromSearch = JSON.parse(propertyIdFromStorage);
    }
     this.selectedPosition = this.selecetdPositionLatLng;
    const instance = this;
    this.mapEditProperty = new mapEditPropertyDTO();
    this.IndexedDBService = new IndexedDBService();
    this.StagingIndexedDBService = new StagingIndexedDBService();
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.ImageUploadWorkerService = new ImageUploadWorkerService();
    this.showAddEditPropertyForm = true;
    this.isMarkerAnimationSet = false;
    document.addEventListener('fullscreenchange',function(){
      if (!document['fullscreenElement']) {
        if(instance.isBuildingModalInFullScreenMode){
          instance.buildingModalComponent.hideTooltip();
          instance.buildingModalComponent.addMapOverlay();
        }
        instance.isBuildingModalInFullScreenMode=false;
        if (instance.buildingModalComponent) {
          instance.buildingModalComponent.clearMarkersData();
        }
        instance.selectionEnabled=false;
        instance.isExpressMapInFullScreenMode=false;
        if(instance.isStreetView)
         instance.mapHeight = splitViewMapHeight;
        else
         instance.mapHeight = fullScreenMapHeight;
    }
    });
    document.addEventListener("keydown", function(event) {
      const key = event.key; // Or const {key} = event; in ES6+
      if (key === "Escape") {
        if( instance.selectionEnabled === true){
          instance.selectionEnabled = false;
        }
        instance.marker_pano && instance.marker_pano.setVisible(true);
        if (instance.clickLocationPin) {
          instance.clickLocationPin.map = instance.map;
        }
      }
    });
    const isFromMapSearch = sessionStorage.getItem(SessionStorageKeys.IsFromMapSearch)
    this.showAddEditPropertyForm = isFromMapSearch !== 'true';
    this.isMapSearch = isFromMapSearch == 'true'
  }

  fetchLastVisitedStrataProperty(propertyID) {
    this.exitFullscreen();
    setTimeout(() => {
      this.showStreetView = true;
      this.mapHeight = 'calc(60vh - 100px)';
      const lastVisitedStrataProperty = JSON.parse(sessionStorage.getItem(SessionStorageKeys.LastVisitedStrataProperty));
      if (lastVisitedStrataProperty) {
        this.propertyDetails.Location.Latitude = lastVisitedStrataProperty.Latitude;
        this.propertyDetails.Location.Longitude = lastVisitedStrataProperty.Longitude;
      }
      this.isStreetView = true;
      const response = this._propertyService.getPropertyById(propertyID);
      response.subscribe((result: ApiResponsePropertyDetailsSizeResponseDTO) => {
        if (!result.error) {
          const property = result.responseData;
          let latLngObj = new LatLng();
          latLngObj.Latitude = property.Location.Latitude;
          latLngObj.Longitude = property.Location.Longitude;
          this.propertyDetails.Location.Latitude = property.Location.Latitude;
          this.propertyDetails.Location.Longitude = property.Location.Longitude;
          this.selectedPosition = { Latitude: property.Location.Latitude, Longitude: property.Location.Longitude };
          this.StreetView(latLngObj, "map-express", property);
          this.mapEditProperty = new mapEditPropertyDTO();
          this.openEditProperty(property, propertyID);
          this.isNewProperty();
          this.isMarkerClicked = true;
        }
      })
    }, 100);
    
  }

  ngAfterViewInit() {
    setTimeout(() => {
      let instance=this;
      instance.initMap();
      // If the browser is online again, restart the pending media upload.
      // Ensure that the media upload process resumes when the network connection is restored.
      window.addEventListener('online', () => {
        this.IndexedDBService.restartPendingMediaUpload();
      })
       google.maps.event.addListener(this.map.getStreetView(), 'visible_changed', function() {
       instance.updateStreetViewStatus();
     });
    const visitedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)) || [];
    const lastVisitedPropertyId = visitedStrataIds.pop();
    if(lastVisitedPropertyId) {
      this.fetchLastVisitedStrataProperty(lastVisitedPropertyId);
    }else  if (this.selecetdPositionLatLng && this.selectedPropertyIdfromSearch) {
      this.fetchPropertyByIdFromSearch(this.selectedPropertyIdfromSearch, this.selecetdPositionLatLng);
    }
    // Restart pending media upload onReload
    this.IndexedDBService.restartPendingMediaUpload();
    }, 100);

    const isCreateProperty = sessionStorage.getItem(SessionStorageKeys.IsCreateProperty);
    setTimeout(async () => {
      const isNavigatedFromERC = sessionStorage.getItem(SessionStorageKeys.IsNavigatedFromERC);
      if(isNavigatedFromERC && isCreateProperty){
        this.address = sessionStorage.getItem(SessionStorageKeys.Address);
        this.latitude = sessionStorage.getItem(SessionStorageKeys.Latitude);
        this.longitude = sessionStorage.getItem(SessionStorageKeys.Longitude);

        if (!this.address) {
            this.address = await this.getAddressFromLatitudeLongitude();
        }
        this._mapService.SetIDCenter(this.map, Number(this.latitude), Number(this.longitude));
    }
    }, 150);

    const isNavigatedFromLIT = sessionStorage.getItem(SessionStorageKeys.isNavigatedFromLIT);
    setTimeout(() => {
      if (isNavigatedFromLIT) {
        this.cityName = sessionStorage.getItem(SessionStorageKeys.CityName) || '';
        this.sourceAddress = sessionStorage.getItem(SessionStorageKeys.SourceAddress) || '';
        this.searchLocation = [this.cityName, this.sourceAddress]
          .filter(val => val)  
          .join(', ');
        this.clearSessionStorage();
        this.address = this.searchLocation;
      }
    }, 200);

    const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
    if (isCreateProperty && navigationEntries && navigationEntries.length > 0 && navigationEntries[0].type && (navigationEntries[0].type === 'reload')) {
        this.address = null;
        this.clearSessionStorage();
        this.router.navigate(['/expressmapsearch']);
    }
  }

  private getAddressFromLatitudeLongitude(): Promise<string | null> {
    const geocoder = new google.maps.Geocoder();
    const latLng = new google.maps.LatLng(this.latitude, this.longitude);
  
    return new Promise((resolve, reject) => {
      geocoder.geocode({ location: latLng }, (results, status) => {
        if (status && status === "OK" && results && results[0] && results[0].formatted_address) {
          resolve(results[0].formatted_address);
        } else {
          console.error("Geocoder failed due to:", status);
          this._notificationService.ShowErrorMessage("Failed to fetch the address");
          reject(null);
        }
      });
    });
  }

  updateStreetViewStatus() {
    this.isExpressMapStreetViewVisible = this.map.getStreetView().getVisible();
  }

  updateBuildingModalStreetView(event){
    this.isBuildingModalStreetViewVisible=event.isStreetViewVisible;
  }
 

  onIDChange(propID = null, searchText = null, searchTypeText = null) {
    const  searchValue = propID ?? searchText;
    this.isMarkerClicked = false;
    if (searchValue && isNumber(parseInt(searchValue))) {
      if (!!searchTypeText) {
        this.SpinLoader = true;
      }

      const searchCriteria: PropertyMapSearchRequestDTO = {
        PropertyId: propID ?? null,
        ZipCode: propID ? null : searchText,
        PageSize: propID ? 1 : DefaultMapSearchPageSize
      }
      const properties = this._propertyService.getPropertiesByMapSearch(searchCriteria);
      //this.showHideFusionLayer();
      properties.subscribe((result: ApiResponseListPropertyMapSearchSizeResponseDTO) => {
        if (!result.error) {
          let propertiesList = result.responseData;
          if (propertiesList && propertiesList.length > 0) {
            const center = this.selectedPosition ? this.selectedPosition : { Latitude: propertiesList[0].Location.Latitude, Longitude: propertiesList[0].Location.Longitude };
            this._mapService.SetIDCenter(this.map, center.Latitude, center.Longitude);
            this._mapService.SetMapZoomLevel(this.map, this.selectedPosition ? ZoomLevel20 : ZoomLevel18);
            this.sharedDataService.mapSearchPropertyList = propertiesList;
            this.loadPropertyPins(propertiesList, this.selectedPosition ? false : true);
            this.selectedPosition = undefined;
          }
        } else {
          this._notificationService.ShowErrorMessage(result.message);
        }
        this.SpinLoader = false;
      }, error => {
        this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchMapSearchProperties);
      });
    }
    else {
      this.SpinLoader = false;
      this.fetchViewPortProperties();
      this._mapService.MapReload(this.map);
    }
    this.sharedDataService.searchPostalCode = searchValue;
    this._mapService.addOverlay(this.checkedList, this.map, this.deckOverlay,'layerInfoCardInExpressMapSearch');
  }

  onComplete(event) {
    if(this.masterStrataObj) {
      this.fetchPropertyById(this.masterStrataObj.property.PropertyID);
      this.fromMasterStrata = false;
      this.masterStrataObj = undefined;
      return;
    }
    this.selectedPropertyCondoType = undefined;
    this.showStreetView = false;
    if (this.marker_pano) {
      this.marker_pano.setMap(null);
      this.marker_pano = null;
    }
    this.tabSelected = undefined;
    this.isExpressMapStreetViewVisible = false;
    this.isBuildingModalStreetViewVisible = false;
    this.fromMasterStrata = false;
    this.masterStrataObj = undefined;
    this.oldPolygon = {};
    if (sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch)) {
      const KeysToRemoveFromSS = [SessionStorageKeys.IsNavigationFromSearch,
        SessionStorageKeys.PropertyId,
        SessionStorageKeys.SelectedPropertyPos,
        SessionStorageKeys.ActiveGridRowNumber
      ];
    clearSessionStorage(KeysToRemoveFromSS);
      this.router.navigate(['/search']);
    }
    this._mapService.ClearSingleMarker(this.clickLocationPin);
    this._mapService.ClearPolygons(this.polylines);
    this._mapService.SetMapZoomLevel(this.map, this._mapService.GetMapZoomLevel(this.map) + 2);
    if (this.selectedPosition) {
      this._mapService.SetCenter(this.map, this.selectedPosition.Latitude, this.selectedPosition.Longitude);
    }
    this.selectedMarker && this._mapService.ClearSingleMarker(this.selectedMarker);
    this.clickLocationPin = null;
    this.isMarkerAnimationSet = false;
    this.selectedMarker = null;
    this.isNewProperty();
    var searchType = null;
    this.SpinLoader = false;
    this.newPropertyLocation = undefined;
    if (!!this.searchText) {
      searchType = 'zipcode';
    }
    if(this.panorama) {
      this.panorama.setVisible(false);
    }
    this.editPropertyForm.completeChanges()
    if(this.map.getStreetView() && this.map.getStreetView().getVisible()) {
      this.map.getStreetView().setVisible(false);
    }
    this.mapHeight = fullScreenMapHeight;
    this.isStreetView = false;
    this.selectionEnabled = false;
    this.onIDChange(this.propertyID, this.searchText, searchType);
    this._mapService.addOverlay(this.checkedList, this.map, this.deckOverlay,'layerInfoCardInExpressMapSearch');
    this._mapService.OnMapClick(this.map, (latlng) => {
      if (!this.isclusterclick) {
        this.showNewPropertyPin(latlng);
      }
      this.isclusterclick = false;
    });
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    const isFromMapSearch = sessionStorage.getItem(SessionStorageKeys.IsFromMapSearch)
    this.showAddEditPropertyForm = isFromMapSearch !== 'true';
    this.propertyDetails = new PropertyDetails();
  }

  private initMap() {
    this.mapOptions = new MapOptions('map-canvas-express');
    this.mapOptions.SetBasicOptions(MapEnum.MapType.Roadmap, 14, 7, null, !!this._loginService.UserInfo ? this._loginService.UserInfo.MetroCentroidLat : null, !!this._loginService.UserInfo ? this._loginService.UserInfo.MetroCentroidLong : null);

    this.mapOptions.RequireCtrlToZoom = false;
    this.mapOptions.FullscreenControl = false;
    this.mapOptions.FeaturesToHide.push(MapEnum.MapFeatures.Administrative_LandParcel,
      MapEnum.MapFeatures.HighwayRoad,
      MapEnum.MapFeatures.ControlledAccessHighwayRoad,
      MapEnum.MapFeatures.LineTransit, MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation, MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin, MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin, MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin, MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin, MapEnum.MapFeatures.SportsComplexPin);
    this.map = this._mapService.CreateMap(this.mapOptions);
     //Disable the default full screen expand and full screen compress controls
    this.map.get('streetView')
    .setOptions({
      fullscreenControl:false
    });

    //this.fusionLayers = new Array<any>();
    //let layer = this._mapService.LoadFusionTableLayer(this.map, environment.fusionTableId1, 'geometry');
    let polygonOption = new PolygonStyleOption();
    polygonOption.fillOpacity = 0.001;
    polygonOption.strokeColor = '#5e5e5e';
    polygonOption.strokeWeight = 1;
    polygonOption.strokeOpacity = 0.3;
    // layer = this._mapService.StyleFusionLayer(layer, polygonOption, null);
    // layer = this._mapService.HideFusionTableLayer(this.map, layer);
    // this.fusionLayers.push(layer);
    // layer = this._mapService.LoadFusionTableLayer(this.map, environment.fusionTableId2, 'geometry');
    // layer = this._mapService.StyleFusionLayer(layer, polygonOption, null);
    // layer = this._mapService.HideFusionTableLayer(this.map, layer);
    // this.fusionLayers.push(layer);

    this._mapService.AddSearchBox(this.map, 'searchbox', MapEnum.GoogleMapControlPosition.Top_Left, true, (place) => {
      this._mapService.ClearSingleMarker(this.neutralPin);
      const neutralPinUrl = this._mapHelperService.GetNeutralPinUrl();
      let latlng = new LatLng();
      latlng.Latitude = place.geometry.location.lat();
      latlng.Longitude = place.geometry.location.lng();
      this.neutralPin = this._mapService.PlaceMarker(this.map, latlng.Latitude, latlng.Longitude, false, neutralPinUrl);
      this.setupNewPinInfoWindow(this.neutralPin, place, latlng);
      this._mapService.OnMarkerClick(this.neutralPin, (event, marker, latlng) => {
        this.showNewPropertyPin(latlng);
      })
    });

    this._mapService.OnMapClick(this.map, (latlng) => {
      if (!this.isclusterclick) {
        this.showNewPropertyPin(latlng);

      }
      this.isclusterclick = false;
    });
    this._mapService.AddController(this.map, "idsearchbox", MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"searchCodeBox",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"layersdropdown",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"legend",MapEnum.GoogleMapControlPosition.Left_Bottom);
    this._mapService.AddController(this.map,"streetViewCamViewCam",MapEnum.GoogleMapControlPosition.Right_Top);
    this._mapService.AddController(this.map,"fullScreenExpand",MapEnum.GoogleMapControlPosition.Right_Top);
    this._mapService.AddController(this.map,"fullScreenCompress",MapEnum.GoogleMapControlPosition.Right_Top);
    this._mapService.AddController(this.map,"saveAndClose",MapEnum.GoogleMapControlPosition.Top_Right);
    this._mapService.AddController(this.map,"layerInfoCardInExpressMapSearch",MapEnum.GoogleMapControlPosition.LEFT_Top);

    this.createOverlay();
    this.deckOverlay = new GoogleMapsOverlay({
      layers: []
    });
    this._mapService.addOverlay(this.checkedList,this.map,this.deckOverlay,'layerInfoCardInExpressMapSearch')
    const isNavigatedFromFAC = sessionStorage.getItem(SessionStorageKeys.isNavigatedFromFAC);
    const isNavigatedFromERC = sessionStorage.getItem(SessionStorageKeys.IsNavigatedFromERC);
    const isNavigatedFromLIT = sessionStorage.getItem(SessionStorageKeys.isNavigatedFromLIT);
    const propertyId = sessionStorage.getItem(SessionStorageKeys.PropertyId);
    const isFromMapSearch = sessionStorage.getItem(SessionStorageKeys.IsFromMapSearch)
    if (isNavigatedFromFAC || isNavigatedFromERC || (isNavigatedFromLIT && !isFromMapSearch)) {
      this.propertyID = propertyId;
      this.onIDChange(propertyId);
    } else {
      this.fetchViewPortProperties();
    }
  }
  createOverlay() {
    this.ourOverlay = new google.maps.OverlayView();
    this.ourOverlay.draw = function () { };
    this.ourOverlay.setMap(this.map);
  }

  shareCheckedList(item:string[]){
    this._mapService.addOverlay(item,this.map,this.deckOverlay,'layerInfoCardInExpressMapSearch')
  }

  goFullScreen() {
    const mapContainer = document.getElementById('fullScreenMapContainer');
    if (mapContainer) {
      const fullscreenElement = document['fullscreenElement'];
      if (fullscreenElement) {
        this.exitFullscreen();
      } else {
        this.launchIntoFullscreen(mapContainer);
      }
    } else {
      console.error('Element with ID "mapcontainer" not found.');
    }
  }

  launchIntoFullscreen(element) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }
  }

  exitFullscreen() {
    this.showAddEditPropertyForm = true;
    if (document.exitFullscreen) {
      if (document['fullscreenElement']) {
        if(this.isStreetView)
          this.mapHeight = splitViewMapHeight;
        else
        this.mapHeight = fullScreenMapHeight;
        document.exitFullscreen();
        }
      }
    }

  expressMapFullScreenModeHandler() {
    this.showAddEditPropertyForm = false;
    this.mapHeight = '100vh';
    this.isExpressMapInFullScreenMode = true;
    this.goFullScreen();
  }

  openAerialView() {
    this.buildingModalComponent.goBuildingModalFullScreen();
  }

  expressMapExitFullScreenModeHandler() {
    this.showAddEditPropertyForm = true;
    this.isExpressMapInFullScreenMode = false;
    this.exitFullscreen();
  }

  buildingModalFullScreenModeHandler() {
    this.mapHeight = '0vh';
    this.isBuildingModalInFullScreenMode = true;
    this.goFullScreen();
  }

  buildingModalExitFullScreenModeHandler() {
    if(this.isStreetView)
      this.mapHeight = splitViewMapHeight;
    else
    this.mapHeight = fullScreenMapHeight;
    this.isBuildingModalInFullScreenMode = false;
    this.exitFullscreen();
  }

  private closeInfoWindows() {
    if (this.infoWindows) {
      for (var infowindow of this.infoWindows) {
        this._mapService.ClearInfoWindow(infowindow);
      }
    }
    this.infoWindows = new Array<any>();
  }

  private showNewPropertyPin(latlng: LatLng) {
    this.exitFullscreen();
    setTimeout(() => {
      this.isMarkerClicked = true;
      this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
      localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
      this.isMarkerSelectionChangable(latlng, null, (latlng, marker) => {
        // To show map in Street view
        this.showStreetView = true
        this.StreetView(latlng, "map-express", null);

        this._mapService.ClearSingleMarker(this.clickLocationPin);
        this._mapService.ClearSingleMarker(this.selectedMarker);
        const clickPinUrl = this._mapHelperService.GetClickedPositionPin();
        this.clickLocationPin = this._mapService.PlaceMarker(this.map, latlng.Latitude, latlng.Longitude, false, clickPinUrl);
        this.clickLocationPin.data = { Latitude: latlng.Latitude, Longitude: latlng.Longitude };
        this.selectedMarker = this.clickLocationPin;
        this.isNewProperty();
        this.isMarkerAnimationSet = true;
        this._mapService.GetLocationDetailsFromLatLng(latlng.Latitude, latlng.Longitude, (places) => {
           this.setupNewPinInfoWindow(this.clickLocationPin, places[0], latlng);
          this.openAddProperty(latlng, places[0], null);
        });
      });
    }, 100)
  }

  StreetView(latlng: any, mapId: any, property = null): any {  
    let instance = this;  
    const currentLatLng = { lat: latlng.Latitude, lng: latlng.Longitude };
    const sv = new google.maps.StreetViewService();
    sv.getPanoramaByLocation(currentLatLng, 50, function (data, status) {
      if (status == 'OK') {
        //google has a streetview image for this location, so attach it to the streetview div
        var panoramaOptions = {
          // position: {
          //   lat: latlng.Latitude,
          //   lng: latlng.Longitude
          // },
          pano: data.location.pano,
          //    addressControl: true,
          navigationControl: true,
          fullscreenControl: false,
          navigationControlOptions: {
            style: google.maps.NavigationControlStyle.SMALL
          },
          enableCloseButton: true,
          zoom: 0,
        };

        var myLatlng = new google.maps.LatLng(latlng.Latitude, latlng.Longitude);
        // Set the initial Street View camera to the center of the map
        sv.getPanorama({
          location: myLatlng
          , radius: 50
          , preference: google.maps.StreetViewPreference.BEST
          , source: google.maps.StreetViewSource.OUTDOOR
        }, function (data, status) {
          if (status === google.maps.StreetViewStatus.OK) {
            if (!instance.panorama) {
              instance.panorama = new google.maps.StreetViewPanorama(document.getElementById(mapId), panoramaOptions);
            }
            google.maps.event.addListener(instance.panorama, "closeclick", () => {
              instance.showStreetView = false;
              if (instance.marker_pano) {
                instance.marker_pano.setMap(null);
                instance.marker_pano = null;
              }
              instance.selectedMarker = instance._mapService.ClearSingleMarker(instance.selectedMarker);
              instance.isExpressMapStreetViewVisible = false;
              if (property) {
                instance._mapService.SetCenter(instance.map, property.Latitude, property.Longitude);
                instance._mapService.SetMapZoomLevel(instance.map, instance._mapService.GetMapZoomLevel(instance.map) + 2);
                instance.propertyMarkers = instance._mapService.ClearMarkers(instance.propertyMarkers);
                instance.selectedMarker = instance._mapService.PlaceMarker(instance.map, property.Latitude, property.Longitude);
                google.maps.event.clearListeners(instance.map, 'click');
              } else {
                instance._mapService.SetCenter(instance.map, latlng.Latitude, latlng.Longitude);
                instance._mapService.SetMapZoomLevel(instance.map, ZoomLevel20);
                instance.selectedMarker = instance._mapService.ClearSingleMarker(instance.selectedMarker);
                instance.setStreetviewValue();
                instance.loadPropertyPins(instance.sharedDataService.mapSearchPropertyList, false);
                instance.editPropertyForm.completeChanges();
              }
            });

            //    function processSVData({ data }: any, status) {
            const location = data.location!;

            instance.panorama.setPano(location.pano as string);
            let icon = instance._mapHelperService.GetClickedPositionPin();
            if (!!property) {
              instance.propertyDetails = property;
              icon = instance._mapHelperService.GetPropertyPinByResearchType(property.PropertyResearchTypeID, property.UseTypeID, property.SpecificUseID);
            } else {
              instance.propertyDetails.Location.Latitude = latlng.Latitude;
              instance.propertyDetails.Location.Longitude = latlng.Longitude;
            }
            if (instance.marker_pano) {
              instance.marker_pano.setMap(null);
              instance.marker_pano = null;
            }
            instance.marker_pano = new google.maps.Marker({
              position: myLatlng,
              map: instance.panorama,
              icon:icon,
            });
            // this.marker_pano.setIcon(icon);
            var heading = google.maps.geometry.spherical.computeHeading(data.location.latLng, myLatlng);
            instance.panorama.setPov(
              {
                heading: !!heading ? heading : (!!instance.panorama.getPhotographerPov() ? instance.panorama.getPhotographerPov().heading : 34),
                pitch: !!instance.panorama.getPhotographerPov() ? instance.panorama.getPhotographerPov().pitch : 10,
              }
            );
            instance.panorama.setVisible(true);
              instance.isExpressMapStreetViewVisible=true;
          } else {
            instance.showStreetView = false;
            if (this.marker_pano) {
              this.marker_pano.setMap(null);
              this.marker_pano = null;
            }
          }
        });
      }
      else {
        instance.showStreetView = false;
        instance.onIDChange(instance.property.PropertyID)
        if (this.marker_pano) {
          this.marker_pano.setMap(null);
          this.marker_pano = null;
        }
      }
    });


  }
  private isMarkerSelectionChangable(property: any, marker: any, onChangable: (latlng: any, marker: any) => void): any {
    const latlng = new LatLng();
    latlng.Latitude = property.Latitude ?? property.Location.Latitude;
    latlng.Longitude = property.Longitude ?? property.Location.Longitude;
    this.selectedMarker = this._mapService.ClearSingleMarker(this.selectedMarker);
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
    this.StagingIndexedDBService.clearCollection(IndexedDBCollections.stagingImages);
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    this.propertyDetails.Location.Latitude = latlng.Latitude;
    this.propertyDetails.Location.Longitude = latlng.Longitude;
    this.isStreetView = true;
    if(!this.isExpressMapInFullScreenMode) {
      this.mapHeight = splitViewMapHeight;
    }
    this.selectedPosition = latlng;
    if (this.showAddEditPropertyForm) {
      let sendModel: CommunicationModel = new CommunicationModel();
      sendModel.data = { latlng: latlng, marker: marker, callback: onChangable };
      sendModel.Key = 'IsPropertyDirty';
      this._communicationService.broadcast(sendModel);
    } else {
      // On initial pin click, no check required
      onChangable(latlng, marker);
    }
    
  }

  private fetchViewPortProperties() {
    if (this.map) {
      this._mapService.OnMapViewPortChangedOnce(this.map, (boundProperties: MapBound) => {
        if (!this.showAddEditPropertyForm && this._mapService.GetMapZoomLevel(this.map) <= ZoomLevel18) {
          this.polylines = this._mapService.ClearPolygons(this.polylines);
        }
        if (!this.searchText && !this.propertyID && !this.isMarkerClicked) {
          this.sharedDataService.mapSearchPropertyList = [];
          const NWCorner = new google.maps.LatLng(boundProperties.NorthEast.Latitude, boundProperties.SouthWest.Longitude);
          const SECorner = new google.maps.LatLng(boundProperties.SouthWest.Latitude, boundProperties.NorthEast.Longitude);
          let mainPolyText = '(';
          mainPolyText += NWCorner.lng() + ' ' + NWCorner.lat() + ','
          mainPolyText += boundProperties.NorthEast.Longitude + ' ' + boundProperties.NorthEast.Latitude + ','
          mainPolyText += SECorner.lng() + ' ' + SECorner.lat() + ','
          mainPolyText += boundProperties.SouthWest.Longitude + ' ' + boundProperties.SouthWest.Latitude + ','
          mainPolyText += NWCorner.lng() + ' ' + NWCorner.lat() + ')'
          const polygonText = 'POLYGON(' + mainPolyText + ')';
          const searchCriteria: PropertyMapSearchRequestDTO = {
            PolygonText: polygonText,
            NELat: boundProperties.NorthEast.Latitude,
            NELng: boundProperties.NorthEast.Longitude,
            SWLat: boundProperties.SouthWest.Latitude,
            SWLng: boundProperties.SouthWest.Longitude,
            PageSize: DefaultMapSearchPageSize
          }
          const properties = this._propertyService.getPropertiesByMapSearch(searchCriteria);

          properties.subscribe((result: ApiResponseListPropertyMapSearchSizeResponseDTO) => {
            if (!result.error) {
              const propertiesList = result.responseData;
              this.sharedDataService.mapSearchPropertyList = propertiesList;
              this.loadPropertyPins(propertiesList);
              if (this.selectedMarker != null) {
                for (const m of this.propertyMarkers) {
                  if (m.data.PropertyID === this.selectedMarker.data.PropertyID) {
                    this.selectedMarker = m;
                    this.isNewProperty();
                  }
                }
                this.addPinAnimation();
              }
            } else {
              this._notificationService.ShowErrorMessage(result.message);
            }
            this.fetchViewPortProperties();
          }, error => {
            this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchMapSearchProperties);
          });
        }
      });
    }
  }


  private loadPropertyPins(propertyList: PropertyMapSearchSizeResponseDTO[], setMapZoomByBounds = true) {
    this.SpinLoader = false;
    this.markerCluster = this._mapService.clearCluster(this.markerCluster);
    this.propertyMarkers = this._mapService.ClearMarkers(this.propertyMarkers);
    let markers: Array<any> = new Array<any>();
    const bounds = new google.maps.LatLngBounds();
    for (let property of propertyList) {
      const icon = this._mapHelperService.GetPropertyPinByResearchType(property.PropertyResearchTypeID, property.UseTypeID, property.SpecificUseID);
      let marker = this._mapService.PlaceMarker(this.map, property.Location.Latitude, property.Location.Longitude,false,icon);
      markers.push(marker);
      marker.data = property;
      this.propertyMarkers.push(marker);


      const isNavigatedFromERC = sessionStorage.getItem(SessionStorageKeys.IsNavigatedFromERC);
      const isNavigatedFromFAC = sessionStorage.getItem(SessionStorageKeys.isNavigatedFromFAC);
      const isNavigatedFromLIT = sessionStorage.getItem(SessionStorageKeys.isNavigatedFromLIT);
      const propertyId = sessionStorage.getItem(SessionStorageKeys.PropertyId);
      if(isNavigatedFromERC || isNavigatedFromFAC || isNavigatedFromLIT){
        isNavigatedFromERC && sessionStorage.removeItem(SessionStorageKeys.IsNavigatedFromERC);
        isNavigatedFromFAC && sessionStorage.removeItem(SessionStorageKeys.isNavigatedFromFAC);
        isNavigatedFromLIT && sessionStorage.removeItem(SessionStorageKeys.isNavigatedFromLIT);
        propertyId && sessionStorage.removeItem(SessionStorageKeys.PropertyId);
        this.handleMarkerInteraction(property, marker);
    }
    this._mapService.OnMarkerClick(marker,
      () => this.handleMarkerInteraction(property, marker)
    );

     this.setPropertyPinInfoWindow(marker, property);
      if (!!this.searchText) {
        const markerPos = marker.position;
        bounds.extend(markerPos);
      }
    }
    if (!!this.searchText && setMapZoomByBounds) {
      this.map.fitBounds(bounds);
    }
    this.markerCluster = this._mapService.createCluster(this.map, markers, 15, 10);

    let set = new Set(markers);
    this.markerCluster.markers.forEach(item => set.add(item));
    markers = Array.from(set.values());

    this._mapService.onClusterClick(this.markerCluster, (cluster) => {
      if (this.markerCluster.isZoomOnClick()) {
        this.map.setCenter(cluster.getCenter())
        this.isclusterclick = true;
      }
    });
  }

  private handleMarkerInteraction(property, marker)  {
    this.exitFullscreen();
    setTimeout(() => {
        this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
        localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
        this.isMarkerSelectionChangable(property, marker, () => {
                this.isMarkerAnimationSet = false;
                this._mapService.ClearSingleMarker(this.clickLocationPin);
                this._mapService.ClearSingleMarker(this.selectedMarker);
                this.clickLocationPin = null;
                this._mapService.ClearViewPortChangeListener(this.map);
                const latlng = new LatLng();
                latlng.Latitude = property.Latitude ?? property.Location.Latitude;
                latlng.Longitude = property.Longitude ?? property.Location.Longitude;
                this._mapService.MoveMapCenter(this.map, latlng.Latitude, latlng.Longitude, false);
                this.showStreetView = true;
                this.StreetView(latlng, "map-express", marker.data);
                this.openEditProperty(property, marker.data.PropertyID);
                setTimeout(() => {
                    for (let m of this.propertyMarkers) {
                       this._mapService.ClearMarkerAnimation(m);
                    }         
                    this.selectedMarker = marker;
                    this.isNewProperty();
                    this.isMarkerAnimationSet = true;
                    this.addPinAnimation();
                    this.isMarkerClicked = true;
                      this.fetchViewPortProperties();
                }, 1000);
        });
    }, 100)
  }

  initializeAndHandleProperty(latlng: any, propertyId: number) {
    this.mapEditProperty = new mapEditPropertyDTO();
    let latLong: LatLng;
    if (latlng instanceof LatLng) {
      latLong = latlng;
    } else {
      latLong = new LatLng();
      latLong.Latitude =  latlng.Latitude ?? latlng.Location.Latitude;
      latLong.Longitude = latlng.Longitude ?? latlng.Location.Longitude;
    }
    this.mapEditProperty.latLng = latlng;
    this.mapEditProperty.parcelProperties = this.properties;
    this.mapEditProperty.propertyId = propertyId;
    this.mapEditProperty.fromMasterStrata = this.fromMasterStrata;
    this.mapEditProperty.masterStrataObj = this.masterStrataObj;
  }

  openEditProperty(property: any, propertyId?: number) {
    this.initializeAndHandleProperty(property, propertyId);
    this.isAnExistingProperty = !!propertyId;
    const { Address } = property;
    const { StreetPrefix1, StreetSuffix1, StreetNumberMin, StreetNumberMax, AddressStreetName, StateID, ZipCode, CountryID, CityID } = Address;

    const lookup = this.sharedDataService.getLookupDropdowns();
    if (lookup) {
      const prefixList = lookup?.['PrefixID'] ?? [];
      const suffixList = lookup?.['StreetSuffix1'] ?? [];
      const streetPrefix = prefixList?.find(prefix => prefix.PrefixEnum === StreetPrefix1)?.PrefixName || '';
      const suffixData = suffixList?.find(suffix => suffix.SuffixId === StreetSuffix1);
      const streetSuffix = suffixData?.Suffix || '';
      const streetSuffixLong = suffixData?.SuffixName || '';
      const cities = lookup?.['CityID'] ?? [];
      const CityName = cities?.find(city => city.CityID === CityID)?.CityName || '';
      const states = lookup?.['StateID'] ?? [];
      const StateAbbr = states?.find(state => state.StateID === StateID)?.StateAbbr || '';
      const countries = lookup?.['CountryID'] || [];
      const CountryName = countries?.find(country => country.CountryId === CountryID)?.CountryName || '';
      const Zip = ZipCode || '';

      const formattedStreetNumbers = StreetNumberMax && StreetNumberMin !== StreetNumberMax
        ? `${StreetNumberMin}-${StreetNumberMax}`
        : StreetNumberMin;
      this.mapEditProperty.address = `${formattedStreetNumbers} ${streetPrefix} ${AddressStreetName}${streetSuffix ? ' ' + streetSuffix : ''}, ${CityName} ${StateAbbr} ${Zip}, ${CountryName}`;
      this.mapEditProperty.locationData = {
        MinimumStreetNumber: StreetNumberMin,
        MaximumStreetNumber: StreetNumberMax,
        StreetNameLong: `${AddressStreetName} ${streetSuffixLong}`,
        StreetNameShort: `${AddressStreetName} ${streetSuffix}`,
        StateCode: StateAbbr,
        ZipCode: Zip,
        CountryCode: CountryID,
        City: CityName,
        CountryName: CountryName,
      };
      this.updatePropertyform();
    } else {
      this.onComplete(true);
    }
  }

  openAddProperty(latlng: LatLng, locationDetails: any, propertyId?: number) {
    this.initializeAndHandleProperty(latlng, propertyId);
    this.mapEditProperty.address = locationDetails.formatted_address;
    this.mapEditProperty.locationData = this._mapService.GetFormattedObjectFromGooglePlaceAddressComponent(locationDetails.address_components);
    this.isAnExistingProperty = !!propertyId;
    this.updatePropertyform();
  }

  updatePropertyform(){
    if (!this.showAddEditPropertyForm) {
      this.showAddEditPropertyForm = true;
    }
    let communicationModel: CommunicationModel = new CommunicationModel();
    communicationModel.Key = 'updatePropertyForm';
    communicationModel.data = this.mapEditProperty;
    this._communicationService.broadcast(communicationModel); 
  }

  private setPropertyPinInfoWindow(marker: any, prop: any) {
    this._mapService.OnMarkerHover(marker, (event, eventmarker, latlng) => {
      this.closeInfoWindows();
      for (const m of this.propertyMarkers) {
        this._mapService.ClearMarkerAnimation(m);
      }
      const instance = this;
      let infoText = '';
      const infowWindow = document.createElement('div');
      // const MainPhotoUrl = prop.MainPhotoUrl ? prop.MainPhotoUrl : null; 
      const imagePath = environment.MediaS3Base + environment.MediaS3Path + '/' +
        environment.MediaS3ThumbnailPath + '/' + environment.MediaS3ThumbResolution + '/' + prop.MainPhotoUrl;
      infoText += `<div class="col-md-12 mb-2 p-0" style ="cursor:pointer;">`;
      infoText += `<div class="iwBox-img-wrap"><img src="`
        + imagePath + `" alt="img" style="width:100%; height:auto;" class="img-fluid"></div>`;
      infoText += `<ul class="iwTextBox">`;
      infoText += `<li class=" mapiw-text"><span>` + prop.PropertyName + ` </span></li>`;
      if (prop.PropertyName !== prop.Address.AddressText) {
        infoText += `<li class=" mapiw-text"><span>` + prop.Address.AddressText + `</span></li>`;
      }
      infoText += `<li class=" mapiw-text"><span>` + prop.CityName + ' ' + prop?.Address?.ZipCode + `</span></li>`;
      if (this.UnitId === this.metricUnit) {
        if (prop.UseTypeID === UseTypes.Land) {
          infoText += `<li class="mapiw-text"><span>` + prop.LotSizeSMFormatted + ' ' + this.UnitDisplayTextSize + `</span></li>`;
        } else if (prop.HasNoBuildingFootprints) {
          infoText += `<li class=" mapiw-text"><span>` + prop.ContributedGBASizeSMFormatted + ' ' + this.UnitDisplayTextSize + `</span></li>`;
        } else {
          infoText += `<li class=" mapiw-text"><span>` + prop.BuildingSizeSMFormatted + ' ' + this.UnitDisplayTextSize + `</span></li>`;
        }
      } else if (this.UnitId !== this.metricUnit) {
        if (prop.UseTypeID === UseTypes.Land) {
          infoText += `<li class=" mapiw-text"><span>` + prop.LotSizeSF + ` SF</span></li>`;
        } else if (prop.HasNoBuildingFootprints) {
          infoText += `<li class=" mapiw-text"><span>` + prop.ContributedGBASizeSF + ` SF</span></li>`;
        } else {
          infoText += `<li class=" mapiw-text"><span>` + prop.BuildingSF + ` SF</span></li>`;
        }
      }
      if (prop.UseTypeName ) {
        infoText += `<li class=" mapiw-text"><span>` + prop.UseTypeName + `</span>`;
      }
      if (prop.SpecificUseName != null) {
        infoText += `<span class=" mapiw-text" style="display:inline-block !important">-` + prop.SpecificUseName + `</span></li>`;
      } else {
        infoText += `</li>`
      }
      infoText += `<li class=" mapiw-text"><span> PID: ` + prop.PropertyID + `</span></li>`;
      infoText += `</ul>`;
      infoText += `</div>`;
      infowWindow.innerHTML = infoText;
      infowWindow.onclick = function () { instance.iwClick(marker, prop); };
      const infowindow = this._mapService.ShowInfoWindow(this.map, marker, event, infowWindow, false,
        this.previousMarker, this.previousHoverMarker, this.ourOverlay);
      if (this.infoWindows == null) {
        this.infoWindows = new Array<any>();
      }
      this.infoWindows.push(infowindow);
    });

    // this._mapService.OnMarkerMouseOut(marker, (event, marker) => {
    //   this.closeInfoWindows();
    // });
  }

  private setupNewPinInfoWindow(marker: any, place: any, latlng: LatLng) {
    this._mapService.OnMarkerHover(marker, (event, markerevent, latlng) => {
      this.closeInfoWindows();
      for (const m of this.propertyMarkers) {
        this._mapService.ClearMarkerAnimation(m);
      }
      let infoText = '';
      this.previousHoverMarker = marker;
      const infowWindow = document.createElement('div');
      infoText += `<div class="col-md-12 pt-2 pb-2" style ="cursor:pointer;">`;
      if (place.adr_address) {
        infoText += '<b>' + place.adr_address + '</b>';
      } else {
        infoText += '<span><b>' + place.formatted_address + '</b></span>';
      }
      infoText += '<br/><br/>';
      infoText += 'Latitude : <b>' + latlng.Latitude + '</b>';
      infoText += '<br/><br/>';
      infoText += 'Longitude : <b>' + latlng.Longitude + '</b>';
      infoText += `</div>`;
      infowWindow.innerHTML = infoText;
      const infowindow = this._mapService.ShowInfoWindow(this.map, marker, event, infowWindow, false,
        this.previousMarker, this.previousHoverMarker, this.ourOverlay);
      if (this.infoWindows == null) {
        this.infoWindows = new Array<any>();
      }
      this.infoWindows.push(infowindow);
    });
    this._mapService.OnMarkerMouseOut(marker, (event, markerevent) => {
      this.closeInfoWindows();
    });
  }

  private addPinAnimation(): void {
    if (this.isMarkerAnimationSet && this.selectedMarker != null) {
      this._mapService.SetMarkerAnimation(this.selectedMarker, MapEnum.Animation.Bounce);
      setTimeout(() => {
        if (this.selectedMarker != null) {
          this._mapService.ClearMarkerAnimation(this.selectedMarker);
        }
        setTimeout(() => {
          this.addPinAnimation();
        }, 1000);
      }, 2000);
    }
  }
  iwClick(marker, property) {
    this.markerClick(marker, property);
  }

  markerClick(marker, property) {
    const latlLong = {
      lat: () => marker.position.lat,
      lng: () => marker.position.lng
    };
    const latlng: LatLng = this._mapService.getLatLngObject(latlLong);
    // To show map in Street view
    this.exitFullscreen();
    setTimeout(() => {
      this.showStreetView = true;
      this.isStreetView = true;
      this.mapHeight = 'calc(60vh - 100px)';
      this.StreetView(latlng, "map-express", marker.data);
      this.openEditProperty(property, marker.data.PropertyID);
    }, 100);
    this.isMarkerAnimationSet = false;
    this._mapService.ClearSingleMarker(this.clickLocationPin);
    this.clickLocationPin = null;
    this.selectedProperty = marker.data;
    this.sharedDataService.mapSearchPropertyList = [];
    this.sharedDataService.mapSearchPropertyList.push(this.selectedProperty);
    this._mapService.ClearViewPortChangeListener(this.map);
    this._mapService.MoveMapCenter(this.map, latlng.Latitude, latlng.Longitude, true);
    setTimeout(() => {
      for (const m of this.propertyMarkers) {
        this._mapService.ClearMarkerAnimation(m);
      }
      this.selectedMarker = marker;
      this.isNewProperty();
      this.isMarkerAnimationSet = true;
      this.addPinAnimation();
      const latLng = marker.position; // returns LatLng object
      this.map.setCenter(latLng);
      this.isMarkerClicked = true;
      this.fetchViewPortProperties();
    }, 1000);
  }
  showDeleteModal(shapeId) {
    this.showDeletePolygonModal = true;
    this.buildingFootPrintIdToBeDeleted = shapeId;
  }
  deletePolygon() {
    this.buildingFootprintService.deleteBuildingFootprints(this.propertyID, this.buildingFootPrintIdToBeDeleted).subscribe((result: ApiResponseVoid) => {
      if (result.error) {
        this.showDeletePolygonModal = false;
        this._notificationService.ShowErrorMessage(result.message);
        return;
      }
      this.close();
      let commModel = new CommunicationModel();
      commModel.Key = 'deleteBuildingFootprint';
      commModel.data = result;
      this._communicationService.broadcast(commModel); 
    }, error => {
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToDeleteBuildingFootprints);
    }) 
  }
  close() {
    this.showDeletePolygonModal = false;
    this.buildingFootPrintIdToBeDeleted = undefined;
  }

  normalizeSelectionRect() {
    if (this.selectionRect.startX > this.selectionRect.endX) {
      [this.selectionRect.startX, this.selectionRect.endX] = [this.selectionRect.endX, this.selectionRect.startX];
    }
    if (this.selectionRect.startY > this.selectionRect.endY) {
      [this.selectionRect.startY, this.selectionRect.endY] = [this.selectionRect.endY, this.selectionRect.startY];
    }
  }
  
  setStreetviewValue() {
    this.isStreetView = false;
    this.mapHeight = fullScreenMapHeight;
  }

  buildingModalCamClickHandler(data:{ aerialViewCaptureDiv}){
    this.selectedCamera=CameraType.AerialView;
    this.selectionEnabled = true;
    this.divToCapture=data.aerialViewCaptureDiv;
    this.enableMapSelection();
    this.capturingAerialView = !this.isBuildingModalStreetViewVisible;
    
  }
  expressMapCamClickHandler(event){
    this.selectionEnabled = true;
    this.selectedCamera=CameraType.StreetView;
    this.divToCapture=this.captureDiv;
    this.enableMapSelection();
    this.capturingAerialView = !this.isExpressMapStreetViewVisible;
    this.marker_pano && this.marker_pano.setVisible(false);
    if(this.clickLocationPin){
     this.clickLocationPin.map = null;
    }
  }

  enableMapSelection(): void {
    this.mapCanvasClass = 'disable-pointer-events'; 
    this.canvas=this.streetViewCanvas;
     this.canvasWidth = 100 * window.innerWidth / 100,
    this.canvasHeight = 100 * window.innerHeight / 100;
    this.ctx = this.canvas.nativeElement.getContext('2d')!;
    //Setting the canvas site and width to be responsive 
    this.canvas.nativeElement.width =this.canvasWidth;;
    this.canvas.nativeElement.height = this.canvasHeight;
    this.canvas.nativeElement.style.width = this.canvasWidth; ;
    this.canvas.nativeElement.style.height =  this.canvasHeight;
    this.canvasx = this.canvas.nativeElement.offsetLeft;
    this.canvasy = this.canvas.nativeElement.offsetTop;
    this.canvas.nativeElement.style.background='rgba(0, 0, 0, 0.5)';
    this.canvas.nativeElement.style.cursor='crosshair';
    this.canvas.nativeElement.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.nativeElement.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvas.nativeElement.addEventListener('mousemove', this.handleMouseMove.bind(this));    
  }

  handleMouseDown(event: MouseEvent) {
    event.preventDefault();
    if (this.selectionEnabled){     
    this.startMouseX = event.offsetX;
    this.startMouseY = event.offsetY;
    this.selectionRect = { startX:this.startMouseX, startY:this.startMouseY, endX: 0, endY: 0 };
    this.isMouseDown = true;
    }
  }
  handleMouseMove(event:MouseEvent):void{
    event.preventDefault();
    if(this.selectionEnabled){   
      this.mouseX = event.offsetX;
      this.mouseY = event.offsetY;
      if (this.isMouseDown) {    
        this.canvas.nativeElement.style.background='transparent';
        this.ctx.clearRect(0, 0, this.canvas.nativeElement.width, this.canvas.nativeElement.height);  
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(0, 0, this.canvas.nativeElement.width, this.canvas.nativeElement.height);
        const width = this.mouseX - this.startMouseX;
        const height = this.mouseY - this.startMouseY;
        this.ctx.clearRect(this.startMouseX, this.startMouseY, width, height);
        this.ctx.strokeStyle = 'transparent';
        this.ctx.strokeRect(this.startMouseX, this.startMouseY, width, height);
      }
    }
  }

  handleMouseUp(e: MouseEvent): void {
    e.preventDefault();
    if (this.selectionEnabled && this.selectionRect) {
      this.selectionRect.endX =e.offsetX ;
      this.selectionRect.endY = e.offsetY;
      this.normalizeSelectionRect();
      this.ctx.clearRect(0, 0, this.canvas.nativeElement.width, this.canvas.nativeElement.height);
      this.captureScreen();
      this.isMouseDown = false;
      this.selectionEnabled = false;
    }
    this.marker_pano && this.marker_pano.setVisible(true)
    if(this.clickLocationPin){
      this.clickLocationPin.map = this.map;
    }
  }

  captureScreen() {
    const rect = this.selectionRect as any;
    html2canvas(this.divToCapture.nativeElement, {
        x: rect.startX,
        y: rect.startY,
        width: rect.endX - rect.startX,
        height: rect.endY - rect.startY,
        useCORS: true
      }).then((canvas) => {
        canvas.getContext('webgl',{ preserveDrawingBuffer:true });
        const base64Img = canvas.toDataURL('image/png');
        this.capturedImage = base64Img;
        this.setFileModalDetails(base64Img);
      })
      .catch((error) => {
        console.error('An error occurred while capturing the screen:', error);
 });
  }

  setFileModalDetails(base64Img) {
    this.setModalInfo(base64Img);
    this.showFileUploadModal = true;
    this.mediaTitle = 'Media Upload';
    this.mediaHeight = 'large';
    this.selectionRect = null;
  }

  setModalInfo(base64Img) {
    const rect = this.selectionRect as any;
    const image = new Image();
    image.src  = base64Img;
    this.files = new Array<MediaInfoDTO>();
    const media = {} as MediaInfoDTO;
    const base64str =  base64Img.split('base64,')[1];
    const decoded = atob(base64str);
    const size = decoded.length
    media.File = {
      name: this.capturingAerialView ? "Aerial.png" : "Streetview.png",
      webkitRelativePath: "",
      lastModified: new Date().getTime(),
      size: size
    } as any;
    media.Ext = "png";
    if (this.mediaService.mediaCheck(media) === false) {
      this._notificationService.ShowErrorMessage('File not Supported');
      return;
    }
    media.URL = this.capturedImage;
    media.UploadPathURL = this.capturedImage;
    media.Width = rect.endX - rect.startX;
    media.Height = rect.endY - rect.startY;
    media.Size = size;
    media.MediaTypeID = this.capturingAerialView ? MediaDTO.MediaTypeIDEnum.AerialImagery : MediaDTO.MediaTypeIDEnum.BuildingImage;
    media.MediaSubTypeID = this.capturingAerialView ? undefined : MediaDTO.MediaSubTypeIDEnum.Front;
    media.IsDefault = this.capturingAerialView ? false : true;
    media.IsOwnMedia = false;
    media.MediaSourceID = MediaDTO.MediaSourceIDEnum.Streetview;
    this.mediaService.fileExtentions(media);
    this.files.push(media);
    this.ArrayFiles = this.files;
    this.media = this.files[0];
    this.mediaHeight = 'mediaSource';
    this.showFileUploadModal = true;

    this.initialDetails = {
      PropertyID: this.mapEditProperty.propertyId,
      RelationID: this.mapEditProperty.propertyId,
      RelationshipTypeID: MediaDTO.MediaRelationTypeIDEnum.Property
    }
    this.ArrayFiles = [];
  }
  broadCastUploadOrCancelEvent () {
    if (this.selectedCamera = CameraType.AerialView) {
      let sendModel: CommunicationModel = new CommunicationModel();
      sendModel.Key = 'MediaUploaded';
      sendModel.data = true
      this._communicationService.broadcast(sendModel);
    }
  }

  onUploadEvent(path: string) {
    this.closeMediaUploadModal();
    this.isMediaListUpdated = true;
    this.broadCastUploadOrCancelEvent();
  }

  closeMediaUploadModal() {
    this.showFileUploadModal = false;
    this.mapCanvasClass = ''; 
    this.broadCastUploadOrCancelEvent();
  }

  mediaRetakeHandler(){
    this.showFileUploadModal = false;
    this.selectionEnabled=true;
    this.enableMapSelection();
  }

  onClearCanvas(){
    this.selectionEnabled=false;
    this.marker_pano.setVisible(this.panorama);
  }

  onSaveBuildingSize(areaSM) {
    this.editPropertyForm.onSaveBuildingSize(areaSM);
    this.editPropertyForm && this.editPropertyForm.onSaveBuildingSize(areaSM);
  }

  showDeleteBSModal(shapeId) {
    this.editPropertyForm.showDeleteModal(shapeId)
  }

  closeMapModal() {

  }

  isNewProperty(){
    this.isAnExistingProperty = !!(this.selectedMarker && this.selectedMarker.data.PropertyID);
  }

  saveOldPolygon({polygons}) {
    this.oldPolygon = polygons;
  }

  fetchPropertyById(propertyID) {
    if(this.selectedPosition) {
      this.mapEditProperty = new mapEditPropertyDTO();
      const response_location = this._propertyService.getPropertyById(propertyID);
      response_location.subscribe((result: ApiResponsePropertyDetailsSizeResponseDTO) => {
        if (!result.error) {
          const property = result.responseData;
          this.propertyDetails = new PropertyDetails();
          this.propertyDetails.Location.Latitude = property.Location.Latitude;
          this.propertyDetails.Location.Longitude = property.Location.Longitude;
          this.selectedPosition = { Latitude: property.Location.Latitude, Longitude: property.Location.Longitude };
          this.mapEditProperty = new mapEditPropertyDTO();
          this.openEditProperty(property, propertyID);
        }})
    }
  }

  clearMultiStrataObj(event) {
    this.fromMasterStrata = false;
    this.masterStrataObj = undefined;
  }

  fetchNextProperty(event) {
    this.selectedPropertyIdfromSearch = event.nextPropertyId;
    this.selecetdPositionLatLng = event.propertyLocation;
    this.selectedPosition = this.selecetdPositionLatLng;
    sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(this.selectedPropertyIdfromSearch));
    sessionStorage.setItem(SessionStorageKeys.SelectedPropertyPos, JSON.stringify(this.selecetdPositionLatLng));
    this.fetchPropertyByIdFromSearch(this.selectedPropertyIdfromSearch, this.selecetdPositionLatLng);
  }

  fetchPropertyByIdFromSearch(propertyID, pos) {
    this.exitFullscreen();
    setTimeout(() => {
      this.propertyDetails.Location.Latitude = pos.Latitude;
      this.propertyDetails.Location.Longitude = pos.Longitude;
      this.isStreetView = true;
      this.showStreetView = true;
      this.mapHeight = 'calc(60vh - 100px)';
      const response_propertdetails = this._propertyService.getPropertyById(propertyID);
      response_propertdetails.subscribe((result: ApiResponsePropertyDetailsSizeResponseDTO) => {
        if (!result.error) {
          const property = result.responseData
          this.property = {...property} as PropertyDetails;
          if (property) {
            this.StreetView(pos, "map-express", property);
            this.isMarkerClicked = true;
            this.isAnExistingProperty = true;
            this.openEditProperty(property, propertyID);
          }
        }
      })
    }, 100)
  }


  fetchStrataProperty(propertyID) {
    this.exitFullscreen();
    setTimeout(() => {
      this.tabSelected = EditPropertyTabs.Strata;
      this.showStreetView = true;
      const response_location = this._propertyService.getPropertyById(propertyID);
      response_location.subscribe((result: ApiResponsePropertyDetailsSizeResponseDTO) => {
        if (!result.error) {
          const property = result.responseData;
          let latLngObj = new LatLng();
          latLngObj.Latitude = property.Location.Latitude;
          latLngObj.Longitude = property.Location.Longitude;
          this.selectedPosition = { Latitude: property.Location.Latitude, Longitude: property.Location.Longitude }
          this.StreetView(latLngObj, "map-express", property);
          this.mapEditProperty = new mapEditPropertyDTO();
          this.openEditProperty(property, propertyID);
        }
      })
    }, 100)
    
  }
  updateNewLatLng(latlng: LatLng) {
    this.newPropertyLocation = latlng;
  }

  setSelectedPropertyCondoType(condoType) {
    this.selectedPropertyCondoType = condoType;
  }

  addNewChildUnitsToMaster({property, strataMin, isMultiStrata, strataList, isFreehold}) {
    this.fromMasterStrata = true;
    this.masterStrataObj = {
      property: property,
      minStrataUnit: strataMin,
      isMultiStrata,
      strataList,
      isFreehold
    }
    let latLngObj = new LatLng();
    latLngObj.Latitude = property.Latitude;
    latLngObj.Longitude = property.Longitude;
    this.selectedPosition = { Latitude: property.Latitude, Longitude: property.Longitude }
    this.StreetView(latLngObj, "map-express", property);
    this.mapEditProperty = new mapEditPropertyDTO();
    this.openEditProperty(property, null);


  }

  addNewFreeholdUnitToMaster({property, strataMin, isMultiStrata, strataList, isFreehold}) {
    this.fromMasterStrata = true;
    this.masterStrataObj = {
      property: property,
      minStrataUnit: strataMin,
      isMultiStrata,
      strataList,
      isFreehold: isFreehold
    }
    let latLngObj = new LatLng();
    latLngObj.Latitude = property.Latitude;
    latLngObj.Longitude = property.Longitude;
    this.selectedPosition = { Latitude: property.Latitude, Longitude: property.Longitude }
    this.StreetView(latLngObj, "map-express", property);
    this.mapEditProperty = new mapEditPropertyDTO();
    this.openEditProperty(property, null);


  }

  private handleNavigation(url: string): void {
    const isNavigatedFromERC = sessionStorage.getItem(SessionStorageKeys.IsNavigatedFromERC);
    const isCreateProperty = sessionStorage.getItem(SessionStorageKeys.IsCreateProperty);
    if (isNavigatedFromERC == 'true' && isCreateProperty == 'true' && url === '/') {
      this.clearSessionStorage();
    }
  }

  private clearSessionStorage(): void {    
    this.address = null;
    sessionStorage.removeItem(SessionStorageKeys.IsCreateProperty);
    sessionStorage.removeItem(SessionStorageKeys.Address);
    sessionStorage.removeItem(SessionStorageKeys.Latitude);
    sessionStorage.removeItem(SessionStorageKeys.Longitude);
    sessionStorage.removeItem(SessionStorageKeys.IsNavigatedFromERC);
    sessionStorage.removeItem(SessionStorageKeys.SourceAddress);
    sessionStorage.removeItem(SessionStorageKeys.CityName);
    sessionStorage.removeItem(SessionStorageKeys.isNavigatedFromLIT);
  }

  isParcelLayerEnabled() {
    return this.list.find(layer => layer.id === Overlays.Parcel)?.checked;
  }
}
