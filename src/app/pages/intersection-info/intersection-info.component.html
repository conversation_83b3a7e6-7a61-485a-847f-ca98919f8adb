<div [formGroup]="locationDetailsForm" *ngIf="property">
    <div class="row">
        <div class="col-md-6">
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Primary Street</div>
                <div class="col-md-7">
                    <input type="text" maxLength="100" class="form-control" formControlName="PrimaryStreet"
                        [(ngModel)]="property.Address.PrimaryStreet">
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Primary Access</div>
                <div class="col-md-7">
                    <input type="text" maxLength="100" class="form-control" formControlName="PrimaryAccess"
                        [(ngModel)]="property.Address.PrimaryAccess">
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Primary Traffic Count</div>
                <div class="col-md-7">
                    <input type="text" integer maxLength="10" class="form-control" formControlName="PrimaryTrafficCount"
                        [(ngModel)]="property.Address.PrimaryTrafficCount">
                </div>
            </div>
            <div class="row label-value-wrapper input-with-icon">
                <div class="col-md-5 label" for="text-input">Primary Traffic Count Date
                    Taken</div>
                <div class="col-md-7">
                    <input class="form-control" placeholder="Click to select a date"
                        formControlName="PrimaryTrafficCountDate" angular-mydatepicker name="PrimaryTrafficCountDate"
                        (click)="primaryDp.toggleCalendar()" [(ngModel)]="property.Address.PrimaryTrafficCountDate"
                        (dateChanged)="onDateChange('PrimaryTrafficCountDate', $event)" [options]="myDpOptions"
                        #primaryDp="angular-mydatepicker" />
                    <i class="far fa-calendar-alt" aria-hidden="true" (click)="primaryDp.toggleCalendar()">
                    </i>
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Primary Frontage</div>
                <div class="col-md-7">
                    <img class="icon" src="/assets/images/DrawMap.png" (click)="onPrimaryFrontageClicked()">
                    <input type="text" integer maxLength="20" class="form-control" formControlName="PrimaryFrontage"
                        [(ngModel)]="property.Address.PrimaryFrontage">
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Secondary Street</div>
                <div class="col-md-7">
                    <input type="text" maxLength="100" class="form-control" formControlName="SecondaryStreet"
                        [(ngModel)]="property.Address.SecondaryStreet">
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Secondary Access</div>
                <div class="col-md-7">
                    <input type="text" maxLength="100" class="form-control" formControlName="SecondaryAccess"
                        [(ngModel)]="property.Address.SecondaryAccess">
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Secondary Traffic Count</div>
                <div class="col-md-7">
                    <input type="text" integer maxLength="10" class="form-control"
                        formControlName="SecondaryTrafficCount" [(ngModel)]="property.Address.SecondaryTrafficCount">
                </div>
            </div>
            <div class="row label-value-wrapper input-with-icon">
                <div class="col-md-5 label" for="text-input">Secondary Traffic Count Date Taken</div>
                <div class="col-md-7">
                    <input class="form-control" placeholder="Click to select a date" formControlName="SecTrafficCntDate"
                        angular-mydatepicker name="SecTrafficCntDate" (click)="secondaryDp.toggleCalendar()"
                        [(ngModel)]="property.Address.SecTrafficCntDate"
                        (dateChanged)="onDateChange('SecTrafficCntDate', $event)" [options]="myDpOptions"
                        #secondaryDp="angular-mydatepicker" />
                    <i class="far fa-calendar-alt" aria-hidden="true" (click)="secondaryDp.toggleCalendar()">
                    </i>
                </div>
            </div>
            <div class="row label-value-wrapper">
                <div class="col-md-5 label" for="text-input">Secondary Frontage</div>
                <div class="col-md-7">
                    <img class="icon" src="/assets/images/DrawMap.png" (click)="onSeconaryFrontageClicked()">
                    <input type="text" integer maxLength="20" class="form-control" formControlName="SecondaryFrontage"
                        [(ngModel)]="property.Address.SecondaryFrontage">
                </div>
            </div>
        </div>
    </div>
</div>
<!--*********************** Retail Frontage Measurement ************************* -->
<div *ngIf="showPrimaryFrontageDistace || showSecondaryFrontageDistace">
    <imperium-modal [width]="'xl-large'" [height]="'large'" [(visible)]="showFrontage"
        [title]="'Frontage Measurement'" [bodyTemplate]="bodyTemplate" (visibleChange)="onFrontageModalClosed($event)">
        <ng-template #bodyTemplate>
            <app-distance-measurement-modal [latLng]="{lat: property.Location.Latitude, lng: property.Location.Longitude}"
                [retailFrontagePolyline]="showPrimaryFrontageDistace ? primaryFrontagePolyline : secondaryFrontagePolyline"
                (onRetailFrontageSave)="onFrontageSave($event)"></app-distance-measurement-modal>
        </ng-template>
    </imperium-modal>
</div>


