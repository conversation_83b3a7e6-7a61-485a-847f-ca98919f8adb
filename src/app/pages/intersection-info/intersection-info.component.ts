import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { IAngularMyDpOptions, IMyDateModel } from 'angular-mydatepicker';
import { LoginService } from '../../services/login.service';
import { CommunicationService } from '../../services/communication.service';
import { PropertyDetailsDTO } from '../../../app/api-client';

@Component({
  selector: 'app-intersection-info',
  templateUrl: './intersection-info.component.html',
  styleUrls: ['./intersection-info.component.css']
})
export class IntersectionInfoComponent implements OnInit {
  @Input() locationDetailsForm;
  @Input() property: PropertyDetailsDTO;
  @Output() onDateChanged = new EventEmitter<{ type: string; value: any }>();

  date = new Date();
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false,
    disableSince: { year: this.date.getFullYear(), month: this.date.getMonth() + 1, day: this.date.getDate() }
  };

  myDateInit: boolean = true;
  model: IMyDateModel = null;
  dateFormat: string;
  private _communicationService: CommunicationService;
  primaryFrontagePolyline: { distance: number, polyline: any };
  secondaryFrontagePolyline: { distance: number, polyline: any };
  showPrimaryFrontageDistace: boolean = false;
  showSecondaryFrontageDistace: boolean = false;
  clearFrontageListener: Subscription;

  constructor(private _loginService: LoginService,
    communicationService: CommunicationService) {
    this._communicationService = communicationService;

    this.clearFrontageListener = this._communicationService.subscribe('onClearFrontage').subscribe(result => {
      if(result && result.data) {
        this.primaryFrontagePolyline = { distance: null, polyline: null };
        this.secondaryFrontagePolyline = { distance: null, polyline: null };
      }
    })
  }

  ngOnInit(): void {
    this.dateFormat = this._loginService?.UserInfo?.DateFormat?.toLowerCase() || "dd/mm/yyyy";
    this.myDpOptions.dateFormat = this.dateFormat;
  }

  onDateChange(type: string, event: any): void {
    this.onDateChanged.emit({ type, value: event });
  }

  onPrimaryFrontageClicked() {
    this.showPrimaryFrontageDistace = true;
  }

  onSeconaryFrontageClicked() {
    this.showSecondaryFrontageDistace = true;
  }

  onFrontageModalClosed(bool) {
    this.showPrimaryFrontageDistace = false;
    this.showSecondaryFrontageDistace = false;
  }

  onFrontageSave({ distance, polyline }) {
    if (distance) {
      if (this.showPrimaryFrontageDistace) {
        this.property.Address.PrimaryFrontage = distance;
        this.locationDetailsForm.get('PrimaryFrontage') &&  this.locationDetailsForm.get('PrimaryFrontage').markAsDirty();
        this.primaryFrontagePolyline = { distance, polyline }
      } else if (this.showSecondaryFrontageDistace) {
        this.property.Address.SecondaryFrontage = distance;
        this.locationDetailsForm.get('SecondaryFrontage') &&  this.locationDetailsForm.get('SecondaryFrontage').markAsDirty();
        this.secondaryFrontagePolyline = { distance, polyline }
      }
    }
    this.showPrimaryFrontageDistace = false;
    this.showSecondaryFrontageDistace = false;
  }

  get showFrontage(): boolean {
    return this.showPrimaryFrontageDistace || this.showSecondaryFrontageDistace;
  }
  
  set showFrontage(value: boolean) {
    this.showPrimaryFrontageDistace = value;
    this.showSecondaryFrontageDistace = value;
  }
}
