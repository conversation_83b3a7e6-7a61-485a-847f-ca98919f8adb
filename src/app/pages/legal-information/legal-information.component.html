<div class="col-md-12" [formGroup]="locationDetailsForm" *ngIf="property">
    <div class="row">
      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">LGA </div>
          <div class="col-md-7">
            <input type="text" class="form-control" maxLength="100" formControlName="LGA" [(ngModel)]="property.LGA">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Geoscape Property ID </div>
          <div class="col-md-7">
            <input type="text" class="form-control" maxLength="20" formControlName="GeoscapePropertyID"
              [(ngModel)]="property.GeoscapePropertyID">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Council Tax ID</div>
          <div class="col-md-7">
            <input type="text" class="form-control" maxLength="50" formControlName="CounsilTaxID"
              [(ngModel)]="property.CounsilTaxID">
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Valuer General ID</div>
          <div class="col-md-7">
            <input type="text" maxlength="50" numericOnly [allowNegative]="false" [allowDecimal]="true" class="form-control" formControlName="ValuerGeneralID"
              [(ngModel)]="property.ValuerGeneralID">
          </div>
        </div>
        <div class="row label-value-wrapper">
          <div class="col-md-5 label" for="text-input">Legal Description</div>
          <div class="col-md-7">
            <textarea rows="2" class="form-control" maxLength="2000" formControlName="LegalDesc" name="LegalDesc"
              id="LegalDesc" [(ngModel)]="property.LegalDescription"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>


  