<form [formGroup]="salecompProperties" (ngSubmit)="SaveCompProperties()" style="width:100%;">
  <div class="form-group row" style="margin-top: 10px;">
    <div class="col-md-6">
      <input type="text" class="form-control" placeholder="Enter PropertyID / Property Name / Address"
        formControlName="SearchText" [(ngModel)]="SearchText" (keyup)="getProperties()" />
      <i [ngClass]="{'fa fa-spinner fa-spin':IsSearchInProgress}" class="spinRotate"></i>
    </div>
    <div class="col-md-6">
      <button type="submit" class="btn w-xs btn-warn pull-right margin-left-03"><i class="fa fa-save"></i>Save</button>
      <button type="button" class="btn w-xs btn-primary pull-right margin-left-03"
        (click)="closeSaleCompProperties()"><i class="fa fa-remove"></i>Cancel</button>
    </div>
  </div>
  <div class="form-group row tab-table-wrapper">
    <div class="col-md-12">
      <p-table #selectedPropertyTable [columns]="propertyHeader" [value]="Properties"  [scrollable]="true"
      [scrollHeight]="getScrollHeight()">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th class="tableHeader padding-left" [style.max-width]="'100px'">Select</th>
            <th *ngFor="let col of columns" class="tableHeader" [style.max-width]="getColumnWidth(col.header)">{{col.header}}</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-index="rowIndex" let-columns="columns">
          <tr>
            <td class="padding-left" [style.max-width]="'100px'"><input type="checkbox" (change)="addProperty(rowData,$event,index)"
                [(ngModel)]="rowData.IsMultiSelected" [ngModelOptions]="{standalone: true}"> </td>
            <td *ngFor="let col of columns" [style.max-width]="getColumnWidth(col.header)">
              {{rowData[col.field]}}
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage" let-columns="columns">
          <tr>
            <td colspan="7">
              No records found
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</form>
