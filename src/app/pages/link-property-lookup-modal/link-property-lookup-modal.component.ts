import { Component, OnInit, Input, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { PropertyService } from '../../services/api-property.service';
import { LoginService } from '../../services/login.service';
import { ApiResponseListMasterPropertiesDTO, LoginUserInfoDTO, PropertyDetailsDTO } from '../../../app/api-client';
import { MasterPropertiesInfoDTO } from '../../models/master-property-dto';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { CommonStrings } from '../../constants';

@Component({
  selector: 'app-link-property-lookup-modal',
  templateUrl: './link-property-lookup-modal.component.html',
  styleUrls: ['./link-property-lookup-modal.component.scss']
})
export class LinkPropertyLookupModalComponent implements OnInit {
  @ViewChild('#selectedPropertyTable') selectedPropertyTable: TableModule;
  salecompProperties: FormGroup;
  @Input() isFreehold = false;
  @Output() onClose = new EventEmitter();
  @Output() onSave: EventEmitter<Array<PropertyDetailsDTO>> = new EventEmitter<Array<PropertyDetailsDTO>>();
  Properties: MasterPropertiesInfoDTO[] = [];
  SearchText: string;
  @Input() isMasterProperty = false;
  @Input() PropId: number = null;
  IsSearchInProgress = false;
  SelectedProperties: Array<PropertyDetailsDTO> = new Array<PropertyDetailsDTO>();
  propertyHeader: any[];
  public totalSelectedPropertyRecords: number;
  public selectPropLoading = false;
  public totalPropertyRecords: number;
  public PropLoading = false;
  UnitDisplayTextSize: any;
  BuildingSizeHeader: string;
  UnitId: LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;
  metricUnit: LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;
  condoTypes = PropertyDetailsDTO.CondoTypeIDEnum;
  constructor(private propertyService: PropertyService, private _loginService: LoginService,
    private notificationService: NotificationService
  ) {
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.UnitId = this._loginService.UserInfo.UnitId;
    this.metricUnit = LoginUserInfoDTO.UnitIdEnum.Metric;
  }

  ngOnInit() {
    if (this.UnitId === LoginUserInfoDTO.UnitIdEnum.Metric) {
      this.BuildingSizeHeader = 'Building Size (' + this.UnitDisplayTextSize + ')';

    } else {
      this.BuildingSizeHeader = 'Building Size (' + this.UnitDisplayTextSize + ')';
    }
    this.IsSearchInProgress = false;
    this.salecompProperties = new FormGroup({
      'SearchText': new FormControl('')
    });

    this.getTableHeader();
  }

  getTableHeader() {
    this.propertyHeader = [
      { field: 'PropertyID', header: 'Property ID' },
      { field: 'PropertyName', header: 'Property Name' },
      { field: 'Address', header: 'Address' },
      { field: 'CityName', header: 'City' },
      { field: 'PropertyUse', header: 'Property Use' },
      { field: 'BuildingSF', header: this.BuildingSizeHeader },
      { field: 'BuildingSizeSM', header: this.BuildingSizeHeader }
    ];
  }

  getProperties() {
    this.IsSearchInProgress = true;
    if (this.SearchText.length >= 3) {
      const masterPropertiesResponse = this.propertyService.getMasterPropertydetails(this.SearchText, this.isFreehold ? this.condoTypes.ChildFreehold : this.condoTypes.Strata );
      masterPropertiesResponse.subscribe((result: ApiResponseListMasterPropertiesDTO) => {
        if (!result.error) {
          this.Properties = result.responseData || [];
        if (!!this.PropId && this.isMasterProperty) {
          this.Properties = this.Properties.filter(x => x.PropertyID !== this.PropId);
        }
        this.IsSearchInProgress = false;
        } else {
          this.IsSearchInProgress = false;
          this.notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this.IsSearchInProgress = false;
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchMasterProperties);
      });
    } else {
      this.IsSearchInProgress = false;
    }
  }

  addProperty(value, event, index) {
    const isChecked = event.target.checked;
    if (isChecked) {
      this.changeChecked(index)
      this.SelectedProperties = new Array<PropertyDetailsDTO>();
      this.SelectedProperties.push(value);

    } else {
      this.SelectedProperties.pop();
    }
  }

  changeChecked(indexVal: number) {
    for (let i = 0; i < this.Properties.length; i++) {
      if (i !== indexVal) {
        this.Properties[i].isSelected = false;
      }
    }
  }

  SaveCompProperties() {
    this.onSave.emit(this.SelectedProperties);
    this.onClose.emit();
  }

  closeSaleCompProperties() {
    this.onClose.emit();
  }

  getColumnWidth(header: string): string {
    const BuildingSizeHeader = this.BuildingSizeHeader;
    switch (header) {
      case 'Property ID': return '200px';
      case 'Property Name': return '150px';
      case 'Address': return '100px';
      case 'City': return '150px';
      case 'Property Use': return '150px';
      case BuildingSizeHeader: return '150px';
      default: return '120px';
    }
  }

  getScrollHeight() {
    return this.Properties?.length > 10 ? '40vh' : 'auto';
  }
}
