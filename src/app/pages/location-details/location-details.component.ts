import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { PropertyService } from '../../services/api-property.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { CommunicationService } from '../../services/communication.service';
import { PropertyFormControls } from '../../enumerations/propertyFormControls';
import { LocationFormControls } from '../../enumerations/locationFormControls';
import { UseTypes } from '../../enumerations/useTypes';
import { AddressTypeValues, LocationValidationsRequiredFields, PartOfComplex } from '../../constants';
import { ApiResponseListPropertyIntersectResponseDTO, PropertyDetailsDTO, PropertyIntersectRequestDTO } from '../../../app/api-client';

@Component({
  selector: 'app-location-details',
  templateUrl: './location-details.component.html',
  styleUrls: ['./location-details.component.css']
})
export class LocationDetailsComponent implements OnInit {
  @Input() property: PropertyDetailsDTO;
  @Input() locationDetailsForm;
  @Input() streetMinMaxError: boolean = false;
  @Input() streetPrefixes;
  @Input() streetSufixes;
  @Input() cities;
  @Input() states;
  @Input() counties;
  @Input() countries;
  @Input() quadrants;
  @Input() showMasterStrataAlert;
  @Input() propertyCopy;
  @Input() dataArray;
  @Output() onChangeStrata = new EventEmitter();
  @Output() setAddressAsPropertyName = new EventEmitter<boolean>();
  @Output() changeMapLocation = new EventEmitter();
  @Input() propertyLookups;
  public addressTypeValues = AddressTypeValues;
  generalUseType = UseTypes;
  roofTopSource;
  complexTypes;
  complexTypeId;
  PropertyFormControlsEnum = PropertyFormControls;
  EnumCondoTypeNames = PropertyDetailsDTO.CondoTypeIDEnum;
  updateMarketListListener: Subscription;
  newPropertyFetchedListener: Subscription;

  MarketList: any;
  SubmarketList: any;

  ngOnDestroy(): void {
    this.updateMarketListListener?.unsubscribe();
    this.newPropertyFetchedListener?.unsubscribe();
  }

  constructor( private propertyService: PropertyService, private communicationService: CommunicationService, private notificationService: NotificationService) {
    this.updateMarketListListener = this.communicationService.subscribe('updateMarketList').subscribe(result => {
      const { useTypeID } = result?.data;
        this.onSpecificUseChange(useTypeID);
    });
    this.newPropertyFetchedListener = this.communicationService.subscribe('newPropertyFetched').subscribe(result =>{
      this.fetchInitialMarketList();
      this.initData();
    });
  }

  onSpecificUseChange(useTypeId) {
    this.MarketList = [];
    this.property.MarketId = null;
    this.SubmarketList = [];
    this.property.SubMarketID = null;
    if (!this.property.MetroId) {
      this.getPropertyIntersectMarketSubmarket(this.property);
    } else {
      this.getMarketList(this.property.MetroId, useTypeId);
    }
  }

  fetchInitialMarketList() {
    if (this.property.MetroId) {
      this.getMarketList(this.property.MetroId, this.property.UseTypeID);
    } else {
      this.getPropertyIntersectMarketSubmarket(this.property);
    }
    if (this.property.MarketId) {
      this.getAllSubMarketList(this.property.MarketId);
    }
  }

  getPropertyIntersectMarketSubmarket(value: PropertyDetailsDTO) {
    const propertyIntersectRequestDTO: PropertyIntersectRequestDTO = {
      MetroID: value?.MetroId, PropertyID: value?.PropertyID, UseTypeID: value?.UseTypeID, Latitude: value?.Location?.Latitude, Longitude: value?.Location?.Longitude
    }
    const response = this.propertyService.getPropertyIntersectMarketSubmarket(propertyIntersectRequestDTO);
    response.subscribe((result: ApiResponseListPropertyIntersectResponseDTO) => {
      if (!result?.error) {
        const propMarketData = result.responseData[0] || {};
        if (!!propMarketData.MetroID) {
          this.getMarketList(propMarketData.MetroID, value.UseTypeID);
          if (!this.property.MarketId) {
            this.getAllSubMarketList(propMarketData.MarketID);
          }
          this.property.MetroId = propMarketData.MetroID;
        }
      } else {
        this.notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message);
    });
  }

  getMarketList(metroID, UseTypeID) {
    const marketsList = this.propertyLookups?.['MarketID'] || [];
    this.MarketList = marketsList.filter(market => market.MetroID === metroID && market.PropertyType === UseTypeID);
    if (!this.property.MarketId) { this.property.MarketId = null; }
  }

  // to get all selected Market submarket List
  getAllSubMarketList(marketID) {
    const submarketsList = this.propertyLookups?.['SubMarketID'] || [];
    setTimeout(() => {
      this.SubmarketList = submarketsList.filter(submarket => submarket.MarketID === marketID);
    }, 0);
  }


  getRoofTopSources() {
    this.roofTopSource = this.propertyLookups?.['RoofTopSourceID'] || [];
    this.roofTopSource?.forEach(source => {
      source.RoofTopSource = source.RoofTypeName;
      source.RoofTopSourceID = source.RoofTypeID;
    })
  }

  // To get all complex type.
  getComplexTypes() {
    this.complexTypes = this.propertyLookups?.['ComplexTypeID'] || [];
    this.complexTypes?.forEach(complexType => {
      if (complexType?.ComplexTypeName === PartOfComplex) {
        this.complexTypeId = complexType?.ComplexTypeID;
      }
      this.onComplexTypesFetched();
    });
  }

  loadCities() {
    this.cities = (this.propertyLookups?.['CityID'] || []).filter(city => {
      return city.StateID === this.property.Address?.StateID 
    });
    if (this.property.Address?.CityID) {
      this.property.Address.CityID = null
    }
  }

  onComplexTypesFetched() {
    if ((this.complexTypeId) && (this.property?.Address?.PartOfComplex === this.complexTypeId)) {
      this.locationDetailsForm?.get(LocationFormControls?.Complex)?.clearValidators();
      this.locationDetailsForm?.get(LocationFormControls?.Complex)?.setValidators([Validators.required]);
    }
  }

  ngOnInit(): void {
    this.initData();
  }


  updateValidations(){
    Object?.values(LocationFormControls)?.forEach(controlName => {
      const isValidationRequired = LocationValidationsRequiredFields?.includes(controlName);
    
      // Check if the control exists in the FormGroup
      if (this.locationDetailsForm?.contains(controlName)) {
        const control = this.locationDetailsForm?.get(controlName);
        
        // Update validators for the existing control
        control?.setValidators(isValidationRequired ? Validators.required : null);
        
        // Recalculate the control's validation status
        control?.updateValueAndValidity();
      } 
    });
  }


  initData(){
    this.getComplexTypes();
    this.getRoofTopSources();
    this.updateValidations();
    //set the address type value
    this.locationDetailsForm?.patchValue({
      AddressType: this.property?.Address?.AddressType ? 1 : 0,
      UseAddressAsPropertyName: this.property?.UseAddressAsPropertyName
    });
    this.locationDetailsForm?.get(LocationFormControls?.AddressType)?.valueChanges?.subscribe(AddressType => {
      if (AddressType === this.addressTypeValues?.Address) {
        this.clearValidators(['Quadrant', 'EastWestStreet', 'NorthSouthStreet']);
        this.setValidators(['StreetNumberMin', 'AddressStreetName'], [Validators.required]);
      } else {
        this.clearValidators(['StreetNumberMin', 'AddressStreetName']);
        this.setValidators(['Quadrant', 'EastWestStreet', 'NorthSouthStreet'], [Validators.required]);
      }
    });
    
    this.locationDetailsForm?.get(LocationFormControls?.PartOfComplex)?.valueChanges?.subscribe(partOfComplex => {
      const complexControl = this.locationDetailsForm?.get(LocationFormControls?.Complex);
      complexControl?.clearValidators();
      if (this.complexTypeId && partOfComplex === this.complexTypeId) {
        complexControl?.setValidators([Validators.required]);
      }
      complexControl?.updateValueAndValidity();
    });
  }

  setValidators = (controls: string[], validators: any[]) => {
    controls.forEach(ctrl => {
      const control = this.locationDetailsForm?.get(LocationFormControls?.[ctrl]);
      control?.setValidators(validators);
      control?.updateValueAndValidity();
    });
  };
  
  clearValidators = (controls: string[]) => {
    controls.forEach(ctrl => {
      const control = this.locationDetailsForm?.get(LocationFormControls?.[ctrl]);
      control?.clearValidators();
      control?.updateValueAndValidity();
    });
  };
  addressTypeChange(value) {
    this.property.Address.AddressType = value ? true : false;
  }

  addressAsPropertyName(value: boolean) {
    this.setAddressAsPropertyName?.emit(value);
  }

  showStrataConfirmation() {
    this.onChangeStrata?.emit();
  }

  changeMarket(event) {
    if (!!event) {
      const value = event.MarketID;
      this.property.SubMarketID = null;
      this.SubmarketList = [];
      this.getAllSubMarketList(value);
    }
  }

}
