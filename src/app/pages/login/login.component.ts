// Angular core imports
import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
// Environment imports
import { environment } from '../../../environments/environment';
// Application-specific imports
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { LoginService } from '../../services/login.service';
import { LoginRequestDTO, LoginUserInfoDTO } from '../../api-client';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {


  _router: Router;
  validationError: boolean = false;
  invalidLogin: boolean = false;
  CountryId: number;
  errorMessage:string;
  SSOToken: string = null;
  propertyId: any;
  address: string = null;
  
  public loginDetails: LoginUserInfoDTO;
  public showLoginPage = true;
  public invalidMessage = '';
  sourceApplicationId: any;

  constructor(
    private _loginService: LoginService,
    private router: Router
  ) { }
  
  ngOnInit() { }

  login(loginForm: NgForm) {

    this.invalidLogin = false;
    this.validationError = false;
    if (loginForm.valid) {
      let userdata = {
        username: loginForm.value.username,
        password: loginForm.value.password
      };
      this.processLogin(userdata);
    }
    else {
      this.validationError = true;
    }
  }

  logout() {
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }

  private processLogin(userdata: LoginRequestDTO) {

    this.invalidLogin = false;
    const response_userLogin = this._loginService.login(userdata);
    response_userLogin.subscribe(result => {
      const data = result;

      if(!data.error && data.responseData.Response.ResponseCode==1){
        // this._loginService.UserInfo.IsLoggedin = true;
        this._loginService.UserInfo.DateFormat = data.responseData.UserInfo.DateFormat;
        this._loginService.UserInfo.CountryID = data.responseData.UserInfo.CountryID;
        this._loginService.UserInfo.EntityID = data.responseData.UserInfo.EntityID;
        this._loginService.UserInfo.PersonName = data.responseData.UserInfo.PersonName;
        this._loginService.UserInfo.UnitId = data.responseData.UserInfo.UnitId;
        this._loginService.UserInfo.UnitDisplayTextSize = data.responseData.UserInfo.UnitDisplayTextSize;        
        this._loginService.UserInfo.MetroCentroidLat = data.responseData.UserInfo.MetroCentroidLat || -37.814;
        this._loginService.UserInfo.MetroCentroidLong = data.responseData.UserInfo.MetroCentroidLong ||  144.96332;
        this._loginService.UserInfo.MainPhotoUrl = data.responseData.UserInfo.MainPhotoUrl;
        this._loginService.UserInfo.RoleID = data.responseData.UserInfo.RoleID;
        this._loginService.UserInfo.RoleName = data.responseData.UserInfo.RoleName;
        
        sessionStorage.setItem(SessionStorageKeys.AccessToken, data.responseData.Token);

        var CryptoJS = require("crypto-js");
        var encryptedData = CryptoJS.AES.encrypt(JSON.stringify(this._loginService.UserInfo), environment.EncryptionKey.toString());      
        sessionStorage.setItem(SessionStorageKeys.LogInData, encryptedData);
        this.router.navigate(['/search']);
      }
      else {
        this.errorMessage= !!data.responseData ? data.responseData.Response[0].ErrorMessage : !!data ? data : "Invalid Login";
        this.invalidLogin = true;
      }
    });
  }


}
