// Angular core imports
import { AfterViewInit, Component, Input, OnDestroy, OnInit, Output, EventEmitter } from '@angular/core';
import { environment } from './../../../../environments/environment';
import { GoogleMapsOverlay } from '@deck.gl/google-maps';
import * as MapEnum from './../../../modules/map-module/models/MapEnum';
import { azureMap } from './../../../constants';
import { MapService } from './../../../modules/map-module/service/map-service.service';
import { SharedDataService } from './../../../services/shareddata.service';
// Global declarations
declare var google: any;

@Component({
  selector: 'app-map-switch',
  templateUrl: './map-switch.component.html',
  styleUrls: ['./map-switch.component.css']
})
export class MapSwitchComponent implements AfterViewInit , OnDestroy{
  @Input() id:string;
  @Input() map: any;
  @Input() checkedList: string[];
  @Input() deckOverlay: any;
  
  @Output() onAddMapSwitchBtnControler: EventEmitter<void> = new EventEmitter<void>()
  mapCategory = MapEnum.MapCategory;
  constructor(private _mapService: MapService, private _sharedDataService: SharedDataService) { }

  ngAfterViewInit(): void {
    
    if(!this.checkedList) {
      this.checkedList = []
    } if(!this.deckOverlay) {
      this.deckOverlay = new GoogleMapsOverlay({
        layers: []
      });
    } if(!this.id) {
      this.id = 'map-switch';
    }

      if(environment.enableAzureMap){
        this._mapService.AddController(this.map, this.id ,MapEnum.GoogleMapControlPosition.LEFT_Top);
        this.onAddMapSwitchBtnControler.emit();
      }

      google.maps.event.addListener(this.map, 'maptypeid_changed', () => {
        this.updateAzureMapOverlayOnMapTypeChange()
      });
      google.maps.event.addListener(this.map, 'zoom_changed', () => {
        if(this._sharedDataService.IsAzureMapOn){
          if(this._mapService.GetMapZoomLevel(this.map) > azureMap.MaxZoom.Satellite) {
            this._mapService.addAzureMapDeckLayer(this.map, this.checkedList, this.deckOverlay);
          } else {
            this._mapService.addAzureMapOverlay(this.map, this.checkedList, this.deckOverlay);
          }
        }      
    })

  }

  ngOnDestroy(): void {
    this._sharedDataService.IsAzureMapOn = false;
    this._sharedDataService.checkedList = [];
  }

  private updateAzureMapOverlayOnMapTypeChange() {
    this._mapService.toggleAzureMapOverlay(this.map, this.checkedList, this.deckOverlay)
  }

  SwitchMapTo(mapType: MapEnum.MapCategory.Google | MapEnum.MapCategory.Azure) {
    switch(mapType) {
      case MapEnum.MapCategory.Google:
        this._mapService.clearAzureMapOverlay(this.map, this.checkedList, this.deckOverlay);
        this._sharedDataService.IsAzureMapOn = false;
        break;
      case MapEnum.MapCategory.Azure:
        this._mapService.addAzureMapOverlay(this.map, this.checkedList, this.deckOverlay);
        this._sharedDataService.IsAzureMapOn = true;
        break;
    }
  }

  IsAzureMap() {
    if(this.map){
      return !!(this.map && (this.map.IsAzureMap || this.map. IsAzureMapDeckLayer || this.map.IsAzureRoadMap));
    }
  }
}
