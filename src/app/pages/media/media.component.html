<div class ="refresh-button" ><i class="fa fa-refresh refresh-icon" (click)="refreshAllMedia()" ></i></div>
<div *ngIf="galleryImages && galleryImages.length > 0" class="galleryContainer">
  <div *ngFor="let image of galleryImages" data-testId="media-gallery">
    <div class="gallery">
      <div>

        <img [src]="image.URL" class="img-pointer" (click)="showImage(image)">
      </div>
      <div class="bottom-wrapper">
        <div class="desc">

          <div class="actions">
            <i class="fas fa-pen" data-testId="media-edit"  aria-hidden="true" (click)="editMedia(image.MediaID)"></i>
            <i class="fa fa-trash" data-testId="media-delete" aria-hidden="true" (click)="deleteMedia(image.MediaID)"></i>
            <i class="fa fa-star" data-testId="media-default" style="color: rgb(247, 247, 93);" aria-hidden="true" *ngIf="image.IsDefault" (click)="setAsDefault(image.MediaID)"></i>
            <i class="fa-regular fa-star" data-testId="media-not-default" aria-hidden="true" *ngIf="!image.IsDefault" (click)="setAsDefault(image.MediaID)"></i>
            <i class="fa fa-history change-log-icon" aria-hidden="true" title="ChangeLog"
              (click)="viewChangeLog(image.MediaID)"></i>
          </div>

          <span class="nameLabel">Name : <b>{{image.MediaName}}</b></span>
          <span class="nameLabel">Modified Date : <b>{{image.ModifiedDate | date:'MM/dd/yyyy'}}</b></span>
          <span class="nameLabel">Modified By : <b>{{image.ModifiedByName}}</b></span>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="uploads upload-wrapper">
  <div class="main-container">
    <div class="buttons loader-btn" data-testId="media-add">
      <label for="inputFiles" class="btn btn-info file-input-btn">
        <i class="glyphicon glyphicon-plus"></i>
        <img src="assets/images/add_blue.png" height="50" width="50">
        <input id="inputFiles" name="files[]" multiple="" type="file" accept="image/*"
          (change)="fileChangeEvent($event)">
      </label>
    </div>
  </div>

</div>

<div *ngIf="showMediaChangeLogDetails">
  <imperium-modal [(visible)]="showMediaChangeLogDetails" [title]="'Media Changelog'" [width]="'large'"
    [bodyTemplate]="bodyTemplate">
    <ng-template #bodyTemplate>
      <app-changelog-modal [changelogType]="changelogType" [parentId]="parentId" class="change-log">
      </app-changelog-modal>
    </ng-template>
  </imperium-modal>
</div>