// Angular core imports
import { Component, Input, OnInit, NgZone } from '@angular/core';
import { Router } from '@angular/router';
// Environment config
import { environment } from '../../../environments/environment';
// Third-party imports
import { Subscription } from 'rxjs';
// Application-specific DTOs and types
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { FileTypes } from '../../modules/aws/types/types';
// Application-specific enums, constants, and models
import { CommonStrings } from '../../constants';
import { MediaInfoDTO } from '../../models/media-dto';
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
// Application-specific services
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
import { MediaService } from '../../services/media.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { SharedDataService } from '../../services/shareddata.service';

//Api-client imports
import { ApiResponseListMediaDTO, ApiResponseNull, AuditLogEntityTypes, MediaDeleteRequestDTO, MediaRequestDTO } from '../../api-client';

@Component({
  selector: 'app-media',
  templateUrl: './media.component.html',
  styleUrls: ['./media.component.css']
})
export class MediaContainerComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  files: MediaInfoDTO[] = [];
  galleryImages: MediaInfoDTO[] = [];
  eventSubscription: Subscription;
  fileuploaded: Subscription;
  uploadStarted = false;
  url: any;
  mediaUrl: any;
  parentId: number;
  showMediaChangeLogDetails: boolean = false;
  changelogType = AuditLogEntityTypes.Media;

  constructor(private router: Router,
    private mediaService: MediaService,
    private communicationService: CommunicationService,
    private notificationService: NotificationService,
    private zone: NgZone
    , private _sharedDataService: SharedDataService) {
    this.eventSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
      this.zone.run(() => {
        this.initialDetails = result.data;
        //this.clearAll();
        this.refreshAllMedia();
      });
    });

    this.fileuploaded = this.communicationService.subscribe('onFileUpload').subscribe(result => {
      this.onUploadEvent(result.data);
    });

    this.mediaUrl = `${environment.MediaS3DynamicImageBase}` + `${environment.MediaS3Path}`+'/';
   
  }

  private onUploadEvent($event) {
    this.refreshAllMedia();
  }

  editMedia(mediaId: number) {

    let file = this.galleryImages.find(x => x.MediaID == mediaId);
    let files = new Array<MediaInfoDTO>();
    files.push(file);
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, files: files };
    model.Key = 'showFileUploadModal';
    this.communicationService.broadcast(model);
  }

  viewChangeLog(parentId:number) {
    this.showMediaChangeLogDetails = true;
    this.parentId = parentId;
  }

  showImage(image) {
    if(image.Ext.toLowerCase() == FileTypes.PNG || image.Ext.toLowerCase() == FileTypes.JPG){
    let imgUrls: string[]=[];
    let imgIndex: number;
    let index = 0;
    this.galleryImages.forEach(img => {
      let path = this.mediaUrl + img.Path;
      imgUrls.push(path)
      if (img.URL == image.URL)
        imgIndex = index;

      index++;
    })
    let model: CommunicationModel = new CommunicationModel();
    model.data = { imageUrls: imgUrls, ImageIndex: imgIndex };
    model.Key = 'imageViewer';
    this.communicationService.broadcast(model);
  }else {
    let path = this.mediaUrl + image.Path;
    window.open(path);
  }
  }
  deleteMedia(mediaId: number) {
    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteMediaFileConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
      let file = this.galleryImages.find(x => x.MediaID == mediaId);
      const deleteMediaReq: MediaDeleteRequestDTO = {
        MediaRelationTypeID: file.MediaRelationTypeID,
        RelationID: file.RelationID,
        MediaRelationshipID: file.MediaRelationshipID
      }
      const medias = this.mediaService.deleteMedia(deleteMediaReq);
      medias.subscribe((result: ApiResponseNull) => {
        if (!result.error) {
          this.refreshAllMedia();
        }
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {

    }
    this.notificationService.CustomDialog(configuration);
  }

  setAsDefault(mediaId: number) {
    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.SetAsDefaultImageConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
      let file = this.galleryImages.find(x => x.MediaID == mediaId);
      const medias = this.mediaService.saveOrUpdateMedia(file, file.MediaID, true);
      medias.subscribe(result => {
        if (!result.error) {
          this.refreshAllMedia();
        } else {
          this.notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSaveMedia);
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {
    }
    this.notificationService.CustomDialog(configuration);
  }

  fileChangeEvent(fileInput: any) {
    this.files = new Array<MediaInfoDTO>();
    if (fileInput.target.files && fileInput.target.files.length) {
      for (let i = 0; i < fileInput.target.files.length; i++) {
        const media = {} as MediaInfoDTO;
        media.File = fileInput.target.files[i];
        if (fileInput.target.files && fileInput.target.files[0]) {

          var reader = new FileReader();
          reader.onload = (event: any) => {
            media.URL = event.target.result;
            var img = new Image;
            img.onload = function () {

              media.Height = img.height;
              media.Width = img.width;
            };
            img.src = reader.result as string;
          }
          reader.readAsDataURL(fileInput.target.files[0]);
        }
        this.files.push(media);
      }
    }
    fileInput.target.value = null;

    let model: CommunicationModel = new CommunicationModel();

    model.data = { initialDetails: this.initialDetails, files: this.files };
    model.Key = 'showFileUploadModal';
    this.communicationService.broadcast(model);
  }

  refreshAllMedia() {
    const medias = this.mediaService.getAllMedias(this.initialDetails.propertyId);
    medias.subscribe((result: ApiResponseListMediaDTO) => {
      if (!result.error) {
        this.galleryImages = result.responseData;
        this.galleryImages.forEach(x => {
          if(x.Ext.toLowerCase() == FileTypes.PNG || x.Ext.toLowerCase() == FileTypes.JPG){
          x.URL = this.mediaUrl + environment.MediaS3ThumbnailPath + "/" + environment.MediaS3ThumbResolution  +"/"+ x.Path;
        }
        else if(x.Ext.toLowerCase() == FileTypes.PDF) {
          x.URL = 'assets/images/pdf.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.TEXT) {
          x.URL = 'assets/images/text.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.WORD) {
          x.URL = 'assets/images/word.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.EXCEL) {
          x.URL = 'assets/images/excel.png'
        }
        else if(x.Ext.toLowerCase() == FileTypes.CSV) {
          x.URL = 'assets/images/csv.png'
        }
      }
      );
         this._sharedDataService.propertyMedia =    this.galleryImages;
         this._sharedDataService.propertyMedia.forEach(element => {
           element.PropertyID = this.initialDetails.propertyId;
         });
      } else {
        this.notificationService.ShowErrorMessage(result.message)
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToGetMedias);
    });
  }

  ngOnInit() {
      if(!!this._sharedDataService.propertyMedia && this._sharedDataService.propertyMedia.length > 0 && this._sharedDataService.propertyMedia[0].PropertyID == this.initialDetails.propertyId) {
       this.galleryImages = this._sharedDataService.propertyMedia;
      } else {
        this.refreshAllMedia();
      }   

   
  }

  ngOnDestroy() {
    // prevent memory leak when component destroyed
    this.eventSubscription.unsubscribe();
    this.fileuploaded.unsubscribe();
  }
}
