import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-multi-select-dropdown',
  templateUrl: './multi-select-dropdown.component.html',
  styleUrls: ['./multi-select-dropdown.component.css']
})
export class MultiSelectDropdownComponent {
  @Input() list: any[];
  @Input() checkedList: any[];
  @Input () isEditPropertyOpen:boolean;
  @Output() shareCheckedList = new EventEmitter();
  currentSelected: {};
  showDropdown = false;

  constructor() {}

  getSelectedValue(status: Boolean, id: string) {
    if (status) {
      this.checkedList.push(id);
    } else {
      var index = this.checkedList.indexOf(id);
      this.checkedList.splice(index, 1);
    }

    this.currentSelected = { checked: status, id };
    this.shareCheckedlist();
  }

  shareCheckedlist() {
    this.shareCheckedList.emit(this.checkedList);
  }
  setShowDropdown(value) {
    this.showDropdown = value;
  }
}
