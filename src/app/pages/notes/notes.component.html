<div class="page-head">
  <!-- <h4>Notes @ABC Real Estate (Company Level)</h4> -->
  <div class="row" style="margin-right: 0px;">
    <div class="col-md-12">
      <div class="row" style="padding: 5px 15px 5px 15px; background-color: white">
        <div class="col-md-4" *ngFor="let note of notesList" style="padding: 10px;" data-testId="notes-number">
          <div class="notes-cards card" data-testId="notes-card">
            <div class="card-body p-2">
              <h4>{{note.NoteTitle}} </h4>
              <p>
                {{note.NoteDescription}}
              </p>
            </div>
            <div class="card-footer p-2">
              <div class="row">
                <div class="col-md-12">
                  <p><span class="dataKey"> Created on</span> <span class="dataLabelValue">{{note.CreatedDate | date: 'dd/MM/yyyy'}} </span></p>
                  <p><span class="dataKey">By</span> <span class="dataLabelValue">{{note.CreatedByName}}</span> </p>
                </div>
                <div class="icon-wrapper-notes col-md-12">
                  <div class="pull-right" style="text-align: right">
                    <i class="far fa-edit" (click)="editNote(note)" data-testId="note-edit"></i>
                    <i class="fa fa-trash cursor-pointer" aria-hidden="true" data-testId="note-delete" (click)="deleteNote(note)"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="buttons loader-btn add-note-link" data-testId="notes-add">
            <a (click)="addNote()"><img src="assets/images/add_blue.png" height="50" width="50"> </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--***********************Note-Add-modal ************************* -->