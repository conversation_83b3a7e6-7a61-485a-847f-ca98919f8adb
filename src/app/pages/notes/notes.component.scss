
.card-footer p {
    margin: 0;
}
.notes-cards .card-footer {
    background: #f0f3f5;
}
.notes-cards .card-body h4{
   color: var(--primary-brightBlue);
}
button.btn-transparant {
    background: transparent;
    color: #fff;
    border: 0;
    text-align: center;
    -webkit-appearance: none;
}
.notes-cards.card {
    margin-bottom: 0;   
}
.notes-cards .card-body p {
    display: -webkit-box;
    height: 59.2px;
    margin: 0 auto;
    line-height: 1.4;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.add-note-link{
    background: none;
    border: 2px dashed #ccc;
    width: 100%;
    text-align: center;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    height:100%;
    max-height: 200px;
    margin-top: 10px;
    min-height:200px;
}
.add-note-link a{
    height: 100%;
    display: flex;
}
.add-note-link a img{
    margin: auto;
}
.icon-wrapper-notes button{
    background: #f18a00;
    border: 0;
    color: #fff;
    margin-bottom:10px;
}
.icon-wrapper-notes button i{
    margin: 0;
}
@media (min-width:768px){
    .notes-cards.card {   
        height: 100%;
    }
}

.mat-tab-body-content {
     overflow: none; 
}
.fa-edit{
    margin-right: 4px;
}
