// Angular core imports
import { Component, OnInit, Input, NgZone } from '@angular/core';
// Third-party imports
import { Subscription } from 'rxjs';
// Application-specific enums and constants
import { CommonStrings } from '../../constants';
// Application-specific DTOs and models
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
// Application-specific services
import { LoginService } from '../../services/login.service';
import { NotesService } from '../../services/notes.service';
import { CommunicationService, CommunicationModel } from '../../services/communication.service';
// Notification models and services
import { confirmConfiguration } from '../../modules/notification/models/confirmConfiguration';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { ApiResponseListNotesResponseDTO, ApiResponseNull, NotesResponseDTO } from '../../api-client';

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss']
})
export class NotesComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  @Input() selectedNote: NotesResponseDTO;
  note: NotesResponseDTO;
  notesList: NotesResponseDTO[] = [];
  ParentTableID = NotesResponseDTO.ParentTableIdEnum.Property;

  showNoteModal: boolean = false;
  eventSubscription: Subscription;

  constructor(private _notesService: NotesService
    , private _loginService: LoginService
    ,  private communicationService: CommunicationService
    , private notificationService: NotificationService
    , private zone: NgZone) {
      this.eventSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
        this.zone.run(() => {
          this.initialDetails = result.data;
          this.note.ParentId = this.initialDetails.propertyId;
          this.note.ParentTableId  = this.ParentTableID;
          this.getNotes();
        });
      });
      this.communicationService.subscribe('onNoteClose').subscribe(result => {
        this.getNotes();
      });
    }

  ngOnInit() {
    this.note = {} as NotesResponseDTO;
    this.note.ParentId = this.initialDetails.propertyId;
    this.note.ParentTableId  = this.ParentTableID;
    this.getNotes();
  }

  getNotes() {
    if (this.note.ParentId) {
      const response_notes = this._notesService.GetNotes(this.note.ParentId, NotesResponseDTO.ParentTableIdEnum.Property);
      response_notes.subscribe((result: ApiResponseListNotesResponseDTO) => {
        if (!result.error) {
          this.notesList = result.responseData;
        } else {
          this.notificationService.ShowErrorMessage(result.error);
        }
      }, error => {
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToGetNotes);
      });
    } else {
      this.notesList = [];
    }
  }

  addNote() {
    this.selectedNote = {} as NotesResponseDTO;
    this.selectedNote.ParentId = this.note.ParentId;
    this.selectedNote.ParentTableId = this.note.ParentTableId;
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, selectedNote:  this.selectedNote };
    model.Key = 'showNoteModal';
    this.communicationService.broadcast(model);
  }

  editNote(note: NotesResponseDTO) {
    this.selectedNote = JSON.parse(JSON.stringify(note));
    let model: CommunicationModel = new CommunicationModel();
    model.data = { initialDetails: this.initialDetails, selectedNote:  this.selectedNote };
    model.Key = 'showNoteModal';
    this.communicationService.broadcast(model);
  }

  deleteNote(note: NotesResponseDTO) {
    let configuration: confirmConfiguration = new confirmConfiguration();
    configuration.Message = CommonStrings.DialogConfigurations.Messages.DeleteNoteConfirmationMessage;
    configuration.Title = CommonStrings.DialogConfigurations.Title.Arealytics;
    configuration.OkButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Yes;
    configuration.OkButton.Callback = () => {
      const response_deleteNote = this._notesService.DeleteNote(note.NoteId);
      response_deleteNote.subscribe((result: ApiResponseNull) => {
        if (!result.error) {
          this.getNotes();
        } else {
          this.notificationService.ShowErrorMessage(result.message);
        }
      }, error => {
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToDeleteNote);
      });
    };
    configuration.CancelButton.Label = CommonStrings.DialogConfigurations.ButtonLabels.Cancel;
    configuration.CancelButton.Callback = () => {
    }
    this.notificationService.CustomDialog(configuration);
  }

  closeNoteModal() {
    this.getNotes();
  }
}
