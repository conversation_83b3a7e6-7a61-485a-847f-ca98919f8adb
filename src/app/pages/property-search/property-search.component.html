<app-header [hideModeLabel]="true" [showAdvancedSearchLabel]="false"></app-header>
<div class="card-body" (keyup.enter)="handleKeyboardEvent($event)" *ngIf="searchPropertyFields">
  <div class="form-group row header">
    <div class="col-md-2"></div>
    <div class="col-md-3 page_sub_head1">
      <label class="col-form-label form-heading-label"><b>Property Search</b></label>
    </div>
    <div class="col-md-2 col gap-2 d-flex">
      <button (click)="navigateToMapSearch('false')" data-testid="add-property" class="btn btn-primary">Add Property</button>
      <button (click)="navigateToMapSearch()" data-testid="map-search" class="btn btn-primary">Map Search</button>
    </div>
    <div class="col-md-2"></div>
  </div>
  <div class="form-content">
    <div class="row">
      <label class="col-md-2 col-form-label">PID</label>
      <div class="col-md-3">
        <input class="form-control ng-pristine ng-valid ng-touched" [(ngModel)]="searchCriteria.Pids" data-testId="search-propertyid"
          placeholder="Property ID" type="text">
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Property Use</label>
      <div class="col-md-10" id="chklistPropertyType">
        <label class="checkbox-inline label-margin" for="inline-checkbox3" *ngFor="let type of propertyTypes">
          <label class="lbl-pname">
            <input type="checkbox" class="checkbox-inline" [(ngModel)]="type.IsSelected" data-testId="search-property-use"
              id="checkboxForInput{{type.UseTypeName}}" (change)="showPropTypeCheckButton(type.UseTypeID)" />
            {{type.UseTypeName}}</label>
        </label>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Street Number</label>
      <div class="col-md-3" id="chklistStreetNo">
        <div class="row">
          <div class="col">
            <input class="form-control ng-pristine ng-valid ng-touched" [(ngModel)]="searchCriteria.StreetMin" data-testId="search-street-min"
              placeholder="Min" type="text">
          </div>
          <div class="col">
            <input class="form-control ng-pristine ng-valid ng-touched" [(ngModel)]="searchCriteria.StreetMax" data-testId="search-street-max"
              placeholder="Max" type="text">
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Street Name</label>
      <div class="col-md-3">
        <input class="form-control ng-pristine ng-valid ng-touched" [(ngModel)]="searchCriteria.StreetName" data-testId="search-street-name"
          placeholder="Street Name" type="text">
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Postal Code</label>
      <div class="col-md-3">
        <input class="form-control ng-pristine ng-valid ng-touched" [(ngModel)]="searchCriteria.Zipcode" data-testId="search-postal-code"
          placeholder="Postal Code" type="text">
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">State</label>
      <div class="col-md-3">
        <ng-select [items]="states" [virtualScroll]="true" bindLabel="StateName" bindValue="StateID" data-testId="search-state"
          placeholder="--Select--" [(ngModel)]="searchCriteria.StateID"
          (change)="GetCitiesByState($event)" (clear)="clearState()" labelForId="State"
          class="select-style">
        </ng-select>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">City</label>
      <div class="col-md-3">
        <ng-select [items]="cities" [virtualScroll]="true" bindLabel="CityName" [multiple]="true" bindValue="CityID" data-testId="search-city"
          placeholder="--Select--" [(ngModel)]="searchCriteria.CityIds"
          class="select-style"></ng-select>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Research Status</label>
      <div class="col-md-3">
        <ng-select [items]="researchTypes" [virtualScroll]="true" bindLabel="PropertyResearchTypeName" [multiple]="true"
          bindValue="PropertyResearchTypeID" placeholder="--Select--" [(ngModel)]="searchCriteria.ResearchStatusIds" data-testId="search-research-status"
          labelForId="researchType" class="select-style"></ng-select>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Without Footprint</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" [(ngModel)]="searchCriteria.HasNoBuildingFootprints" data-testId="search-without-footprint" />
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Without Parcel</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" [(ngModel)]="searchCriteria.HasNoExistingParcelInTileLayer" data-testId="search-without-parcel" />
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Not Strata Only</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" data-testid="notStrata-checkbox" [(ngModel)]="searchCriteria.NotStrata" data-testId="search-not-strata" />
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Skipped</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" [(ngModel)]="searchCriteria.IsSkipped" data-testId="search-skipped"
          (change)="showSkipCheckButton($event)" />
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Exclude Hidden</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" [(ngModel)]="searchCriteria.ExcludeHidden" data-testId="search-exclude-hidden"
          (change)="onExcludeHiddenChecked($event)" />
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Last Modified By</label>
      <div class="col-md-3">
        <ng-select [items]="researchersList" [virtualScroll]="true" bindLabel="Fullname" [multiple]="true" data-testId="search-modified-by"
          bindValue="EntityID" placeholder="--Select--" [(ngModel)]="searchCriteria.ResearcherIds"
          labelForId="researcher" class="select-style"></ng-select>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Audit Status</label>
      <div class="col-md-3">
        <ng-select [items]="auditStatusList" [virtualScroll]="true" bindLabel="StatusName" [multiple]="true"
          bindValue="StatusDefinationID" placeholder="--Select--" [(ngModel)]="searchCriteria.AuditStatus" data-testId="search-audit-status"
          labelForId="researchType" class="select-style"></ng-select>
      </div>
    </div>
    <div class="row">
      <label class="col-md-2 col-form-label">Not Reviewed</label>
      <div class="skip-checkbox">
        <input type="checkbox" class="checkbox-inline" [(ngModel)]="searchCriteria.IsNotReviewed" data-testId="search-not-reviewed"
          (change)="onIsReviewedChacked($event)" />
      </div>
    </div>
    <div class="row">
      <div class="col-md-2">
        <label class="col-form-label">Last Reviewed </label>
      </div>
      <div class="col-md-3">
        <div class="row">
          <div class="col-md-6">
            <div class="input-with-icon">
              <input class="form-control reviewDate" [ngClass]="{'readonly-input': !searchCriteria.IsNotReviewed}"
                placeholder="Min Date" angular-mydatepicker
                (click)="!searchCriteria.IsNotReviewed && primaryDp.toggleCalendar()"
                [(ngModel)]="searchCriteria.LastReviewedDateMinFormatted" [options]="myDpOptions"
                #primaryDp="angular-mydatepicker" (ngModelChange)="onDateChange()" readonly />
              <i class="far fa-calendar-alt" aria-hidden="true" data-testId="search-reviewed-min-date"
                (click)="!searchCriteria.IsNotReviewed && primaryDp.toggleCalendar()"></i>
              <i *ngIf="searchCriteria.LastReviewedDateMinFormatted" class="fa fa-close clear-icon" aria-hidden="true"
                (click)="clearDate('min')"></i>
            </div>
          </div>
          <div class="col-md-6">
            <div class="input-with-icon">
              <input class="form-control readonly-input reviewDate" [ngClass]="{'readonly-input': !searchCriteria.IsNotReviewed}"
                placeholder="Max Date" angular-mydatepicker
                (click)="!searchCriteria.IsNotReviewed && secondaryDp.toggleCalendar()"
                [(ngModel)]="searchCriteria.LastReviewedDateMaxFormatted" [options]="myDpOptions"
                #secondaryDp="angular-mydatepicker" (ngModelChange)="onDateChange()" readonly />
              <i class="far fa-calendar-alt" aria-hidden="true" data-testId="search-reviewed-max-date"
                (click)="!searchCriteria.IsNotReviewed && secondaryDp.toggleCalendar()"></i>
              <i *ngIf="searchCriteria.LastReviewedDateMaxFormatted" class="fa fa-close clear-icon" aria-hidden="true"
                (click)="clearDate('max')"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="reviewedDateError" class="row">
      <div class="col-md-2 col-form-label"></div>
      <div class="col-md-3 error-message date-error-msg">
        Min Date cannot be greater than Max Date
      </div>
    </div>
    <div class="action-btns">
      <button type="button" class="btn btn-primary mr-2" (click)="onSearchResetClick()" data-testId="search-reset-button">Reset</button>
      <button type="button" class="btn search-btn" (click)='onSearchClick()' [disabled]="reviewedDateError" data-testId="search-search-button"><i class="fa fa-search mr-2"></i>
        Search</button>
    </div>
  </div>
</div>
<div *ngIf="searchPropertyTable">
  <div class="col-md-12 mb-3 mt-2">
    <button type="button" class="btn btn-primary  pull-left  mr-3" (click)='backToSearch()'><i class="fa fa-arrow-left"
        aria-hidden="true"></i>
      <span class="d-sm-none d-md-inline-block">Back to Search</span></button>
    <span class="mob-full-width"  [style.visibility]="propertyLoading ? 'hidden' : 'visible'"><b>{{propertyCount}} Results Found</b></span>
    <div class="btn-group right pull-right mr-2">
      <div class="dropdown">
        <button type="button" class="btn btn-primary float-right" data-toggle="dropdown" aria-haspopup="true"
          aria-expanded="false" (click)="openMapModel()">Map</button>

      </div>
    </div>

  </div>

  <div class="col-md-12 mb-2 mt-4 row">
    <div class="col-md-1">
      <select class="form-control" [(ngModel)]="searchCriteria.FrontEndOffsetValue" (change)="onOffsetChange()">
        <option value="150">150</option>
        <option value="100">100</option>
        <option value="50">50</option>
        <option value="25">25</option>
        <option value="10">10</option>
      </select>
    </div>

    <div class="col-md-6  no-padding" *ngIf="!propertyLoading">
      <ul *ngIf="propertypager.pages && propertypager.pages.length" class="pagination">
        <li [ngClass]="{disabled:propertypager.currentPage === 1}">
          <a (click)="propertypager.currentPage !== 1 && setPropertyPage(1)">First</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === 1}">
          <a (click)="propertypager.currentPage !== 1 && setPropertyPage(propertypager.currentPage - 1)">Previous</a>
        </li>
        <li *ngFor="let page of propertypager.pages" [ngClass]="{active:propertypager.currentPage === page}">
          <a (click)="setPropertyPage(page)">{{page}}</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === propertypager.totalPages}">
          <a (click)="propertypager.currentPage !== propertypager.totalPages && setPropertyPage(propertypager.currentPage + 1)" data-testId="search-previous-button">Next</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === propertypager.totalPages}">
          <a (click)="propertypager.currentPage !== propertypager.totalPages && setPropertyPage(propertypager.totalPages)" data-testId="search-last-button">Last</a>
        </li>
      </ul>
    </div>
  </div>

  <div class="mx-auto col-md-12 navigation-preference">
  <div class="navigation-prefernce-text">Navigation Preference
    <i class="fa fa-info-circle info-icon" aria-hidden="true" title="Choose whether the 'Save and Next' button takes you to the next unvisited property or the next untouched property”"></i>
  </div>
    <ng-container *ngFor="let navigationType of navigationPreferences" class="col-md-12 navigation-preference">
    <label title="{{navigationType.name}}" class="navigation-type">
      <input class="navigation-type-button" type="radio" name="navigationType.name"
        [value]="navigationType.name" [(ngModel)]="selectedNavigationType"
        [ngModelOptions]="{ standalone : true }"
        (change)="onNavigationPreferenceChange(navigationType.name)">
      {{navigationType.name}}
    </label>
    </ng-container>
  </div>

  <div class="col-md-12 mb-3 mt-1">
    <div class="table-scroll table-responsive table-height">
      <p-table #propertyTable [columns]="propertyHeader" [value]="currentPagePropertyList" [scrollable]="true" [scrollHeight]="!propertyLoading ? getScrollHeight() : '40vh'"
        [totalRecords]="totalRecords" [loading]="propertyLoading" data-testId="search-table-result">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th [style.max-width]="'50px'" class="checkbox-wrapper"><input (change)="selectAll($event)" type="checkbox" [(ngModel)]="IsSelectAll">
            </th>
            <th *ngFor="let col of columns" [style.max-width]="getColumnWidth(col.header)">
              <div class="header-row">
                <div class="header-row-content">
                  {{col.header}}
                </div>
                <div class="arrow-group">
                  <i class="fa fa-sort-up" (click)="sortProperty(col.field,'Ascending')"></i>
                  <i class="fa fa-sort-down" (click)="sortProperty(col.field,'Descending')"></i>
                </div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-index="rowIndex" let-columns="columns">
          <tr [ngClass]="getClassName(index)">
            <td [style.max-width]="'50px'" class="checkbox-wrapper">
              <input type="checkbox" [(ngModel)]="rowData.IsSelected">
            </td>
            <td [style.max-width]="'80px'">
              <a class="record-number" (click)="onPropertyClick(rowData)">
                {{ getPropertyRecordDisplayNumber(index)}}</a>
            </td>
            <ng-container *ngFor="let col of columns">
              <td *ngIf="col.header !=='Number' && col.header !=='Skip' && col.header !== 'Property ID' && col.header !=='Record Type'" [style.max-width]="getColumnWidth(col.header)">
                {{ col.valueGetter ? col.valueGetter(rowData) : rowData[col.field] }}
              </td>
              <td *ngIf="col.header === 'Property ID'"  [style.max-width]="getColumnWidth(col.header)">
                <a class="record-number" (click)="onPropertyClick(rowData)">

                  {{rowData[col.field]}}</a>
                <span *ngIf="isPropertyVisited(rowData.PropertyID) && !isPropertyEdited(rowData.PropertyID)"> <i
                    class="fas fa-check-circle fa-check-circle-grey" title="Visited"></i></span>
                <span *ngIf="isPropertyEdited(rowData.PropertyID)"> <i class="fas fa-check-circle" title="Edited"></i></span>
              </td>
              <td *ngIf="col.header ==='Skip'"  [style.max-width]="getColumnWidth(col.header)">
                {{ rowData[col.field] === 1 ? true : '' }}
              </td>
              <td *ngIf="col.header ==='Record Type'"  [style.max-width]="getColumnWidth(col.header)">
                {{getCondoType(rowData[col.field])}}
              </td>
            </ng-container>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage" let-columns="columns">
          <tr  [style.height.px]="propertyLoading ? 200 : null">
            <td [attr.colspan]="columns.length + 1" *ngIf="!propertyLoading">No records found</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>

  <div class="col-md-12 mb-2 row">
    <div class="col-md-1">
      <select class="form-control" [(ngModel)]="searchCriteria.FrontEndOffsetValue" (change)="onOffsetChange()">
        <option value="150">150</option>
        <option value="100">100</option>
        <option value="50">50</option>
        <option value="25">25</option>
        <option value="10">10</option>
        <option value="5">5</option>
      </select>
    </div>

    <div class="col-md-6  no-padding">
      <ul *ngIf="!propertyLoading && propertypager.pages && propertypager.pages.length" class="pagination">
        <li [ngClass]="{disabled:propertypager.currentPage === 1}">
          <a (click)="propertypager.currentPage !== 1 && setPropertyPage(1)">First</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === 1}">
          <a (click)="propertypager.currentPage !== 1 && setPropertyPage(propertypager.currentPage - 1)">Previous</a>
        </li>
        <li *ngFor="let page of propertypager.pages" [ngClass]="{active:propertypager.currentPage === page}">
          <a (click)="setPropertyPage(page)">{{page}}</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === propertypager.totalPages}">
          <a (click)="propertypager.currentPage !== propertypager.totalPages && setPropertyPage(propertypager.currentPage + 1)">Next</a>
        </li>
        <li [ngClass]="{disabled:propertypager.currentPage === propertypager.totalPages}">
          <a (click)="propertypager.currentPage !== propertypager.totalPages && setPropertyPage(propertypager.totalPages)">Last</a>
        </li>
      </ul>
    </div>

    <div class="col-md-5">
      <button type="button" class="btn btn-primary float-right" aria-haspopup="true" aria-expanded="false"
        (click)="defaultSortOrder()">Reset Sort Order</button>
    </div>
  </div>
  <div *ngIf="showMapModal">
    <imperium-modal [(visible)]="showMapModal" [title]="'Map View'" [width]="'xl-medium'"
      [bodyTemplate]="bodyTemplate">
      <ng-template #bodyTemplate>
        <app-property-map-utility-modal (onClose)="closeMapModal()" [propertyList]="currentPagePropertyList">
        </app-property-map-utility-modal>
      </ng-template>
    </imperium-modal>
  </div>
</div>
