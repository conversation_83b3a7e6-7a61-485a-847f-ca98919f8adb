import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PropertySearchComponent } from './property-search.component';
import { PropertySearchList } from '../../models/PropertySearch';

describe('PropertySearchComponent - Sorting', () => {
  let component: PropertySearchComponent;
  let fixture: ComponentFixture<PropertySearchComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PropertySearchComponent],
      // Add necessary imports and providers here
    }).compileComponents();

    fixture = TestBed.createComponent(PropertySearchComponent);
    component = fixture.componentInstance;
  });

  it('should sort properties by PropertyID in ascending order', () => {
    // Mock data
    const mockProperties: PropertySearchList[] = [
      { PropertyID: 3, CityName: 'City A' } as PropertySearchList,
      { PropertyID: 1, CityName: 'City B' } as PropertySearchList,
      { PropertyID: 2, CityName: 'City C' } as PropertySearchList,
    ];

    component.propertyListCopy = mockProperties;
    component.sortProperty('PropertyID', 'Ascending');

    expect(component.propertyList[0].PropertyID).toBe(1);
    expect(component.propertyList[1].PropertyID).toBe(2);
    expect(component.propertyList[2].PropertyID).toBe(3);
  });

  it('should sort properties by PropertyID in descending order', () => {
    // Mock data
    const mockProperties: PropertySearchList[] = [
      { PropertyID: 1, CityName: 'City A' } as PropertySearchList,
      { PropertyID: 3, CityName: 'City B' } as PropertySearchList,
      { PropertyID: 2, CityName: 'City C' } as PropertySearchList,
    ];

    component.propertyListCopy = mockProperties;
    component.sortProperty('PropertyID', 'Descending');

    expect(component.propertyList[0].PropertyID).toBe(3);
    expect(component.propertyList[1].PropertyID).toBe(2);
    expect(component.propertyList[2].PropertyID).toBe(1);
  });

  it('should sort properties by CityName in ascending order', () => {
    // Mock data
    const mockProperties: PropertySearchList[] = [
      { PropertyID: 1, CityName: 'City C' } as PropertySearchList,
      { PropertyID: 2, CityName: 'City A' } as PropertySearchList,
      { PropertyID: 3, CityName: 'City B' } as PropertySearchList,
    ];

    component.propertyListCopy = mockProperties;
    component.sortProperty('CityName', 'Ascending');

    expect(component.propertyList[0].CityName).toBe('City A');
    expect(component.propertyList[1].CityName).toBe('City B');
    expect(component.propertyList[2].CityName).toBe('City C');
  });

  it('should handle null values in sorting', () => {
    // Mock data with null values
    const mockProperties: PropertySearchList[] = [
      { PropertyID: 1, CityName: 'City A' } as PropertySearchList,
      { PropertyID: 2, CityName: null } as PropertySearchList,
      { PropertyID: 3, CityName: 'City B' } as PropertySearchList,
    ];

    component.propertyListCopy = mockProperties;
    component.sortProperty('CityName', 'Ascending');

    // Null values should be sorted to the end
    expect(component.propertyList[0].CityName).toBe('City A');
    expect(component.propertyList[1].CityName).toBe('City B');
    expect(component.propertyList[2].CityName).toBe(null);
  });

  it('should not sort when field is invalid', () => {
    const mockProperties: PropertySearchList[] = [
      { PropertyID: 3, CityName: 'City A' } as PropertySearchList,
      { PropertyID: 1, CityName: 'City B' } as PropertySearchList,
      { PropertyID: 2, CityName: 'City C' } as PropertySearchList,
    ];

    component.propertyListCopy = mockProperties;
    const originalOrder = [...mockProperties];
    
    component.sortProperty('', 'Ascending'); // Empty field
    component.sortProperty('Number', 'Ascending'); // Invalid field

    // Order should remain unchanged
    expect(component.propertyList).toBeUndefined();
  });
});
