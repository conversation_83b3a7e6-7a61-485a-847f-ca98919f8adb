// Angular core imports
import { <PERSON><PERSON><PERSON>, OnInit, HostListener, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
// Third-party imports
import { Table } from 'primeng/table';
import { IAngularMyDpOptions } from 'angular-mydatepicker';
// Environment
import { environment } from '../../../environments/environment';
// Application-specific services
import { LoginService } from '../../services/login.service';
import { SharedDataService } from '../../services/shareddata.service';
import { PropertyService } from '../../services/api-property.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { IMetaData, MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { PagerService } from '../../services/pager.service';
import { PropertyTrackingService } from '../../services/property-tracking.service';
import { LookupService } from '../../services/lookup.service';
// Application-specific enums
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { MetaDataCollectionKeys } from '../../enumerations/indexeddb';
import { NavigationPreferences } from '../../enumerations/searchGridNavigationTypes';
import { UserRoles } from '../../enumerations/userRoles';
import { CondoTypeIDEnum } from '../../enumerations/condoType';
// Application-specific models
import { PropertySortCriteria } from '../../models/PropertySortCriteria';
import { PropertySearchResult } from '../../models/PropertySearchResult';
import { PropertySearchDTO, PropertySearchList } from '../../models/PropertySearch';
// Application-specific constants and utils
import { CommonStrings, DefaultFEOffset, DefaultOffset, DefaultStartingIndex, FrontEndStartingIndex, StringValuedSortParams } from '../../constants';
import { clearSessionStorage, dynamicAlphaNumericSort, dynamicIntegerSort, dynamicSort } from '../../utils';
import { ApiResponseListUsersDTO, ApiResponsePropertySearchResponseDTO, PropertySearchRequestDTO, PropertySearchResponseBaseDTO } from '../../api-client';

const CryptoJS = require('crypto-js');

@Component({
  selector: 'app-property-search',
  templateUrl: './property-search.component.html',
  styleUrls: ['./property-search.component.css']
})
export class PropertySearchComponent implements OnInit {
  @ViewChild('propertyTable') dataTable: Table;
  navigationPreferences = [
    { name: NavigationPreferences.UnVisited },
    { name: NavigationPreferences.UnTouched }
  ];
  reviewedDateError: boolean = false;
  selectedNavigationType: string;
  _propertyService: PropertyService;
  searchCriteria: PropertySearchDTO;
  sortCriteria: PropertySortCriteria;
  public states: any[] = [];
  CountryId: number;
  public cities: any[] = [];
  tmpSearchCriteria: PropertySearchDTO;
  public propertyTypes: any;
  public isPropertyTypeSelected = false;
  searchPropertyFields = true;
  searchPropertyTable = false;
  public propertyList: Array<PropertySearchList>;
  public propertyListCopy: Array<PropertySearchList>;
  propertyCount = 0;
  propertypager: any = {};
  public propertyLoading = false;
  IsSearchInProgres = false;
  currentPage = 1;
  public researchTypes: any[] = [];
  propertyLookup: any;
  condoTypes: any;
  totalRecords: number;
  IsSelectAll: boolean = false;
  propertyHeader: any;
  showMapModal: boolean = false;
  visitedPropertyIds: any = [];
  editedPropertyIds: any = [];
  currentPagePropertyList: PropertySearchList[];
  SearchResultsString: string;
  researchersList: any[] = [];
  auditStatusList: any[] = [];
  metaDataIndexedDBService: MetaDataIndexedDBService;
  myDpOptions: IAngularMyDpOptions = {
    dateRange: false,
  };

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (event.keyCode === 13) {
      this.onSearchClick();
    }
  }

  constructor(private loginService: LoginService,
    private _sharedDataService: SharedDataService,
    propertyService: PropertyService,
    private router: Router,
    private pagerService: PagerService,
    private _notificationService: NotificationService,
    private propertyTrackingService: PropertyTrackingService,
    private lookupService: LookupService
  ) {
    this.searchCriteria = {} as PropertySearchDTO;
    this.sortCriteria = new PropertySortCriteria();
    this._propertyService = propertyService;
    const loginData = sessionStorage.getItem(SessionStorageKeys.LogInData);
    if (loginData !== '' && !!loginData) {
      const bytes = CryptoJS.AES.decrypt(loginData.toString(), environment.EncryptionKey);
      const loggedinData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
      if (loggedinData) {
        this.CountryId = loggedinData.CountryId;
      }
    }
  }

  initializeSearchForm() {
    if (sessionStorage.getItem(SessionStorageKeys.SearchCriteria)) {
      this.restoreSearchCriteriaFromCache();
    } else {
      this.searchCriteria  = {} as PropertySearchDTO;
      this.searchCriteria.ListingType = 'all';
      this.searchCriteria.ExcludeHidden = true;
      this.searchCriteria.OffsetValue = 500;
      this.searchCriteria.StartingIndex = 1;
      this.searchCriteria.FrontEndStartingIndex = 1;
      this.searchCriteria.FrontEndOffsetValue = 150;
      this.currentPage = 1;
    }
  }
  ngOnInit() {
    this.propertyHeader = [
      { field: '', header: 'Number' },
      { field: 'PropertyID', header: 'Property ID' },
      { field: 'Address.ZipCode', header: 'Zip Code', valueGetter: (rowData) => rowData?.Address?.ZipCode ?? '' },
      { field: 'Address.StreetNumberMin', header: 'Street Number Min', valueGetter: (rowData) => rowData?.Address?.StreetNumberMin ?? '' },
      { field: 'Address.StreetNumberMax', header: 'Street Number Max', valueGetter: (rowData) => rowData?.Address?.StreetNumberMax ?? '' },
      { field: 'Address.AddressStreetName', header: 'Street Name', valueGetter: (rowData) => rowData?.Address?.AddressStreetName ?? '' },
      { field: 'CityName', header: 'City' },
      { field: 'UseTypeName', header: 'General Use' },
      { field: 'SpecificUseName', header: 'Specific Use' },
      { field: 'ResearchTypeName', header: 'Status', valueGetter: (rowData) => rowData?.AllPropertiesDetails?.ResearchTypeName ?? rowData?.ResearchTypeName ?? '' },
      { field: 'CondoTypeID', header: 'Record Type' },
      { field: 'AllPropertiesDetails.CondoUnit', header: 'Unit', valueGetter: (rowData) => rowData?.AllPropertiesDetails?.CondoUnit ?? '' },
      { field: 'IsSkipped', header: 'Skip' },
      { field: 'Comments', header: 'Comments' },
      { field: 'AuditStatus', header: 'Audit Status' }
    ];
    this.selectedNavigationType = JSON.parse(sessionStorage.getItem(SessionStorageKeys.NavigationPreference)) || NavigationPreferences.UnVisited;
    const today = new Date();
    const nextDay = new Date(today);
    nextDay.setDate(today.getDate() + 1);
    this.myDpOptions.dateFormat = this.loginService?.UserInfo?.DateFormat?.toLowerCase() || 'dd/mm/yyyy';
    this.myDpOptions.disableSince = { year: nextDay?.getFullYear(), month: nextDay?.getMonth() + 1, day: nextDay?.getDate() };
  }

  ngAfterViewInit() {
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.getVisitedAndEditedProperties();
    this.getResearchersFromMetaData();
    this.getPropertyLookup();
    const auditStatusUpdated = sessionStorage.getItem(SessionStorageKeys.FetchProperties);
    if (auditStatusUpdated == 'true') {
      const SearchCriteriaFromSs = sessionStorage.getItem(SessionStorageKeys.SearchCriteria);
      this.searchCriteria = JSON.parse(SearchCriteriaFromSs);
      if (this.searchCriteria) {
        this.getResultFromDB();
      }
      sessionStorage.removeItem(SessionStorageKeys.FetchProperties)
    } else {
      this.getPropertyListFromMetaData();
    }
  }

  async getPropertyLookup() {
    try {
      this.propertyLookup = (await this.propertyTrackingService.getPropertyLookup()) ?? [];
      this.getStateList();
      this.getPropertyTypes();
      this.getResearchStatuses();
      this.getAuditStatuses();
      this._sharedDataService.setLookupDropdowns(this.propertyLookup);
    } catch (error) {
      this._notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.LookupDataFailureMessage);
      this.propertyLookup = []; // fallback value
    }
  }

  getPropertyTypes() {
    this.propertyTypes = this.propertyLookup['UseTypeID'] || [];
  }

  getResearchersFromMetaData = async() => {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.ResearchersList);
      if (searchData && searchData.value && searchData.value.length > 0) {
        this.researchersList = searchData.value;
      } else {
        this.getResearchers();
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  getAuditStatuses = async () => {
    this.auditStatusList = this.propertyLookup['PropertyAuditStatus'] || [];
  }

   getStateList() {
    this.states = (this.propertyLookup['StateID'] || []).filter(x => x.CountryID === this.CountryId) || [];
    if(this.searchCriteria.StateID){
      this.GetCity(Number(this.searchCriteria.StateID),false);
    }
  }

  async getVisitedAndEditedProperties() {
    this.visitedPropertyIds = (await this.propertyTrackingService.getVisitedPropertyIds()) ?? [];
    this.editedPropertyIds = (await this.propertyTrackingService.getEditedPropertyIds()) ?? [];
  }

  async getPropertyListFromMetaData() {
    try {
      this.searchPropertyFields = false;
      this.searchPropertyTable = true;
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyList);
      this.SearchResultsString = sessionStorage.getItem(SessionStorageKeys.SearchResults);
      const SearchCriteriaFromSs = sessionStorage.getItem(SessionStorageKeys.SearchCriteria);
      this.searchCriteria = JSON.parse(SearchCriteriaFromSs);
      if (searchData && SearchCriteriaFromSs && this.SearchResultsString) {
        this.propertyList = searchData.value.propertyList;
        this.propertyListCopy = searchData.value.propertyListCopy;
        const SearchResultsFromSs = JSON.parse(this.SearchResultsString);
        this.currentPagePropertyList = SearchResultsFromSs.CurrentPagePropertyList;
        this.propertyCount = SearchResultsFromSs.PropertyCount;
        this.propertypager = SearchResultsFromSs.Pager;
        this.currentPage = SearchResultsFromSs.Pager.currentPage;
        if(this.searchCriteria.StateID){
          this.GetCity(Number(this.searchCriteria.StateID),false);
        }
        const lastVistedPropertyId = this.visitedPropertyIds[this.visitedPropertyIds.length - 1];
        const rowIndex = this.currentPagePropertyList.findIndex(property => property.PropertyID === lastVistedPropertyId);
        this.scrollToRow(rowIndex);
      } else {
        this.initializeSearchForm();
        this.searchPropertyFields = true;
        this.searchPropertyTable = false;
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  }

  isPropertyVisited(propertyID) {
    if (this.visitedPropertyIds && this.visitedPropertyIds.length > 0) {
      return this.visitedPropertyIds.includes(propertyID);
    }
  }

  isPropertyEdited(propertyID) {
    if (this.editedPropertyIds && this.editedPropertyIds.length > 0) {
      return this.editedPropertyIds.includes(propertyID);
    }
  }

  clearState() {
    this.searchCriteria.CityIds = null;
    this.cities = [];
  }
  getCondoType(CondoTypeID) {
    const CondoTypeIDEnum = PropertySearchResponseBaseDTO.CondoTypeIDEnum;
    switch (CondoTypeID) {
      case CondoTypeIDEnum.NotStrata:
        return 'Not Strata';
      case CondoTypeIDEnum.Strata:
        return 'Strata';
      case CondoTypeIDEnum.MasterStrataRecord:
        return 'Master Strata';
      case CondoTypeIDEnum.MasterFreehold:
        return 'Master Freehold';
      case CondoTypeIDEnum.ChildFreehold:
        return 'Child Freehold'
    }
  }
  onPropertyClick(clickedProperty: PropertySearchList) {
    const activeGridRowNumber = this.propertyList.findIndex(property => property.PropertyID === clickedProperty.PropertyID);
    const selectedPropertyPos = { Latitude: clickedProperty.Location.Latitude, Longitude: clickedProperty.Location.Longitude };
    const isNavigationFromSearch = true;
    sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    sessionStorage.setItem(SessionStorageKeys.ActiveGridRowNumber, JSON.stringify(activeGridRowNumber));
    sessionStorage.setItem(SessionStorageKeys.IsNavigationFromSearch, JSON.stringify(isNavigationFromSearch));
    sessionStorage.setItem(SessionStorageKeys.PropertyId, JSON.stringify(clickedProperty.PropertyID));
    sessionStorage.setItem(SessionStorageKeys.SelectedPropertyPos, JSON.stringify(selectedPropertyPos));
    sessionStorage.setItem(SessionStorageKeys.SearchResultsPageDetails, JSON.stringify({ CurrentPage: this.currentPage, PageSize: this.searchCriteria.FrontEndOffsetValue }));
    this.visitedPropertyIds.push(clickedProperty.PropertyID);
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.VisitedPropertyIds, value: this.visitedPropertyIds });
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.MultiFloors);
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
    this.router.navigate(['/expressmapsearch']);
  }

  GetCitiesByState (selectedState) {
    this.GetCity(selectedState?.StateID);
  }
  GetCity(selectedState,isSelectedStateChanged:boolean = true) {
    if (isSelectedStateChanged) {
      this.cities = [];
      this.searchCriteria.CityIds = null;
    }
    this.cities = selectedState ? (this.propertyLookup?.['CityID'] || []).filter(city => city.StateID === selectedState) : [];
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.CityList, value: this.cities });
  }

  restoreSearchCriteriaFromCache() {
    this.searchCriteria = JSON.parse(sessionStorage.getItem(SessionStorageKeys.SearchCriteria));
    this.searchCriteria.ListingType = 'all';
    this.searchCriteria.FrontEndStartingIndex = FrontEndStartingIndex;
    this.searchCriteria.FrontEndOffsetValue = DefaultFEOffset;
    this.searchCriteria.OffsetValue = DefaultOffset;
    this.searchCriteria.StartingIndex = DefaultStartingIndex;
    this.currentPage = 1;
    if (this.searchCriteria.StateID) {
      this.searchCriteria.StateID = Number(this.searchCriteria.StateID);
      this.GetCity(Number(this.searchCriteria.StateID), false);
    }
    if (this.propertyTypes && this.searchCriteria.PropertyTypes) {
      this.searchCriteria.PropertyTypes.forEach(type => {
        this.propertyTypes.find(x => x.UseTypeID === type).IsSelected = true;
      });
      this.showPropTypeCheckButton(0);
    }
  }

  showPropTypeCheckButton(propertyTypeId) {
    let selectCount = 0;
    this.propertyTypes.forEach(type => {
      if (type.IsSelected) {
        selectCount++;
      }
    });
    if (selectCount > 0) {
      this.isPropertyTypeSelected = true;
    } else {
      this.isPropertyTypeSelected = false;
    }
  }
  showSkipCheckButton(event) {
    this.searchCriteria.IsSkipped = event.target.checked;
  }

  onIsReviewedChacked(event) {
    this.searchCriteria.IsNotReviewed = event.target.checked ? true : false;
    if (this.searchCriteria.IsNotReviewed) {
      this.searchCriteria.LastReviewedDateMax = null;
      this.searchCriteria.LastReviewedDateMaxFormatted = null;
      this.searchCriteria.LastReviewedDateMin = null;
      this.searchCriteria.LastReviewedDateMinFormatted = null;
    }
  }

  onExcludeHiddenChecked(event) {
    this.searchCriteria.ExcludeHidden = event.target.checked;
  }

  onNavigationPreferenceChange(selectedNavigationType) {
    sessionStorage.setItem(SessionStorageKeys.NavigationPreference, JSON.stringify(selectedNavigationType));
  }

  closeMapModal() {
    this.showMapModal = false;
  }

  openMapModel() {
    if (this.currentPagePropertyList.find(property => property.IsSelected === true)) {
      this.showMapModal = true;
    }
    else
      this._notificationService.ShowErrorMessage("No records selected to view on Map");
  }

  stringToArray(strValue) {
    const returnArray = [];
    if (strValue) {
      const array = strValue.split(',');
      array.forEach(value => {
        returnArray.push(parseInt(value, 0));
      });
      return returnArray;
    } else {
      return null;
    }
  }

  onSearchResetClick() {
    this.searchCriteria = {} as PropertySearchDTO;;
    sessionStorage.removeItem(SessionStorageKeys.SearchCriteria);
    sessionStorage.removeItem(SessionStorageKeys.SearchResults);
    this.searchCriteria.ListingType = 'all';
    this.searchCriteria.ExcludeHidden = true;
    this.searchCriteria.StartingIndex = 1;
    if (this.propertyTypes) {
      this.propertyTypes.forEach(type => {
        type.IsSelected = false;
      });
    }
    this.isPropertyTypeSelected = false;
    this.searchPropertyFields = true;
    this.searchPropertyTable = false;
  }

  public onSearchClick() {
    this.propertyList = [];
    this.propertyListCopy = [];
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.PropertyList);
    this.searchCriteria.OffsetValue = 500;
    this.searchCriteria.FrontEndOffsetValue = 150;
    const sortCriteriaFromStorage = sessionStorage.getItem(SessionStorageKeys.SortCriteria);
    if (sortCriteriaFromStorage) {
      this.sortCriteria = JSON.parse(sortCriteriaFromStorage);
    } else {
      this.sortCriteria.SortParam = 'defaultSort';
      this.sortCriteria.SortOrder = '';
      sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
    }
    this.searchCriteria.LastReviewedDateMax = this.searchCriteria?.LastReviewedDateMaxFormatted ? this.searchCriteria?.LastReviewedDateMaxFormatted?.singleDate?.formatted : null;
    this.searchCriteria.LastReviewedDateMin = this.searchCriteria?.LastReviewedDateMinFormatted ? this.searchCriteria?.LastReviewedDateMinFormatted?.singleDate?.formatted : null;

    this.initializeSearchCriteria();
    this.getResultFromDB();
  }

  initializeSearchCriteria() {
    const propArray = [];
    if (this.propertyTypes) {
      this.propertyTypes.forEach(value => {
        if (!!value.IsSelected) {
          if (value.IsSelected) {
            propArray.push(value.UseTypeID);
          }
        }
      });
    }
    this.searchCriteria.PropertyTypes = propArray;
    this.searchCriteria.IncludeStrataPropertiesForMaster = true;
    if (this.searchCriteria.NotStrata) {
      this.searchCriteria.StrataTypeIds =[PropertySearchRequestDTO.StrataTypeIdsEnum.NotStrata];
    } else {
      this.searchCriteria.StrataTypeIds = [];
      if (!!this.searchCriteria.ResearchStatusIds || !!this.searchCriteria.PropertyTypes || !!this.searchCriteria.IsSkipped || !!this.searchCriteria.StreetName || !!this.searchCriteria.StreetMax || !!this.searchCriteria.StreetMin || !!this.searchCriteria.PropertyId || !!this.searchCriteria.IsNotReviewed || !!this.searchCriteria?.LastReviewedDateMax || !!this.searchCriteria?.LastReviewedDateMin) {
        this.searchCriteria.StrataFilter = 1;
      } else {
        this.searchCriteria.StrataFilter = 2;
      }
    }
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    sessionStorage.setItem(SessionStorageKeys.SearchCriteria, JSON.stringify(this.searchCriteria));
  }

  getResultFromDB() {
    this.propertyLoading = true;
    this.IsSelectAll = false;
    this.searchCriteria.ZipCodes = this.searchCriteria?.Zipcode?.length > 0 ? this.searchCriteria?.Zipcode?.split(',') : [];
    this.searchCriteria.PropertyIds = this.searchCriteria?.Pids?.length > 0 ? this.searchCriteria?.Pids?.split(',').map(pid => parseInt(pid)) : [];
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    this.tmpSearchCriteria.HasNoBuildingFootprints = this.searchCriteria.HasNoBuildingFootprints ? true : false;
    this.tmpSearchCriteria.HasNoExistingParcelInTileLayer = this.searchCriteria.HasNoExistingParcelInTileLayer ?  true : false;
    this.propertyList = [];
    this.propertyListCopy = [];
    const reqBody = { ...this.tmpSearchCriteria }
    const keysTodelete = ['OffsetValue', 'StartingIndex', 'FrontEndStartingIndex', 'FrontEndOffsetValue', 'LastReviewedDateMaxFormatted', 'LastReviewedDateMinFormatted', 'Pids', 'Zipcode'];
    keysTodelete.forEach(field => {
      if (field in reqBody) {
        delete reqBody[field];
      }
    });
    const response_propertSearch = this._propertyService.getPropertiesBySearch(reqBody);
    response_propertSearch.subscribe((result: ApiResponsePropertySearchResponseDTO) => {
      if (!result.error) {
        this.propertyList = result.responseData.Properties || [];
        this.propertyList.forEach(property => {
          if (property?.Address?.AddressStreetName !== null) {
            // Trim extra spaces from the AddressStreetName field
            property.Address.AddressStreetName = property?.Address?.AddressStreetName.trim();
          }
        });
        if (this.searchCriteria.StrataFilter === 2) {
          this.propertyListCopy = this.propertyList.slice();
        } else {
          this.removeChildStrataFromListIfItsMasterStrataAlreadyExistsInList();
          this.propertyListCopy = this.propertyList.slice();
        }
        if (this.sortCriteria.SortParam === "defaultSort") {
          this.defaultSortOrder();
        }
        else {
          this.sortProperty(this.sortCriteria.SortParam, this.sortCriteria.SortOrder);
        }
        if (this.propertyList) {
          this.propertyCount = this.propertyList.length;
          this.totalRecords = this.propertyCount;
        }
        this.IsSearchInProgres = false;
        this.propertyLoading = false;
      } else {
        this.IsSearchInProgres = false;
        this.propertyLoading = false;
        this._notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this.IsSearchInProgres = false;
      this.propertyLoading = false;
      this._notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToSearchPropertiesByCriteria);
    });
    this.searchPropertyFields = false;
    this.searchPropertyTable = true;
  }

  getPropertyRecordDisplayNumber(index) {
    const currentPropertyRowNumber = ((index + 1) + (this.searchCriteria.FrontEndOffsetValue * (this.currentPage - 1))).toString().padStart(3, '0');
    return currentPropertyRowNumber;
  }


  private compareField(a: any, b: any, descending: boolean = false): number {
    if (a == null && b == null) return 0;
    if (a == null) return descending ? -1 : 1;
    if (b == null) return descending ? 1 : -1;
    if (typeof a === 'string' && typeof b === 'string') {
      return a.localeCompare(b) * (descending ? -1 : 1);
    }
    return (Number(a) - Number(b)) * (descending ? -1 : 1);
  }


  defaultSortOrder() {
    this.propertyList = this.propertyListCopy.slice();
    this.sortCriteria.SortParam = 'defaultSort';
    this.sortCriteria.SortOrder = '';
    sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
  
    this.propertyList.sort((a, b) => {
      // ZipCode
      let zipComparison = this.compareField(a?.Address?.ZipCode, b?.Address?.ZipCode);
      if (zipComparison !== 0) return zipComparison;
  
      // AddressStreetName
      let addressComparison = this.compareField(a?.Address?.AddressStreetName, b?.Address?.AddressStreetName);
      if (addressComparison !== 0) return addressComparison;
  
      // StreetNumberMin (alphanumeric)
      const matchA = (a?.Address?.StreetNumberMin || '').toString().match(/(\d+)(\D*)/) || ['', '', ''];
      const matchB = (b?.Address?.StreetNumberMin || '').toString().match(/(\d+)(\D*)/) || ['', '', ''];
      const [numA, alphaA] = [matchA[1].padStart(10, '0'), matchA[2]];
      const [numB, alphaB] = [matchB[1].padStart(10, '0'), matchB[2]];
  
      if (numA !== numB) {
        return parseInt(numA, 10) - parseInt(numB, 10);
      }
      let alphaComparison = alphaA.localeCompare(alphaB);
      if (alphaComparison !== 0) return alphaComparison;
  
      // CondoTypeID
      return this.compareField(a?.CondoTypeID, b?.CondoTypeID, true);
    });
  
    this.setPageDefaultValuesOnSortAndOffsetChange();
  }

  appendChildStratasAboveMasterStrataInTheList() {
    for (let i = 0; i < this.propertyList.length; i++) {
      
      // Check if CondoTypeID is 3 and if StrataProperties exist
      if ((this.propertyList[i]?.CondoTypeID === PropertySearchResponseBaseDTO.CondoTypeIDEnum.MasterStrataRecord || this.propertyList[i]?.CondoTypeID === PropertySearchResponseBaseDTO.CondoTypeIDEnum.MasterFreehold) && this.propertyList[i]?.AllPropertiesDetails.StrataProperties) {
        // Parse StrataProperties
        let strataProperties = this.propertyList[i]?.AllPropertiesDetails?.StrataProperties;
        // Sort them by CondoUnit in ascending order
        strataProperties.sort((a, b) => {
          if (a?.AllPropertiesDetails?.CondoUnit === null && b?.AllPropertiesDetails?.CondoUnit === null) {
            return 0;
          } else if (a?.AllPropertiesDetails?.CondoUnit === null) {
            return 1;
          } else if (b?.AllPropertiesDetails?.CondoUnit === null) {
            return -1;
          } else {
            // Pad single-digit numbers with leading zeros before comparison
            const condoUnitA = a?.AllPropertiesDetails?.CondoUnit.toString().padStart(2, '0');
            const condoUnitB = b?.AllPropertiesDetails?.CondoUnit.toString().padStart(2, '0');
            // Convert padded CondoUnit to numbers before comparison
            return parseInt(condoUnitA) - parseInt(condoUnitB);
          }
        });
        // Append the sorted list above the current property
        // TODO: Type of Strataproperties has to be same as propertyList
        this.propertyList.splice(i, 0, ...strataProperties as any);
        // Move the loop's index to the next position after the last inserted property
        i += strataProperties.length;
      }
    }
  }


  removeChildStrataFromListIfItsMasterStrataAlreadyExistsInList() {
    // Iterate over the properties
    for (let i = 0; i < this.propertyList.length; i++) {
      // Check if CondoTypeID is 2
      if (this.propertyList[i]?.CondoTypeID === PropertySearchResponseBaseDTO.CondoTypeIDEnum.Strata || this.propertyList[i]?.CondoTypeID === PropertySearchResponseBaseDTO.CondoTypeIDEnum.ChildFreehold) {
        // Check if it has a MasterPropertyID
        if (this.propertyList[i]?.AllPropertiesDetails?.MasterPropertyID !== null) {
          const masterPropertyId = this.propertyList[i]?.AllPropertiesDetails?.MasterPropertyID;
          // Find the index of the property with the matching MasterPropertyID
          const index = this.propertyList.findIndex(property => property.PropertyID === masterPropertyId);
          // If found, remove the property initially checked for CondoTypeID
          if (index !== -1) {
            this.propertyList.splice(i, 1);
            // Decrement i to account for the removed property
            i--;
            // check i is non-negative
            if (i < 0) {
              i = 0;
            }
          }
        }
      }
    }

  }

  getResearchers = () => {
    const arealyticsUsers = this.lookupService.getArealyticsUsers();
    arealyticsUsers.subscribe((result: ApiResponseListUsersDTO) => {
      if (!result.error) {
        const data = result.responseData;
        this.researchersList = data.filter(person => person.RoleID == UserRoles.Research_Analyst);
        this.researchersList.forEach(person => person.Fullname = `${person.FirstName} ${person.LastName}`);
        this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.ResearchersList, value: this.researchersList });
      }
    });
  }

  getResearchStatuses() {
    this.researchTypes = this.propertyLookup['ResearchType'] ?? [];
  }

  clearPropertyResultsFromSessionStorage() {
    const KeysToRemoveFromSS = [SessionStorageKeys.IsNavigationFromSearch,
    SessionStorageKeys.ActiveGridRowNumber,
    SessionStorageKeys.PropertyId,
    SessionStorageKeys.SelectedPropertyPos,
    SessionStorageKeys.SearchResultsPageDetails,
    SessionStorageKeys.SearchResults,
    SessionStorageKeys.NavigationPreference,
    ];
    clearSessionStorage(KeysToRemoveFromSS);
  }

  clearSearchModuleData() {
    const metaDataKeysToDelete = [
      MetaDataCollectionKeys.PropertyTypeList,
      MetaDataCollectionKeys.ResearchStatusesList,
      MetaDataCollectionKeys.StateList,
      MetaDataCollectionKeys.MultiFloors,
      MetaDataCollectionKeys.PropertyLookup
    ];
    metaDataKeysToDelete.forEach(key => {
      this.metaDataIndexedDBService.deleteDataFromMetaData(key);
    });
    this.clearPropertyResultsFromSessionStorage();
    localStorage.removeItem(MetaDataCollectionKeys.MultiFloorPolygons);
  }

  navigateToMapSearch(isMapSearch = 'true') {
    this.clearSearchModuleData();
    sessionStorage.setItem(SessionStorageKeys.IsFromMapSearch, isMapSearch);
    this.router.navigate(['/expressmapsearch']);
  }

  backToSearch() {
    this.clearPropertyResultsFromSessionStorage();
    this.metaDataIndexedDBService.deleteDataFromMetaData(MetaDataCollectionKeys.PropertyList);
    if (sessionStorage.getItem(SessionStorageKeys.SearchCriteria)) {
      this.restoreSearchCriteriaFromCache();
    }
    this.currentPagePropertyList = [];
    this.searchPropertyFields = true;
    this.searchPropertyTable = false;
  }

  getPropertyList() {
    this.tmpSearchCriteria = JSON.parse(JSON.stringify(this.searchCriteria));
    sessionStorage.setItem(SessionStorageKeys.SearchCriteria, JSON.stringify(this.searchCriteria));
    const offset = this.searchCriteria.FrontEndOffsetValue;
    if (this.propertyList) {
      this.propertyCount = this.propertyList.length;
      this.totalRecords = this.propertyCount;
    }
    this.propertypager = this.pagerService.getPager(this.propertyCount, this.searchCriteria.FrontEndStartingIndex, offset);
    this.currentPagePropertyList = this.propertyList.slice(this.propertypager.startIndex, this.propertypager.endIndex + 1);
    const propertySearchResult = new PropertySearchResult();
    propertySearchResult.PropertyCount = this.propertyCount;
    propertySearchResult.Pager = this.propertypager;
    propertySearchResult.CurrentPagePropertyList = this.currentPagePropertyList;
    sessionStorage.setItem(SessionStorageKeys.SearchResults, JSON.stringify(propertySearchResult));
    this.metaDataIndexedDBService.saveDataInMetaData({ key: MetaDataCollectionKeys.PropertyList, value: { propertyList: this.propertyList, propertyListCopy: this.propertyListCopy } });
    this.scrollToRow(0);
  }

  selectAll(event) {
    if (event && event.target) {
      this.currentPagePropertyList.forEach(x => x.IsSelected = event.target.checked);
      this.IsSelectAll = event.target.checked;
    }
  }
  sortProperty(sortParam: string, sortOrder: string) {
    // Convert sortOrder to match expected type
    const normalizedSortOrder: 'Ascending' | 'Descending' = sortOrder.toLowerCase() === 'descending' ? 'Descending' : 'Ascending';
  
    this.propertyList = this.propertyListCopy.slice();
    this.sortCriteria.SortParam = sortParam;
    this.sortCriteria.SortOrder = normalizedSortOrder;
    sessionStorage.setItem(SessionStorageKeys.SortCriteria, JSON.stringify(this.sortCriteria));
  
    // Apply sorting
    this.propertyList = this.sortByField(this.propertyList, sortParam, normalizedSortOrder);
  
    // Update pagination
    this.setPageDefaultValuesOnSortAndOffsetChange();
  }

  // Unified sorting function
  private sortByField(list: PropertySearchList[], field: string, sortOrder: 'Ascending' | 'Descending'): PropertySearchList[] {
    const direction = sortOrder === 'Ascending' ? 1 : -1;
  
    return list.sort((a, b) => {
      // Get the value for the field, using valueGetter if defined
      const header = this.propertyHeader.find(h => h.field === field);
      let valueA = header?.valueGetter ? header.valueGetter(a) : this.getNestedProperty(a, field);
      let valueB = header?.valueGetter ? header.valueGetter(b) : this.getNestedProperty(b, field);
  
      // Handle null/undefined values
      if (valueA == null && valueB == null) return 0;
      if (valueA == null) return 1 * direction;
      if (valueB == null) return -1 * direction;
  
      // Handle specific field types
      if (field === 'Address.StreetNumberMin' || field === 'Address.StreetNumberMax') {
        // Alphanumeric sorting for street numbers
        const matchA = (valueA || '').toString().match(/(\d+)(\D*)/) || ['', '', ''];
        const matchB = (valueB || '').toString().match(/(\d+)(\D*)/) || ['', '', ''];
        const [numA, alphaA] = [matchA[1].padStart(10, '0'), matchA[2]];
        const [numB, alphaB] = [matchB[1].padStart(10, '0'), matchB[2]];
  
        if (numA !== numB) {
          return (parseInt(numA, 10) - parseInt(numB, 10)) * direction;
        }
        return alphaA.localeCompare(alphaB) * direction;
      } else if (field === 'PropertyID' || field === 'CondoTypeID' || field === 'IsSkipped') {
        // Numeric sorting
        return (Number(valueA) - Number(valueB)) * direction;
      } else {
        // String sorting
        return valueA.toString().localeCompare(valueB.toString()) * direction;
      }
    });
  }

// Helper to access nested properties
private getNestedProperty(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => (current ? current[key] : undefined), obj);
}

  setPageDefaultValuesOnSortAndOffsetChange() {
    this.appendChildStratasAboveMasterStrataInTheList();
    this.currentPage = 1;
    this.searchCriteria.FrontEndStartingIndex = 1;
    this.getPropertyList();
  }

  onOffsetChange() {
    this.currentPage = 1;
    this.searchCriteria.FrontEndStartingIndex = 1;
    this.getPropertyList();
  }

  setPropertyPage(page: number) {
    this.currentPage = page;
    this.searchCriteria.FrontEndStartingIndex = page;
    this.getPropertyList();
  }

  scrollToRow(rowIndex: number): void {
    const instance = this;
    //  Use nativeElement to access the DOM element
    setTimeout(() => {
      const rowElement = instance.dataTable.el.nativeElement.getElementsByClassName(`row-${rowIndex}`)[0];
      if (rowElement) {
        rowElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 500);
  }

  getClassName(index) {
    return `row-${index}`;
  }

  onDateChange() {
    const minDate = this.parseDate(this.searchCriteria.LastReviewedDateMinFormatted);
    const maxDate = this.parseDate(this.searchCriteria.LastReviewedDateMaxFormatted);
    // Validate date range
    if(minDate && maxDate){
      this.reviewedDateError = !!(minDate > maxDate);
    }else{
      this.reviewedDateError = false;
    }
  }
  
  parseDate(date: any): Date | null {
    if (date && date.singleDate && date.singleDate.jsDate) {
      return new Date(date.singleDate.jsDate);
    }
    return null;
  }

  clearDate(field: 'min' | 'max'): void {
    if (field === 'min') {
      this.searchCriteria.LastReviewedDateMinFormatted = null;
    } else if (field === 'max') {
      this.searchCriteria.LastReviewedDateMaxFormatted = null;
    }
    this.onDateChange(); // Revalidate dates
  }

  getColumnWidth(header: string): string {
    switch (header) {
      case 'Number': return '80px';
      case 'Property ID': return '100px';
      case 'Zip Code': return '60px';
      case 'Street Number Min': return '80px';
      case 'Street Number Max': return '80px';
      case 'Street Name': return '200px';
      case 'City': return '180px';
      case 'General Use': return '140px';
      case 'Specific Use': return '200px';
      case 'Status': return '180px';
      case 'Record Type': return '105px';
      case 'Unit': return '55px';
      case 'Skip': return '55px';
      case 'Comments': return '150px';
      case 'Audit Status': return '150px';
      default: return '120px';
    }
  }
  getScrollHeight() {
    return this.currentPagePropertyList?.length > 15 ? '70vh' : 'auto';
  }
}
