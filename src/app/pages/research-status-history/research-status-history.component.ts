// Angular core imports
import { Component, Input, OnChanges } from '@angular/core';
// Third-party imports
import { Subscription } from 'rxjs';
// Application-specific models
import { PropertyResearchStatus } from '../../models/PropertyResearchStatus';
// Application-specific services
import { SharedDataService } from '../../services/shareddata.service';
import { PropertyService } from '../../services/api-property.service';
import { CommunicationService } from '../../services/communication.service';
import { LoginService } from '../../services/login.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
import { CommonStrings, DefaultDateFormat } from '../../constants';
import { ApiResponseListPropertyResearchResponseDTO } from '../../api-client';

@Component({
  selector: 'app-research-status-history',
  templateUrl: './research-status-history.component.html',
  styleUrls: ['./research-status-history.component.css']
})
export class ResearchStatusHistoryComponent implements OnChanges {

  selectedOption: any;
  @Input() parentId: any;
  @Input() isResearchClicked: boolean = false;
  researchStatusHistoryListener: Subscription;
  isResearchStatusHistory: boolean = false;
  propertyResearchStatus: Array<PropertyResearchStatus>;
  dateFormat: string = DefaultDateFormat;

  constructor(  private _propertyService: PropertyService,
    private _sharedDataService: SharedDataService,
    private communicationService: CommunicationService,
    private loginService: LoginService, private notificationService: NotificationService) {
      this.researchStatusHistoryListener = this.communicationService.subscribe('fetchResearchStatus')?.subscribe(result => {
        this.showPropertyResearchStatus(this.parentId);
      }); 
      this.researchStatusHistoryListener = this.communicationService.subscribe('fetchChangeLog').subscribe(result => {
        this.parentId = result?.data
        this.showPropertyResearchStatus(this.parentId);
      });
      this.dateFormat = this.loginService.UserInfo.DateFormat;
    }

  ngOnChanges() {
    this.showPropertyResearchStatus(this.parentId);
  }

  ngOnDestroy(){
    this.researchStatusHistoryListener.unsubscribe;
  }

  showPropertyResearchStatus(propertyId) {
    if (propertyId > 0) {
      this.propertyResearchStatus = this._sharedDataService.researchStatusList || [];
      const response_researchStatus = this._propertyService.getPropertyResearchStatus(propertyId);
      response_researchStatus.subscribe((result: ApiResponseListPropertyResearchResponseDTO) => {
        if (!result.error) {
          let propResearchStatus = result && result.responseData && result.responseData || [];
          if (this.propertyResearchStatus) {
            this.propertyResearchStatus.forEach(status => {
              status.IsActive = false;
              status.ModifiedDate = "";
              status.ModifiedPersonName = "";
            });
          }
          propResearchStatus.forEach(value => {
            this.propertyResearchStatus.forEach(emp => {
              if (emp.PropertyResearchTypeID == value.PropertyResearchTypeID) {
                emp.IsActive = true;
                emp.ModifiedBy = value.ModifiedBy;
                emp.ModifiedPersonName = value.ModifiedPersonName;
                emp.ModifiedDate = value.ModifiedDate;
                emp.PropertyResearchStatusID = value.PropertyResearchStatusID;
              }
            });
          });
        } else {
          this.notificationService.ShowErrorMessage(result.message);
        }
        this._sharedDataService.propertyResearchStatus = this.propertyResearchStatus;
        this._sharedDataService.propertyResearchStatus.forEach(element => {
          element.PropertyID = propertyId;
        });
        const dataArray = this.propertyResearchStatus?.filter(research => research.IsActive === true)
        const sortedArray = dataArray?.sort((a, b) => {
          const dateA = new Date(a.ModifiedDate).getTime();
          const dateB = new Date(b.ModifiedDate).getTime();
          return dateA - dateB;
        });
        if (sortedArray && sortedArray.length > 0 && sortedArray?.slice(-1)[0]) {
          this.selectedOption = sortedArray?.slice(-1)[0];
        }
      }, error => {
        this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToGetResearchStatusHistory);
      })
    }
  }
}
