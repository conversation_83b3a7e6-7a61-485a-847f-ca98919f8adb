// Angular core imports
import { Component, OnInit, Input, NgZone, Output, EventEmitter, SimpleChanges } from '@angular/core';
// Third-party imports
import { Subscription } from 'rxjs';
// Application-specific DTOs and models
import { mapEditPropertyDTO } from '../../DTO/mapEditPropertyDTO';
import { ApiResponseListPropertyStrataDetailsDTO, LoginUserInfoDTO, PropertyDetailsDTO, PropertyStrataDetailsDTO } from '../../../app/api-client';
// Application-specific services
import { CommunicationService } from '../../services/communication.service';
import { PropertyService } from '../../services/api-property.service';
import { LoginService } from '../../services/login.service';
import { IMetaData, MetaDataIndexedDBService } from '../../services/indexed-db-service.service';
import { PropertyTrackingService } from '../../services/property-tracking.service';
import { NotificationService } from '../../modules/notification/service/notification.service';
// Application-specific enums and constants
import { SessionStorageKeys } from '../../enumerations/sessionStorageKeys';
import { UseTypes } from '../../enumerations/useTypes';
import { CommonStrings } from '../../constants';
import { arraysHaveSameElements, dynamicIntegerSort, dynamicSort } from '../../utils';

@Component({
  selector: 'app-strata',
  templateUrl: './strata.component.html',
  styleUrls: ['./strata.component.scss']
})
export class StrataComponent implements OnInit {
  @Input() initialDetails: mapEditPropertyDTO;
  @Output() showPropertyInfo = new EventEmitter();
  @Input() property: PropertyDetailsDTO;
  @Input() selectedUseTypeID: any;
  @Output() addNewUnitsToMaster: EventEmitter<any> = new EventEmitter<any>();
  @Input() isFreehold: boolean = false;
  updateFormSubscription: Subscription;
  fetchFreeholdListSubscription: Subscription;
  linkedProperties: PropertyStrataDetailsDTO[];
  strataFreeholdTableHeader: any;
  BuildingSizeHeader = '';
  BuildingSizeValue: any = '';
  LotSizeValue: any = '';
  UnitId: LoginUserInfoDTO.UnitIdEnum = LoginUserInfoDTO.UnitIdEnum.Metric;
  UnitDisplayTextSize: any;
  propertyId: any;
  visitedStrataIds: any[];
  visitedPropertyIds: any = [];
  editedPropertyIds: any = [];
  editedStrataIds: any = [];
  isNavigationFromSearch: any;
  metaDataIndexedDBService: MetaDataIndexedDBService;
  showAddUnitsBtn = false;
  useTypesToEnableAddUnitsBtns = [UseTypes.Apartments, UseTypes.Office, UseTypes.Retail, UseTypes.Industrial];
  constructor(private communicationService: CommunicationService,
    private zone: NgZone,
    private _propertyService: PropertyService,
    private _loginService: LoginService,
    private propertyTrackingService: PropertyTrackingService,
    private notificationService: NotificationService) {
    this.UnitId = this._loginService.UserInfo.UnitId;
    this.UnitDisplayTextSize = this._loginService.UserInfo.UnitDisplayTextSize;
    this.updateFormSubscription = this.communicationService.subscribe('updatePropertyForm').subscribe(result => {
      this.zone.run(() => {
        if (this.initialDetails.propertyId != result.data.propertyId) {
          this.propertyId = result.data.propertyId;
          this.initialDetails = result.data;
          sessionStorage.setItem(SessionStorageKeys.LastVisitedStrataProperty, JSON.stringify(this.property))
          this.getLinkedProperties();
        }
      });
    });
    this.fetchFreeholdListSubscription = this.communicationService
    .subscribe('fetchFreeholdList')
    .subscribe((result) => {
      this.zone.run(() => {
        this.getLinkedProperties();
      });
    });
  }
  ngOnDestroy() {
    this.updateFormSubscription.unsubscribe();
    this.fetchFreeholdListSubscription.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes.selectedUseTypeID) {
      const isMasterProperty = (this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterStrataRecord || this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterFreehold);
      this.showAddUnitsBtn = this.useTypesToEnableAddUnitsBtns.includes(changes.selectedUseTypeID.currentValue) && isMasterProperty;
    }
    if(changes.property) {
      const isMasterProperty = (changes.property.currentValue.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterStrataRecord || changes.property.currentValue.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterFreehold);
      this.showAddUnitsBtn = this.useTypesToEnableAddUnitsBtns.includes(this.selectedUseTypeID) && isMasterProperty;
    }
  }
  ngOnInit() {
    const isMasterStrataProperty = this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterStrataRecord || this.property.CondoTypeID === PropertyDetailsDTO.CondoTypeIDEnum.MasterFreehold;
    this.showAddUnitsBtn = this.useTypesToEnableAddUnitsBtns.includes(this.selectedUseTypeID) && isMasterStrataProperty;
    this.editedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.EditedStrataIds)) || [];
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
    this.isNavigationFromSearch = JSON.parse(sessionStorage.getItem(SessionStorageKeys.IsNavigationFromSearch));
    this.getVisitedAndEditedProperties();
    this.propertyId = this.initialDetails.propertyId;
    if (this.UnitId === LoginUserInfoDTO.UnitIdEnum.Metric) {
      this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
      this.BuildingSizeValue = 'BuildingSizeSM';
      this.LotSizeValue = 'LotSizeSM';
    } else {
      this.BuildingSizeHeader = 'Size (' + this.UnitDisplayTextSize + ')';
      this.BuildingSizeValue = 'BuildingSizeSF';
      this.LotSizeValue = 'LotSizeSF';
    }
    this.getTableHeader();
    if (!this.linkedProperties || this.linkedProperties.length <= 0) {
      this.getLinkedProperties();
      sessionStorage.setItem(SessionStorageKeys.LastVisitedStrataProperty, JSON.stringify(this.property))
    }
  }

  async getVisitedAndEditedProperties() {
    this.visitedPropertyIds = (await this.propertyTrackingService.getVisitedPropertyIds()) ?? [];
    this.editedPropertyIds = (await this.propertyTrackingService.getEditedPropertyIds()) ?? [];
    }

  getLinkedProperties() {
    const sortedProperties = []
    let activeRecord, masterRecord;
    if (this.initialDetails.propertyId) {
    const response = this._propertyService.getLinkedPropertyDetails(this.initialDetails.propertyId);
    response.subscribe((result: ApiResponseListPropertyStrataDetailsDTO) => {
      if (!result.error) {
        this.linkedProperties = result.responseData;
        const results = this.linkedProperties || [];
        activeRecord = results.find(row => row.PropertyID === this.propertyId);
        masterRecord = results.find(row => row.IsMaster);
        let listWithoutActiveAndMaster = results.filter(row => (!(row.PropertyID === this.propertyId) && !row.IsMaster));
        listWithoutActiveAndMaster = listWithoutActiveAndMaster.sort(dynamicIntegerSort('StrataUnit', 'Ascending'));
        sortedProperties.push(activeRecord);
        sortedProperties.push(...listWithoutActiveAndMaster);
        if (activeRecord?.PropertyID != masterRecord?.PropertyID) {
          sortedProperties.push(masterRecord);
        }
        this.linkedProperties = sortedProperties;
        this.visitedStrataIds = JSON.parse(sessionStorage.getItem(SessionStorageKeys.VisitedStrataIds)) || [];
        const strataPropertyIds = sortedProperties.map(strata => strata?.PropertyID);
        const sessionStrataIds = sessionStorage.getItem(SessionStorageKeys.StrataPropertyIds);
        if (sessionStrataIds) {
          const sessionStorageStrata = JSON.parse(sessionStrataIds);
          if (!arraysHaveSameElements(sessionStorageStrata, strataPropertyIds)) {
            this.setSessionStrataIds(strataPropertyIds);
          }
        } else {
          this.setSessionStrataIds(strataPropertyIds);
        }
      } else {
        this.notificationService.ShowErrorMessage(result.message);
      }
    }, error => {
      this.notificationService.ShowErrorMessage(error?.error?.message ?? CommonStrings.ErrorMessages.FailedToFetchLinkedProperties);
    });
    }
  }

  setSessionStrataIds(strataIds) {
    if (strataIds.filter(id => !!id).length > 0) {
      sessionStorage.setItem(SessionStorageKeys.StrataPropertyIds, JSON.stringify(strataIds));
      sessionStorage.removeItem(SessionStorageKeys.VisitedStrataIds);
    }        
  }

  getTableHeader() {
    this.strataFreeholdTableHeader = [
      { field: 'StrataType', header: 'Type' },
      { field: 'PropertyName', header: 'Property Name' },
      { field: 'Address', header: 'Address' },
      { field: 'CondoUnit', header: 'Strata Unit' },
      { field: this.BuildingSizeValue, header: this.BuildingSizeHeader },
      { field: this.LotSizeValue, header: 'Lot Size' },
      { field: 'ParcelNumbers', header: 'Parcel #' },
      { field: 'PropertyID', header: 'PropertyID' }
    ];
  }

  showPropertySummary(data) {
    this.showPropertyInfo.emit(data.PropertyID);
  }
  sortProperty(sortParam, sortOrder) {
    if (sortParam == 'Address' || sortParam == 'ParcelNumbers') {
      this.linkedProperties = this.linkedProperties.sort(dynamicSort(sortParam, sortOrder));
    }
    else {
      this.linkedProperties = this.linkedProperties.sort(dynamicIntegerSort(sortParam, sortOrder));
    }
  }

  isPropertyVisited(property) {
    if (this.isNavigationFromSearch && this.visitedPropertyIds && this.visitedPropertyIds.length > 0) {
      return this.visitedPropertyIds.includes(property);
    } else {
      if (this.visitedStrataIds && this.visitedStrataIds.length > 0) {
        return this.visitedStrataIds.includes(property);
      }
    }
  }

  isPropertyEdited(propertyId) {
    if (this.isNavigationFromSearch && this.editedPropertyIds && this.editedPropertyIds.length > 0) {
      return this.editedPropertyIds.includes(propertyId);
    } else {
      if (this.editedStrataIds && this.editedStrataIds.length > 0) {
        return this.editedStrataIds.includes(propertyId);
      }
    }
  }

  addChildUnits(addMultiUnits = false) {
    let strataMin = undefined;
    const findLastValue = (arr: any[]): number => {
      if (!arr || arr.length === 0) {
        return 0;
      }
      let max = Number.MIN_VALUE;
      for (let i = 0; i < arr.length; i++) {
        const childUnit = arr[i].CondoUnit ? Number(arr[i].CondoUnit) : 0;
        if (!isNaN(childUnit) && childUnit > max) {
          max = childUnit;
        }
      }
      return max;
    }
    const lastMax = findLastValue(this.linkedProperties);
    strataMin = `${lastMax+1}`;
    this.addNewUnitsToMaster.emit({strataMin: strataMin, isMultiStrata: addMultiUnits, strataList: this.linkedProperties, isFreehold: this.isFreehold})
  }
  getColumnWidth(header: string): string {
    switch (header) {
    case 'Type': return '120px';
    case 'Property Name': return '160px';
    case 'Address': return '160px';
    case 'Strata Unit': return '95px';
    case this.BuildingSizeHeader: return '90px';
    case 'Lot Size': return '90px';
    case 'Parcel #': return '120px';
    case 'PropertyID': return '100px';
    default: return '120px';
}
}
  getScrollHeight() {
      return this.linkedProperties?.length > 6 ? '50vh' : 'auto';
  }
}
