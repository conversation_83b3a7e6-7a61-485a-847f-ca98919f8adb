import { Injectable } from '@angular/core';
import { AddressControllerService, CityRequestDTO } from '../api-client';
import { PropertyService } from '../api-client/api/property.service';
import { AdditionalAddressRequestDTO } from '../api-client';

@Injectable()
export class AddressService  {
  constructor(private addressControllerService: AddressControllerService, private propertyService: PropertyService) {}

  public createCity(cityRequestDTO: CityRequestDTO) {
    return this.addressControllerService.createCity(cityRequestDTO)
  }
  public deleteAdditionalAddress(additionalAddressId: number) {
    return this.propertyService.deleteAdditionalAddress(additionalAddressId);
  }

  public saveAdditionalAddress(address: AdditionalAddressRequestDTO) {
    return this.propertyService.saveAdditionalAddress(address);
  }
  public updateAdditionalAddress(additionalAddressId: number, address: AdditionalAddressRequestDTO) {
    return this.propertyService.updateAdditionalAddress(additionalAddressId, address);
  }
  public getAdditionalAddress(propertyId: number) {
    return this.propertyService.getAdditionalAddress(propertyId);
  }
}
