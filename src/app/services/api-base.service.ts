import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../environments/environment';
import { SessionStorageKeys } from '../enumerations/sessionStorageKeys';

@Injectable()
export class ApiBaseService {

  private spinnerCounter: number;

  // constructor(private _http: HttpClient) { }
   apiAuthKey = environment.GoogleMapApiKey;
  _getHeaders: any;
  _serviceURL: string;
  _countryCode: any;
  constructor(private _http: HttpClient) {

    // tslint:disable-next-line:max-line-length
    this._getHeaders = new HttpHeaders({
      'Content-Type': 'application/json'
        , 'Authorization': 'Basic ' + btoa(this.apiAuthKey) 
    });

    this._serviceURL = environment.baseUrl;
    this.spinnerCounter = 0;
  }
  setHeader() {
    let token = null;
    if (!!(sessionStorage.getItem(SessionStorageKeys.AccessToken))) {
      token = (sessionStorage.getItem(SessionStorageKeys.AccessToken));
    }
    // tslint:disable-next-line: deprecation
    this._getHeaders = new HttpHeaders({
      'Content-Type': 'application/json'
      , Authorization: 'Bearer ' + token,
    });
  }
  private generateParam(data: object): HttpParams {
    let params = new HttpParams();
    // tslint:disable-next-line:forin
    for (const i in data) {
      params = params.append(i, data[i]);
    }
    return params;
  }

  protected httpGet(url: string, data: object = null, showSpinner: boolean = true): Observable<any> {
    this.setHeader();
    let params = new HttpParams();
    const headers = this._getHeaders;
    if (data != null) {
      params = this.generateParam(data);
    }
    const response = this._http.get(url, { params: params, headers: headers, observe: 'response' });
    return this.buildResult(response, showSpinner);
  }

  protected httpPost(url: string, data: any, showSpinner: boolean = true): Observable<any> {
    this.setHeader();

    const response = this._http.post(url, data, { headers: this._getHeaders, observe: 'response' });
    return this.buildResult(response, showSpinner);
  }

  protected httpPut(url: string, data: any, showSpinner: boolean = true): Observable<any> {
    this.setHeader();

    const response = this._http.put(url, data, { headers: this._getHeaders, observe: 'response' });
    return this.buildResult(response, showSpinner);
  }

  protected httpDelete(url: string, data?: any): Observable<any> {
    const options = {
      headers: this._getHeaders,
      body: data
    };
    const response = this._http.delete(url, options);
    return this.buildResult(response, true);
  }
  
  private buildResult(response: Observable<any>, showSpinner: boolean = true): Observable<any> {
    let result = new Subject<any>();
    response.subscribe(data => {
      result.next(data);
    },
    err => {
      result.error(err);
    });
    return result;
  }

}
