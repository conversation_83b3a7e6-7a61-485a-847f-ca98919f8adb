import { Injectable } from '@angular/core';
import { EnumCountry } from '../enumerations/county';
import { ChangeLogsService, MultipleChildRequestDTO, PropertyDetailsDTO, PropertyIntersectRequestDTO, PropertyMapSearchRequestDTO,
  PropertySearchRequestDTO, PropertyStrataRelationshipRequestDTO, PropertyService as PService, ResearchService } from '../api-client';

@Injectable()
export class PropertyService {
  constructor(private pservice: PService, private researchStatusService: ResearchService, private changeLogsService: ChangeLogsService ) {
  }
  public convertUnit(countryId, from, to, value) {
    if ( countryId === EnumCountry.Australia || countryId === EnumCountry.SouthAfrica) {
      if (from === 'SF' && to === 'SqM') {
        // tslint:disable-next-line:curly
        if (value) value = value * 0.092903;
      } else if (from === 'SqM' && to === 'SF') {
        // tslint:disable-next-line:curly
        if (value) value = value / 0.092903;
      } else if (from === 'ft' && to === 'M') {
        // tslint:disable-next-line:curly
        if (value) value = value / 0.3048;
      } else if (from === 'M' && to === 'ft') {
        // tslint:disable-next-line:curly
        if (value) value = value * 0.3048;
      }
      return value;
    }
  }
  public GetChangeLog(type: string, typeId: number) {
    return this.changeLogsService.getChangeLog(type, typeId);
  }
  public getPropertyById(propertyId: number) {
    return this.pservice.findPropertyDetailsByPropertyID(propertyId);
  }
  public saveProperty(property: PropertyDetailsDTO) {
    if (property.PropertyID) {
      return this.pservice.updateProperty(property);
    }
    // If PropertyID is not present, create a new property
    delete property.PropertyID;
    return this.pservice.createProperty(property);
  }
  public linkChildsToMaster(requestBody: PropertyStrataRelationshipRequestDTO) {
    return this.pservice.linkChildToMaster(requestBody);
  }
  public getLinkedPropertyDetails(property_Id: any) {
    return this.pservice.getLinkedPropertyDetails(property_Id);
  }
  public getPropertiesByMapSearch(searchCriteria: PropertyMapSearchRequestDTO) {
    return this.pservice.getPropertiesByMapSearch(searchCriteria);
  }
  public getPropertiesBySearch(searchCriteria: PropertySearchRequestDTO) {
    return this.pservice.getPropertiesBySearch(searchCriteria);
  }
  public getMasterPropertydetails(searchText: string, strataType: PropertyDetailsDTO.CondoTypeIDEnum) {
    return this.pservice.masterPropertiesSearch(searchText, strataType);
  }
  public getPropertyResearchStatus(propertyId: number) {
    return this.researchStatusService.getResearchStatusHistory(propertyId);
  }
  public saveMultiChild(data: MultipleChildRequestDTO) {
    return this.pservice.savePropertyMultiStrata(data);
  }
  public getPropertyIntersectMarketSubmarket(propertyIntersectRequestDTO: PropertyIntersectRequestDTO) {
    return this.pservice.getPropertyIntersect(propertyIntersectRequestDTO);
  }
}
