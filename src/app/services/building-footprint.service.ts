import { Injectable } from '@angular/core';
import { PropertyService } from '../api-client/api/property.service';
import { BuildingFootprintCollectionRequestDTO, BuildingFootPrintDeleteDTO } from '../api-client';

@Injectable()
export class BuildingFootPrintService   {
  constructor(private PropertyService: PropertyService) {
  }

  public getBuildingFootPrintsByPropertyID(propertyId: number) {
    return this.PropertyService.getBuildingFootprintByPropertyId(propertyId);
  }

  public saveMultiFootprint(reqBody: BuildingFootprintCollectionRequestDTO) {
    return this.PropertyService.saveBuildingFootprints(reqBody);
  }

  public deleteBuildingFootprints(propertyId: number, buildingFootPrintIds: string) {
    const reqBody: BuildingFootPrintDeleteDTO = {
      BuildingFootPrintIDs: buildingFootPrintIds,
      PropertyId: propertyId
    }
    return this.PropertyService.deleteBuildingFootprints(reqBody)
  }
}
