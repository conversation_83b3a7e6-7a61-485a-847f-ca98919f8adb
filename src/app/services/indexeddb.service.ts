import { Injectable } from '@angular/core';
import { FileObject } from '../modules/aws/types/types'
import { IndexedDBCollections } from '../enumerations/indexeddb'
import { MediaUploadEvents } from '../enumerations/background-upload-worker'
import { MediaInfoDTO } from '../models/media-dto';

export interface IMediaInfo {
    fileObject?: FileObject;
    media: MediaInfoDTO;
    fileName: string;
    attachments: { fileName: string, media: MediaInfoDTO }[]
}
export interface IIndexedDBMedia {
    mediaIdentifier: string;
    mediaInformation: IMediaInfo
}

export type Task = {
    taskID: string,
    taskIdentifier: string,
    mediaIdentifier: string,
    isAttachment: boolean,
    attachmentIndex: number | null,
    path?: string | undefined,
    mediaID?: number | undefined
}

export interface IBackgroungSync {
    key: string,
    taskQueue: Array<Task>

}

@Injectable()
export class IndexedDBService {
    private database = 'AL-VST';
    private version = 1;
    private dbInstance: IDBDatabase;
    private indexDBInitializationStatus: boolean;
    s3UploadTaskIdentifier = 's3-upload';
    saveMediaTaskIdentifier = 'save-media-db';
    private backgroundSyncTaskQueueKey = 'to-execute';
    static uploadMediaMap: Map<string, number> = new Map<string, number>();

    constructor() {
        // if (version !== this.version) {
        //     this.version = version;
        // }
    
        this.indexDBInitializationStatus = false;
        !this.indexDBInitializationStatus && this.initializeIndexedDB(this.version).then(status => this.indexDBInitializationStatus = true).catch(() => this.indexDBInitializationStatus = false);
    }

    /**
    * Method to Initialize IndexedDB
    * @param { number } [_version=1] - The version number for the IndexedDB (optional, defaults to 1)
    */
    private initializeIndexedDB = async (version: number): Promise<boolean> => {
        return new Promise<boolean>((resolve, reject) => {

            const dbRequest: IDBOpenDBRequest = indexedDB.open(this.database, version);
            dbRequest.onerror = () => {
                reject(false);
            };

            dbRequest.onupgradeneeded = () => {
                const dbInstance: IDBDatabase = dbRequest.result;
                // Initialize 'image-upload' Object Store
                try {
                    if (!dbInstance.objectStoreNames.contains(IndexedDBCollections.imageUploadCollection)) {
                        dbInstance.createObjectStore(IndexedDBCollections.imageUploadCollection, { keyPath: 'mediaIdentifier' })
                    }

                    // Initialize 'background-sync-queue' Object Store
                    if (!dbInstance.objectStoreNames.contains(IndexedDBCollections.backgroundSyncTask)) {
                        dbInstance.createObjectStore(IndexedDBCollections.backgroundSyncTask, { keyPath: 'key' });
                    }

                    // Initialize 'pending' Object Store
                    if (!dbInstance.objectStoreNames.contains(IndexedDBCollections.pending)) {
                        dbInstance.createObjectStore(IndexedDBCollections.pending, { keyPath: 'taskID' });
                    }
                } catch (error) {
                    reject(false);
                }
            };

            dbRequest.onsuccess = () => {
                this.dbInstance = dbRequest.result;
                resolve(true);
            }
        });
    };

    /**
     * Method to Get IndexedDB Initialization Status 
    */
    getIndexedDBInitializationStatus = () => {
        return this.indexDBInitializationStatus;
    }

    /**
    * Method to Insert Data Into IndexedDB
    * @param { any } info Information to Insert
    * @param { string } store Object Store Name
    */
    saveMedia = async (info: IIndexedDBMedia, store: string = IndexedDBCollections.imageUploadCollection): Promise<void> => {
        return new Promise<void>(async (reslove, reject) => {

            if (store === IndexedDBCollections.imageUploadCollection) {
                const transaction = this.dbInstance.transaction(store, 'readwrite');
                const storeInstance = transaction.objectStore(store);

                const response = storeInstance.put(info);
                response.onsuccess = async () => {
                    // Update the Media Upload Map
                    if (!IndexedDBService.uploadMediaMap.has(info.mediaIdentifier)) {
                        IndexedDBService.uploadMediaMap.set(info.mediaIdentifier, 1);
                    } else {
                        let mediaCount = IndexedDBService.uploadMediaMap.get(info.mediaIdentifier);
                        mediaCount += 1;
                        IndexedDBService.uploadMediaMap.set(info.mediaIdentifier, mediaCount);
                    }
                    // Move to 's3-upload-task'
                    await this.moveToBackgroundTaskQueue({
                        taskID: this.generateUniqueTaskID(),
                        taskIdentifier: this.s3UploadTaskIdentifier,
                        mediaIdentifier: info.mediaIdentifier,
                        isAttachment: false,
                        attachmentIndex: null,
                        path: null,
                        mediaID: null
                    });

                    const uploadStartEvent = new CustomEvent(MediaUploadEvents.pending, {
                        detail: {
                            propertyID: info.mediaIdentifier.split('-')[0],
                            filename: info.mediaInformation.fileObject.file.name,
                            uniqueFilename: info.mediaInformation.fileName
                        }
                    });
                    document.dispatchEvent(uploadStartEvent);
                    reslove();
                }
            }
        });
    };

    /**
     * Method to check for the existence of data and retrieves it if found
     * @param { store } store Object Store Name
     * @param { string } searchKey KeyPath 
    */
    fetchMedia = async (store: string, searchKey: number | string): Promise<any | null> => {
        return new Promise<IIndexedDBMedia | null>((resolve, reject) => {
            if (Object.values(IndexedDBCollections).includes(store as IndexedDBCollections)) {
                const storeInstance = this.dbInstance.transaction(store, 'readonly').objectStore(store);
                const response = storeInstance.get(searchKey);
                response.onsuccess = () => {
                    resolve(response.result);
                }
                response.onerror = () => {
                    resolve(null);
                }

            } else {
                resolve(null)
            }

        })
    }

    removeMedia = (key: string, store: string = IndexedDBCollections.imageUploadCollection) => {
        const transaction = this.dbInstance.transaction(store, 'readwrite');
        const storeInstance = transaction.objectStore(store);
        const response = storeInstance.delete(key);

        response.onsuccess = () => {
            return;
        }
    }

    /* Method to Generate Unique TaskID */
    generateUniqueTaskID = () => {
        let d = new Date().getTime();
        const uniqueTaskId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            // tslint:disable-next-line: no-bitwise
            const r = (d + Math.random() * 16) % 16 | 0;
            d = Math.floor(d / 16);
            // tslint:disable-next-line: no-bitwise
            return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
        return uniqueTaskId;
    }

    /**
     * Method to update 'background-sync-queue'
    */
    moveToBackgroundTaskQueue = async (taskInfo: Task): Promise<void> => {
        return new Promise<void>(async (resolve, reject) => {
            const latestTaskQueue: IBackgroungSync = await this.fetchMedia(IndexedDBCollections.backgroundSyncTask, this.backgroundSyncTaskQueueKey) as IBackgroungSync;
            const transaction = this.dbInstance.transaction(IndexedDBCollections.backgroundSyncTask, 'readwrite');
            const storeInstance = transaction.objectStore(IndexedDBCollections.backgroundSyncTask);

            if (latestTaskQueue && latestTaskQueue.taskQueue.length > 0) {
                latestTaskQueue.taskQueue.push(taskInfo);
            }

            const response = storeInstance.put((latestTaskQueue && latestTaskQueue.taskQueue.length > 0) ? latestTaskQueue : { key: this.backgroundSyncTaskQueueKey, taskQueue: [taskInfo] });

            response.onsuccess = () => {
                resolve()
            }
        })
    }

    updateBackgroundSyncQueues = async (): Promise<Task | null> => {
        return new Promise<Task | null>((resolve, reject) => {
            const transaction = this.dbInstance.transaction([IndexedDBCollections.backgroundSyncTask], 'readwrite');
            const backgroundSyncStoreInstance = transaction.objectStore(IndexedDBCollections.backgroundSyncTask);

            // Retrieve the background task that is scheduled for execution
            const responseBackgroundSync = backgroundSyncStoreInstance.get(this.backgroundSyncTaskQueueKey);
            responseBackgroundSync.onsuccess = async () => {
                const BackgroundSyncTasks: IBackgroungSync = responseBackgroundSync.result;

                // Check if there are any tasks present in the current context
                if (BackgroundSyncTasks && BackgroundSyncTasks.taskQueue.length > 0) {
                    const taskToExecute = BackgroundSyncTasks.taskQueue.shift();
                    // Transfer the task to the 'Pending' queue for further processing
                    this.updatePendingQueue(false, taskToExecute);
                    // Modify and update the task in the 'Scheduled for Execution' queue
                    backgroundSyncStoreInstance.put(BackgroundSyncTasks).onsuccess = () => {
                        resolve(taskToExecute);
                    };
                } else { resolve(null) };
            }

        })
    }

    updatePendingQueue = (completed: boolean, taskInforamtion: Task) => {
        const transaction = this.dbInstance.transaction(IndexedDBCollections.pending, 'readwrite');
        const pendingStoreInstance = transaction.objectStore(IndexedDBCollections.pending);
        const response = !completed ? pendingStoreInstance.put(taskInforamtion) : pendingStoreInstance.delete(taskInforamtion.taskID);

        response.onsuccess = () => {
            return;
        }

    }

    restartPendingMediaUpload = () => {
        const transaction = this.dbInstance?.transaction(IndexedDBCollections.pending, 'readwrite');
        const pendingStoreInstance = transaction?.objectStore(IndexedDBCollections.pending);
        const cursorRequest = pendingStoreInstance?.openCursor();
        const pendingMedia: Array<Task> = [];
        if(cursorRequest?.onsuccess){
            cursorRequest.onsuccess = async () => {
                const cursor = cursorRequest.result;
                if(cursor) {
                    const pendingMediaTask: Task = cursor.value;
                    pendingMedia.push(pendingMediaTask);
                    cursor.continue();
                } else {
                    if(pendingMedia.length > 0) {
                        for(let task = 0; task < pendingMedia.length; task++) {
                            await this.moveToBackgroundTaskQueue(pendingMedia[task]);
                            this.updatePendingQueue(true, pendingMedia[task]);
                        }
                    }
                }
            }
        }
    }

}
