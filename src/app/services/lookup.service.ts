import { Injectable } from '@angular/core';
import { LookupService as LookupDataService, ArealyticsUsersService } from '../api-client';

@Injectable()
export class LookupService {
  constructor(private lookupService: LookupDataService,
    private arealyticsUserService: ArealyticsUsersService
  ) {}
 
  public getPropertyLookup() {
    return this.lookupService.getAllPropertyLookups();
  }

  public getArealyticsUsers() {
    return this.arealyticsUserService.getArealyticsUsers();
  }
}
