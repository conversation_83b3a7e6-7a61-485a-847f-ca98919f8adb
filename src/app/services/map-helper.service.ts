
import { Injectable } from '@angular/core';
import { ResearchType } from '../enumerations/researchType';
import { LatLng } from '../modules/map-module/models/LatLng';
import { UseTypes, SpecificUses } from '../enumerations/useTypes';

@Injectable()
export class MapHelperService {
  private baseUrl = 'assets/images/googleMapPins/';
  constructor() { }

  GetNeutralPinUrl() {
    return this.baseUrl + 'marker_neutral.png';
  }

  GetClickedPositionPin() {
    return this.baseUrl + 'marker_click.png';
  }

  GetPropertyPinByResearchType(researchTypeId: number, genUse: number, specificUse: number) {
    let url = this.baseUrl + 'marker_';
    switch (researchTypeId) {
      case ResearchType.NeedsResearch:
        url += 'pink.png';
        break;
      case ResearchType.BaseComplete:
        if(genUse && specificUse && genUse == UseTypes.Apartments && specificUse != SpecificUses.MixedUse){
          url += 'White.png';
        }
        else {
        url += 'yellow.png';
        }
        break;
      case ResearchType.FieldResearchComplete:
        url += 'green.png';
        break;
      case ResearchType.Hidden:
        url += 'black.png';
        break;
      case ResearchType.ExpressComplete:
        url += 'orange.png';
        break;
      case ResearchType.NotStarted:
      default:
        url += 'red.png';
        break;
    }
    return url;
  }

  GetLatLngListFromPolygon(polygonText: string): Array<LatLng> {
    let latlngList: Array<LatLng> = [];
  
    // Determine if the text is MULTIPOLYGON or POLYGON and sanitize input
    if (polygonText.startsWith('MULTIPOLYGON')) {
      polygonText = polygonText.replace(/MULTIPOLYGON\s*\(\(\(/g, '').replace(/\)\)\)/g, '');
    } else if (polygonText.startsWith('POLYGON')) {
      polygonText = polygonText.replace(/POLYGON\s*\(\(/g, '').replace(/\)\)/g, '');
    }
  
    // Split by commas to separate coordinate pairs
    polygonText.split(',').forEach(point => {
      const coords = point.trim().split(' ').map(coord => parseFloat(coord));
      if (coords.length === 2) {
        const latlng = new LatLng();
        latlng.Latitude = coords[1]; // Latitude comes second
        latlng.Longitude = coords[0]; // Longitude comes first
        latlngList.push(latlng);
      }
    });
  
    return latlngList;
  }
  
}
