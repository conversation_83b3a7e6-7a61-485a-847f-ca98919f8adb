import { Injectable } from '@angular/core';
import { FileTypes } from '../modules/aws/types/types';
import { MediaInfoDTO } from '../models/media-dto';
import { ImageUploadRequestDTO, MediaDeleteRequestDTO, MediaDTO, MediaRequestDTO, MediaService as MService} from '../api-client';

@Injectable()
export class MediaService {
  constructor(private mediaService: MService) {}

  generateUUID() {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      // tslint:disable-next-line: no-bitwise
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      // tslint:disable-next-line: no-bitwise
      return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
  }

  public mediaCheck(media, isImageOnly?: boolean): boolean {
    if (isImageOnly) {
      if (media.Ext.toLowerCase() !== FileTypes.PNG && media.Ext.toLowerCase() !== FileTypes.JPEG && media.Ext.toLowerCase() !== FileTypes.JPG) {
        return false;
      }
    }
    if (media.Ext.toLowerCase() !== FileTypes.PDF && media.Ext.toLowerCase() !== FileTypes.TEXT && media.Ext.toLowerCase() !== FileTypes.WORD
      && media.Ext.toLowerCase() !== FileTypes.DOC && media.Ext.toLowerCase() !== FileTypes.EXCEL && media.Ext.toLowerCase() !== FileTypes.CSV
      && media.Ext.toLowerCase() !== FileTypes.PNG && media.Ext.toLowerCase() !== FileTypes.JPEG && media.Ext.toLowerCase() !== FileTypes.JPG) {
      return false;
    }
  }

  public fileExtentions(media) {
    if (media.Ext.toLowerCase() === FileTypes.PDF) {
      media.URL = 'assets/images/pdf.png';
    } else if (media.Ext.toLowerCase() === FileTypes.TEXT) {
      media.URL = 'assets/images/text.png';
    } else if (media.Ext.toLowerCase() === FileTypes.WORD || media.Ext.toLowerCase() === FileTypes.DOC) {
      media.URL = 'assets/images/wordImg.png';
    } else if (media.Ext.toLowerCase() === FileTypes.EXCEL) {
      media.URL = 'assets/images/excel.png';
    } else if (media.Ext.toLowerCase() === FileTypes.CSV) {
      media.URL = 'assets/images/csv.png';
    }
  }

  public deleteMedia(media: MediaDeleteRequestDTO) {
    return this.mediaService.deleteMedia(media);
  }

  public getAllMedias(propertyId: number) {
    return this.mediaService.getMediaByRelationId(MediaDTO.MediaRelationTypeIDEnum.Property, propertyId);
  }

  public saveOrUpdateMedia(media: MediaInfoDTO, mediaId?: number, isDefault = false ) {
    const mediaRequest: MediaRequestDTO = isDefault ? {
        IsDefault: true,
        RelationID: media.RelationID,
        PropertyID: media.RelationID,
        MediaRelationTypeID: media.MediaRelationTypeID
     } : {
      Description: media.Description,
      Ext: media.Ext,
      Height: media.Height,
      Width: media.Width,
      IsDefault: media.IsDefault,
      IsOwnMedia: media.IsOwnMedia,
      MediaName: media.MediaName,
      MediaRelationTypeID: media.MediaRelationTypeID,
      MediaSourceID: media.MediaSourceID,
      MediaSubTypeID: media.MediaSubTypeID,
      MediaTypeID: media.MediaTypeID,
      Path: media.Path,
      PropertyID: media.RelationID,
      RelationID: media.RelationID,
      Size: media.Size,
      SourceComments: media.SourceComments
    }
    if (mediaId) {
      return this.mediaService.updateMedia(mediaId, mediaRequest);
    } else {
      return this.mediaService.createMedia(mediaRequest);
    }
  }

  public uploadToS3(imageUploadRequest: ImageUploadRequestDTO) {
    return this.mediaService.uploadImage(imageUploadRequest)
  }
}
