import { Injectable } from '@angular/core';
import { NotesService as NotesApiClientService, NotesRequestDTO, NotesResponseDTO } from '../api-client'

@Injectable()
export class NotesService {
  constructor(private notesService: NotesApiClientService) {}
  
  public GetNotes(parentId: number, parentTableId: NotesResponseDTO.ParentTableIdEnum) {
    return this.notesService.getNotes(parentId, parentTableId);
  }

  public SaveNote(note: NotesRequestDTO, noteId?: number) {
   if (noteId) {
    return this.notesService.updatesNotes(noteId, note);
   }
   return this.notesService.saveNotes(note);
  }

  public DeleteNote(noteId: number) {
    return this.notesService.deleteNotes(noteId);
  }
}
