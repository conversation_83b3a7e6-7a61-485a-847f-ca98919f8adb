import { Injectable } from '@angular/core';
import { PropertyService } from '../api-client/api/property.service';
import { ParcelPropertyRequestDTO } from '../api-client';

@Injectable()
export class ParcelService {
  constructor(private propertyService: PropertyService) {
  }
 
  public deleteParcel(propertyId: number, parcelID: number) {
    return this.propertyService.deleteParcel(propertyId, parcelID);
  }

  public fetchParcels(propertyId: number) {
    return this.propertyService.getParcelByProperty(propertyId);
  }

  public saveParcel(propertyId: number, parcel: ParcelPropertyRequestDTO[]) {
    return this.propertyService.createParcel(propertyId, parcel);
  }

  public updateParcel(propertyId: number, parcelId: number, parcel: ParcelPropertyRequestDTO) {
    return this.propertyService.updateParcel(propertyId, parcelId, parcel);
  }
}
