import { Injectable } from '@angular/core';
import { MetaDataIndexedDBService, IMetaData } from './indexed-db-service.service';
import { MetaDataCollectionKeys } from '../enumerations/indexeddb';
import { LookupService } from './lookup.service';
import { ApiResponseMapStringObject } from '../api-client';
import { NotificationService } from '../modules/notification/service/notification.service';
import { CommonStrings } from '../constants';

@Injectable({
  providedIn: 'root'
})
export class PropertyTrackingService {
  metaDataIndexedDBService: MetaDataIndexedDBService

  constructor( private lookupService: LookupService,
    private notificationService: NotificationService
  ) {
    this.metaDataIndexedDBService = new MetaDataIndexedDBService();
  }

  async getVisitedPropertyIds(): Promise<string[] | null> {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.VisitedPropertyIds);
      return searchData?.value ?? null;
    } catch (error) {
      console.error('Error retrieving visited properties:', error);
      return null;
    }
  }

  async getEditedPropertyIds(): Promise<string[] | null> {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.EditedPropertyIds);
      return searchData?.value ?? null;
    } catch (error) {
      console.error('Error retrieving edited properties:', error);
      return null;
    }
  }
  
  async getPropertyLookup() {
    try {
      const searchData: IMetaData | null = await this.metaDataIndexedDBService.retriveDataFromMetaData(MetaDataCollectionKeys.PropertyLookup);
      if (searchData && searchData.value) {
        return searchData.value;
      } else {
        return new Promise((resolve, reject) => {
          this.lookupService.getPropertyLookup().subscribe({
            next: (result: ApiResponseMapStringObject) => {
              if (result.error) {
                this.notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.LookupDataFailureMessage);
                reject(new Error(CommonStrings.ErrorMessages.LookupDataFailureMessage));
                return;
              }
              resolve(result.responseData || []);
            },
            error: (err) => {
              reject(err);
              this.notificationService.ShowErrorMessage(CommonStrings.ErrorMessages.LookupDataFailureMessage);
            }
          })
        })
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
      throw error;
    }
  }
}
