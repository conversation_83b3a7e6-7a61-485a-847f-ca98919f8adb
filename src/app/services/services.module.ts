import { NotesComponent } from './../pages/notes/notes.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { MapHelperService } from './map-helper.service';
import { CommunicationService } from './communication.service';
import { PropertyService } from './api-property.service';
import { LoginService } from './login.service';
import { MediaService } from './media.service';
import { SharedDataService } from './shareddata.service';
import { NotesService } from './notes.service';
import { BuildingFootPrintService } from './building-footprint.service';
import { AddressService } from './address.service';
import { PagerService } from './pager.service';
import { IndexedDBService } from './indexeddb.service';
import { ImageUploadWorkerService } from './image-upload-worker.service'
import { MetaDataIndexedDBService } from './indexed-db-service.service';
import { AddFloorService } from './add-floor.service';
import {WhatsNewService} from './whats-new-service';
import { ParcelService } from './parcel.service';
import { LookupService } from './lookup.service';
import { AuditService } from './audit.service';
import { PropertyTrackingService } from './property-tracking.service';

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
  ],
  declarations: [],
  providers: [
    MapHelperService,
    CommunicationService,
    PropertyService,
    LoginService,
    MediaService,
    NotesService,
    SharedDataService,
    BuildingFootPrintService,
    AddressService,
    PagerService,
    IndexedDBService,
    ImageUploadWorkerService,
    MetaDataIndexedDBService,
    AddFloorService,
    WhatsNewService,
    ParcelService,
    LookupService,
    AuditService,
    PropertyTrackingService
  ]
})
export class ServicesModule { }
