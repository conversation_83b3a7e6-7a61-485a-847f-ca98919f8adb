import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable()
export class SharedDataService {

    private _researchstatus:any;
    private _mapSearchPropertyList: any;
    private _propertyMedia: any;
    private _additionalAddressList: any;
    private _selectPropertyParcel: any;
    private _propertyResearchStatus: any;
    private _selectedFloor:any;
    private _postalCode: any;
    private _parcelInfo: any;
    private _deleteBuildingFootPrintIds: any[];
    private _maxZoom: number | null = null;
    private _AzureMapURL:string = environment.AzureMapBaseURL;
    private _isAzureMapOn:boolean = false;
    private _checkedList:string[] = [];
    private _currentZoomLevel:number | null = null;
    private _lookupDropdowns: any;
    constructor() {}

    // All research status.
    get researchStatusList(): any {
        return this._researchstatus;
    }
    set researchStatusList(_researchstatusList: any) {
        this._researchstatus = _researchstatusList;
    }
    get mapSearchPropertyList(): any {
        return this._mapSearchPropertyList;
    }
    set mapSearchPropertyList(property: any) {
        this._mapSearchPropertyList = property;
    }
    get propertyMedia(): any {
        return this._propertyMedia;
    }
    set propertyMedia(media: any) {
        this._propertyMedia = media;
    }
    get additionalAddressList(): any {
        return this._additionalAddressList;
    }
    set additionalAddressList(address: any) {
        this._additionalAddressList = address;
    }
    get selectedPropertyParcel(): any {
        return this._selectPropertyParcel;
    }
    set selectedPropertyParcel(parcel: any) {
        this._selectPropertyParcel = parcel;
    }
    get propertyResearchStatus(): any {
        return this._propertyResearchStatus;
    }
    set propertyResearchStatus(research: any) {
        this._propertyResearchStatus = research;
    }
    get selectedFloor(): any {
        if (!this._selectedFloor && localStorage.getItem('selectedFloor') != null && localStorage.getItem('selectedFloor') !== 'undefined') {
            this._selectedFloor = JSON.parse(localStorage.getItem('selectedFloor'));
        }
        return this._selectedFloor;
    }
    set selectedFloor(floor: any) {
        this._selectedFloor = floor;
        localStorage.setItem('selectedFloor', JSON.stringify(floor));
    }
    get searchPostalCode(): any {
        return this._postalCode;
    }
    set searchPostalCode(postalCode: any) {
        this._postalCode = postalCode
    }
    get parcelInfoPickedFromTileLayer(): any {
        return this._parcelInfo;
    }
    set parcelInfoPickedFromTileLayer(results: any) {
        this._parcelInfo = results;
    }
    get deleteBuildingFootPrintIds(): any[] {
        return this._deleteBuildingFootPrintIds ? this._deleteBuildingFootPrintIds : [];
    }
    set deleteBuildingFootPrintIds(results: any[]) {
        this._deleteBuildingFootPrintIds = results;
    }
    get maxZoom() {
        return this._maxZoom;
    }
    set maxZoom(maxZoom:number | null) {
        this._maxZoom = maxZoom;
    }
    get AzureMapURL() {
        return this._AzureMapURL;
    }
    set AzureMapURL(azureMapURL: string) {
        this._AzureMapURL = azureMapURL;
    }
    get IsAzureMapOn() {
        return this._isAzureMapOn;
    }
    set IsAzureMapOn(isOn:boolean) {
        this._isAzureMapOn = isOn;
    }
    get checkedList() {
        return this._checkedList;
    }
    set checkedList(checkedList: string[]) {
        this._checkedList = checkedList;
    }
    get currentZoomLevel() {
        return this._currentZoomLevel;
    }
    set currentZoomLevel(currentZoom:number | null) {
        this._currentZoomLevel = currentZoom;
    }
    getLookupDropdowns() {
        return this._lookupDropdowns;
    }
    setLookupDropdowns(lookup: any) {
        this._lookupDropdowns = lookup;
    }
    yesNoList = [{ID:false,Item:"No"},{ID:true,Item:"Yes"}];
}
