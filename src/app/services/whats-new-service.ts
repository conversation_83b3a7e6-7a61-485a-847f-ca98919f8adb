import { Injectable } from '@angular/core';
import { ReleaseUpdatesService } from '../api-client';
import { EnumApplication } from '../enumerations/application';

@Injectable()
export class WhatsNewService {
  constructor(private releaseUpdatesService: ReleaseUpdatesService ) {}
 
  public getReleaseUpdateNotes() {
    const applicationID = EnumApplication.VST;
    return this.releaseUpdatesService.getReleaseUpdates(applicationID);
  }
}
